allprojects {
    repositories {
        // See https://github.com/flutter/flutter/blob/master/docs/ecosystem/Plugins-and-Packages-repository-structure.md#gradle-structure for more info.
        def artifactRepoKey = 'ARTIFACT_HUB_REPOSITORY'
        if (System.getenv().contains<PERSON><PERSON>(artifactRepoKey)) {
            println "Using artifact hub"
            maven { url System.getenv(artifactRepoKey) }
        }
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
