<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="io.flutter.plugins.cameraexample">

  <application
    android:icon="@mipmap/ic_launcher"
    android:label="cameramanish">
    <activity
      android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection"
      android:hardwareAccelerated="true"
      android:exported="true"
      android:launchMode="singleTop"
      android:name="io.flutter.embedding.android.FlutterActivity"
      android:theme="@style/LaunchTheme"
      android:windowSoftInputMode="adjustResize">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
    </activity>
    <meta-data android:name="flutterEmbedding" android:value="2"/>
  </application>

  <uses-feature
    android:name="android.hardware.camera"
    android:required="true"/>

  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.FLASHLIGHT"/>
</manifest>
