// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		01CBD73509ECFF3BC31C5BB2BE9CCF4C /* FVPVideoPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 3007591DB86A70A758237A18CCB5BD86 /* FVPVideoPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		02E42A5A466B3E89BFBDC7238104BA0C /* FVPTextureBasedVideoPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 5347F9B6E3B230FA3AF1FD69E81A807D /* FVPTextureBasedVideoPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		030E9CC53C0A2C293E1316DF96C82228 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		03A083048C780CBF8BC0B604A364D23A /* CameraProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A1246DF6159FF87C9A3280C7FE30EEC /* CameraProperties.h */; settings = {ATTRIBUTES = (Public, ); }; };
		03B9D4CEBEC8F075594F2FFC32F406B3 /* video_player_avfoundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = DF72F52732914F17C688D8A19152410B /* video_player_avfoundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		049D0B5938C4EAECAAB6AEBCBF16006A /* CameraProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = 7224C775F567EAC66DA6D831B006DC7A /* CameraProperties.m */; };
		06B394B97CCE583F6882B1102013A28C /* DefaultCamera.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9A7954D281A912A3787A97AC592A539 /* DefaultCamera.swift */; };
		0C95D39F4B6E53824CC6EC13BCDDEE2D /* FVPDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = C6AA5C2B0E6D43669CE8BB64DD20AB03 /* FVPDisplayLink.h */; settings = {ATTRIBUTES = (Public, ); }; };
		12219F8E744420BD5AB117EF75F36E23 /* FVPFrameUpdater.m in Sources */ = {isa = PBXBuildFile; fileRef = 097A4AF8D026387A9BAD833BCE6938AD /* FVPFrameUpdater.m */; };
		14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */; };
		16623A4DCFEF3FBB95D9867F8C354552 /* FLTFormatUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 70387EFB62A21854FF22C870708EFA88 /* FLTFormatUtils.m */; };
		18441E89026621F065FD728737ED4CD1 /* FVPFrameUpdater.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FB93DAC4FCCE59EAADC3F590168FA80 /* FVPFrameUpdater.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1B5990F8256644B633A24CE4791B7F25 /* FLTPermissionServicing.m in Sources */ = {isa = PBXBuildFile; fileRef = D2548E7528F34E83FB32A976652EDE29 /* FLTPermissionServicing.m */; };
		1BCE1839A3F7F738AD9BB2A248BC513B /* FVPVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A3E7B1AAB2A147C69421D4B26BB4C68 /* FVPVideoPlayer.m */; };
		1FD558625959E27D7AC35564029736D0 /* FVPTextureBasedVideoPlayer_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 26C2E100FCDC9334591E88A3078B4382 /* FVPTextureBasedVideoPlayer_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2079EA2588F437AE5B5DBF480639838B /* FVPViewProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 9FCF1CF4ECD8EAA8B20BDFAD0E9B9E6E /* FVPViewProvider.m */; };
		21BD83C33CA9734F112FDAA9C4E42D9C /* FVPEventBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 042F7E29351E9FA529D492C2B156CB8F /* FVPEventBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A656D252EE8A33851AE3F75639E7FEAF /* path_provider_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2695AA2D706BABA00D80D7AAF0BAAFE0 /* FLTCameraPermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = C937C0FFB39216E54DD5C3AEFDA9AAB8 /* FLTCameraPermissionManager.m */; };
		26EA0D20C737C77BAAB5AAA54543EA9C /* FLTWritableData.m in Sources */ = {isa = PBXBuildFile; fileRef = BD8FA65FFA2C3CA33860D2D0CF43612D /* FLTWritableData.m */; };
		2B32705F11377CAC6C8678C341103E53 /* FLTAssetWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AC3857A2A8AB25C6628A56CD817E071 /* FLTAssetWriter.m */; };
		2BA8F29AF420369EA6922201C5A60DDB /* FVPNativeVideoView.h in Headers */ = {isa = PBXBuildFile; fileRef = A460A1FDEA543FAF251AD9891CA44B6D /* FVPNativeVideoView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		306A8154EC3A848B1E8C2C8CCF4C4370 /* FLTCameraPermissionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = A6051D22CDF80C16A95D4C0CBEC88D09 /* FLTCameraPermissionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		340B843A2A0B0F262093208609DFB359 /* FLTCaptureDeviceFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C69386B4A3C728668E3068E9AEDD4CA /* FLTCaptureDeviceFormat.m */; };
		351AC965BF44984D6E225ED347912561 /* Camera.swift in Sources */ = {isa = PBXBuildFile; fileRef = E95C3E491A5C346776B183FE8832E4A5 /* Camera.swift */; };
		36D53000D376A354E6DF0DB75906EA79 /* IntegrationTestIosTest.m in Sources */ = {isa = PBXBuildFile; fileRef = DEE2F5B090C5D6DDA1DD6562CE4AAC4C /* IntegrationTestIosTest.m */; };
		3949E86B1BE43619C0F93DEDE78266F5 /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */; };
		3B7EAE9E43B19CA3CAE87688F63DD31E /* FVPAVFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E7ED98A01C4395E65858967A177056D /* FVPAVFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C8E01595CF33B68A637AD93FA4978CF /* camera_avfoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = 90AEA062F2CB5C158052BD5BD0461BE0 /* camera_avfoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3DCDD529F97405FF13C0BEBD2B6D956A /* QueueUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 546880632FDD3A3FE6465329DEE94FB5 /* QueueUtils.m */; };
		409E4C13C57C5485ADD48990495351C2 /* FLTCapturePhotoOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C97A49FAD6F207F51BD7E4AC970BEB7 /* FLTCapturePhotoOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4392CBA4D2AD75B69AC6AE946B7D04A6 /* FLTCameraDeviceDiscovering.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B7DAFD02453D26F26330A9AD3543433 /* FLTCameraDeviceDiscovering.m */; };
		452D8EE840CEFF02AD1C0FE930BBC5C9 /* FVPVideoPlayer_Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6D4683162ABDFF11BFD78D50F790BCA2 /* FVPVideoPlayer_Internal.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4723E47C3A13863E84C6F94BEDF473BE /* SwiftImageGallerySaverPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5346383D8B1E71918D865AE34A3B24E /* SwiftImageGallerySaverPlugin.swift */; };
		480B16C831F07F86A754E83AC08624BA /* FLTDeviceOrientationProviding.h in Headers */ = {isa = PBXBuildFile; fileRef = 99ABDC44B62C1A6E96DE6600EEB2F8D7 /* FLTDeviceOrientationProviding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4B597D76EB7705A3031CDA82C8BCF7D8 /* FLTCaptureDeviceFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 815F110165A5347FF20D9F1992E01D89 /* FLTCaptureDeviceFormat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5483203B538ADCA16734DF0775985CB8 /* FLTImageStreamHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D012600E7F75D2E7042ABD59F0BA783 /* FLTImageStreamHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		54C1A725399979F0383179618D742D2D /* IntegrationTestIosTest.h in Headers */ = {isa = PBXBuildFile; fileRef = 1606E2A6395A3668B09FAEF0C3B7A985 /* IntegrationTestIosTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		57D55E0DEE5B778AAB39E4D7C589388D /* FLTEventChannel.h in Headers */ = {isa = PBXBuildFile; fileRef = 53F96E8B0935B74DE080CBFFD68DA7E6 /* FLTEventChannel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5817432C68EA5EF661E8A907428B0E18 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5BE6CF81A9A9549249986607F3F790C7 /* PrivacyInfo.xcprivacy */; };
		5A7F034D4B282F6A1CBB4D59A983B252 /* FVPTextureBasedVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 783C9124A0D070B0D839131B2817F6A7 /* FVPTextureBasedVideoPlayer.m */; };
		620F968F698B50F0E4084992CDC22F08 /* FVPVideoPlayerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 751A6559064247F9F84F7FA083862422 /* FVPVideoPlayerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6536FF32D002043A333C3EB64B3DCA89 /* video_player_avfoundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 214C4B9B9D41B451D3379497D1B33861 /* video_player_avfoundation-dummy.m */; };
		6975110905151D5F3245A0E9C98E4700 /* FLTCaptureSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 7ACD42F9C402041BE10F2A93E4D83783 /* FLTCaptureSession.m */; };
		70D3654AA3300267D29EC5F48B6FC560 /* ImageGallerySaverPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CE2766C4E5E5DA23C524C5818663B17 /* ImageGallerySaverPlugin.m */; };
		71366A5094E48AD1F2D29BA3D94FFAB9 /* FVPCADisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 908395F656128487B48C9AB4444FAE9B /* FVPCADisplayLink.m */; };
		73427689B342C9A90D275E8BBC38E498 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC4847692B9CC6B515FB9315E4670058 /* messages.g.swift */; };
		74CA127F589CFDE980529C1FD2DDE7D2 /* FLTSavePhotoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 0F3EBFEF5C5EC4F061B0E0CF06B15C9D /* FLTSavePhotoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7B080D6C6F61953AE66DF6A261D84F92 /* FVPVideoEventListener.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BA40B0F5F6F47991AE8EC1A1AE299F /* FVPVideoEventListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D629EF41C6F8998A19AE82A76EC5AE4 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		7DAB0FECB69FA62231B48023413B13F3 /* FVPAVFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 4B328BCA07BAB5B77D53BDA4739C7481 /* FVPAVFactory.m */; };
		7F351626BE5B7CA9303C04C0A8F25031 /* camera_avfoundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 010642736AA7A5D219D25D07A1AB184C /* camera_avfoundation-dummy.m */; };
		813C6AEB824110C60B8D98B7FA221B70 /* CameraPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED5E4A58FC675653EAB90B93DCDB95B0 /* CameraPlugin.swift */; };
		82D9E3FE965E3414C2DD3AE8BF1C2BD2 /* messages.g.m in Sources */ = {isa = PBXBuildFile; fileRef = A037ABC3E991AD8BDAD565AF8EE9FFC5 /* messages.g.m */; };
		83FEF39BCEA5BAF40E139751B32CF026 /* FLTPermissionServicing.h in Headers */ = {isa = PBXBuildFile; fileRef = 94A7140A34B652D1E52004EF912C682B /* FLTPermissionServicing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		84004623E384C5AA7874121C30C962DE /* messages.g.h in Headers */ = {isa = PBXBuildFile; fileRef = 8039219AB7277FCEC407EC487E453E2D /* messages.g.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8592D3BA6B16769F721129D5D5B4B671 /* FLTWritableData.h in Headers */ = {isa = PBXBuildFile; fileRef = FC37902FB86E31D82689ACDF156B222E /* FLTWritableData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		869631B36F8C61EB5425E582721D4D20 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = CC038C6A6C53B34D65D0CE192F4D055B /* PrivacyInfo.xcprivacy */; };
		875030AD692222B0DF5BD9C991C32896 /* QueueUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24438D3FB467F1BB768EDF4EA3238A1C /* QueueUtils.swift */; };
		875A2A0D4875DA41E3223F8EE8206152 /* PathProviderPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F466989D1C26965A9466A7E75C28ECE /* PathProviderPlugin.swift */; };
		882656F1F7EDA5B1D1D29D160534C190 /* image_gallery_saver-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3AA4A4BD8EE526337B7AE03CC6DF728D /* image_gallery_saver-dummy.m */; };
		898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2FD708D969ABBDFD56A21C2AA0EB227D /* integration_test-dummy.m */; };
		8BBF4F70DC31CCA3238BFE7CA43F5545 /* messages.g.m in Sources */ = {isa = PBXBuildFile; fileRef = CCEBD973F9CD153BF9AF31B801CC9ADA /* messages.g.m */; };
		8C3070272E95A2C4984F2E562E0671C8 /* FLTCaptureVideoDataOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = F64A70734A55167F6AC526724BECA617 /* FLTCaptureVideoDataOutput.m */; };
		8FFEDA1B4F9133249B33B9C706E41CCB /* FLTImageStreamHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 1127A62E38665719680D686282496887 /* FLTImageStreamHandler.m */; };
		93F3B9616434A0926600C441C08115E2 /* FLTCaptureDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 7425FF1330AD7A796E167B0218256B29 /* FLTCaptureDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9957A3C6D1C945E94E4CD46871081DB5 /* FLTCaptureDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 208FBA9E7E9E723D9CE4CFAD26B4BBFD /* FLTCaptureDevice.m */; };
		9B9717221A43BD8DB1BFF7673FE8E871 /* camera_avfoundation-camera_avfoundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */; };
		9C748B0EF7DC986CB78AEEF8D52616D4 /* FVPViewProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = AD41C0E16A6A7F377C3D7BF20189265E /* FVPViewProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A3222ABF19FC9866D221A287F9BFEBC9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		A4CD59CF4F0FE09EEC1E982272B21FD9 /* FVPNativeVideoViewFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = DF09DBA551692F6A77FBBDD814CF4919 /* FVPNativeVideoViewFactory.m */; };
		AC6A99F7C15692400B388F737EA304CC /* FVPVideoPlayerPlugin_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = B6231A1FC7651F5C02A51BBD561B97C4 /* FVPVideoPlayerPlugin_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AFA8A38AC1D494ACE56DE9C7B363AE53 /* FLTCaptureConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A8A6DF427AB82D7AAB8A5D8BB5671B5 /* FLTCaptureConnection.m */; };
		B16C4147A69E0088076A80A73277F4DA /* FLTCamMediaSettingsAVWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E01ADF02C45990C84161F36CA350671 /* FLTCamMediaSettingsAVWrapper.m */; };
		B6E7879E6847184C425BA5E681E1370F /* FLTCaptureOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = E4C0AD57E34570BCBA58ACF21B73AEC2 /* FLTCaptureOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B80EAC8BF0B980336C6E7ED3E0D4E0DB /* FVPNativeVideoViewFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = A6F640F32DA593A07ED25DF9741E38A1 /* FVPNativeVideoViewFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B899DDDB5C10C59A638E73C4F30C2E8E /* FVPNativeVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 692FD6B5D244355A91AEA08351791DF6 /* FVPNativeVideoView.m */; };
		B8B43B2E145F8850F52EA02CDAAFFAED /* FLTCamConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = BB35B717489DAF3E4421389A7F7D057F /* FLTCamConfiguration.m */; };
		B9BB08B99B2081914777477B372F8263 /* FLTCaptureConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = BF1B50AA0BC139719322C05FF10A7435 /* FLTCaptureConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B9F642F64F494665E7B60843F0EEDBC1 /* FLTCamConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 57EC7093C265553F3F96104D34FC8069 /* FLTCamConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BB85A52D2EB5F8E18E58B2A2A0AA30C6 /* FLTSavePhotoDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9D29ED4D7E9F66FF7291962F621D51BA /* FLTSavePhotoDelegate.m */; };
		BBB6C6DB17FF457BACD35352E23728AB /* FLTCameraDeviceDiscovering.h in Headers */ = {isa = PBXBuildFile; fileRef = 3BBEBF85E9C9931DA4FE36701B34741E /* FLTCameraDeviceDiscovering.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BEB688201E1B7583B9B281D8625ECF15 /* FLTThreadSafeEventChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 44579597553404BC2637060600B4798B /* FLTThreadSafeEventChannel.m */; };
		BEF37003673BC8C426D6A87DB7C4065C /* IntegrationTestPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CD76F83AD70324BC04BF2474EF468E2 /* IntegrationTestPlugin.m */; };
		BF7FB6F8D1350C2ED36FAB8B83B9EA17 /* QueueUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 4A552816171BA37AB916D83F2CD12C6D /* QueueUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C0A7D5B0D751A48FD51D23AD682BAA95 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		C1893AFEE2F741B3177B07FF3FFB7A8A /* AVAssetTrackUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = B6005A10AE569542CBD43D20ADA57ED6 /* AVAssetTrackUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C26516EDCD883DF759ED69F4D5637326 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 441FF6270AA3B2B9271D8FB869EA69E4 /* PrivacyInfo.xcprivacy */; };
		C405BC028C4CBAAA7ACE50D3D33B1F6B /* FVPEventBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = DF62AC4EAA87E5D44EAFB68759436506 /* FVPEventBridge.m */; };
		C7CAFABA0A77A333BE575FC1A22B1E0D /* video_player_avfoundation-video_player_avfoundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */; };
		C8AE936F1BB6B30D309F27CE84CC8CBD /* FLTFormatUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 1F0F2A51B57BD5515ABC836B35523EE7 /* FLTFormatUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C964E35A6D72ED7ECEBA91E607A828F7 /* FLTCam.m in Sources */ = {isa = PBXBuildFile; fileRef = 44967DCA0041C77AB6811360BAA2B2BD /* FLTCam.m */; };
		CB2EB908281BFF841551013B778FAF4A /* IntegrationTestPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 376FF978AF671AE59567D0BE167A853A /* IntegrationTestPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CBD57765A1D4F65E34B0A6840CD82007 /* messages.g.h in Headers */ = {isa = PBXBuildFile; fileRef = F835B0C601031BF5ED749DD9C8AFBDEC /* messages.g.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */; };
		D004FC2462D5BDD5EB9C5CA668AEFA42 /* FLTCaptureSession.h in Headers */ = {isa = PBXBuildFile; fileRef = 97809657A1A0A75CE00277EAD6888424 /* FLTCaptureSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D1446FD083744FBAB9E078E5A8EB38B9 /* AVAssetTrackUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D78074A79998639052B973A3742A802 /* AVAssetTrackUtils.m */; };
		D1508918E22EBF4D9DAA6ACF9DDDE092 /* FLTCapturePhotoOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = D206DEF61A2CDF09843879D9351DB288 /* FLTCapturePhotoOutput.m */; };
		D228C2C1BCE91E70A77EB0A0A5471759 /* FLTCam_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 5028B090B48794F6595EDF78741F41E7 /* FLTCam_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D26EA3617436F7A1604DA5B8006FB30D /* FLTSavePhotoDelegate_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = F9C5AFC7CCF17CB09B5527C27AEE3D04 /* FLTSavePhotoDelegate_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 493D0C661842ACDBCF9EF988F815487F /* integration_test-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D806DB15B8D531788D1BD8227BA481B7 /* FLTCaptureVideoDataOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = BACB25B643737223BFB8869D04C59E2A /* FLTCaptureVideoDataOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D89384702678F7189703452E6543BCE9 /* FLTDeviceOrientationProviding.m in Sources */ = {isa = PBXBuildFile; fileRef = 6EA5ACBC0485FFF7CC1219C4E2C1CE6E /* FLTDeviceOrientationProviding.m */; };
		D90221E29BE7FABD5A6260A677EF217C /* FLTIntegrationTestRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E280F8FEC4490389C2AECB33DA3BFF3 /* FLTIntegrationTestRunner.m */; };
		E240BE0B8D01F2875E4D9613A26BD33D /* FLTIntegrationTestRunner.h in Headers */ = {isa = PBXBuildFile; fileRef = CAF69C510652FB16CBD14EA635BBD0BF /* FLTIntegrationTestRunner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E72011D855D5B3288771B3D0403FF69C /* camera_avfoundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 24F8B3A934D3AC8373D02158B05AEEA5 /* camera_avfoundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 78EB4A3A81F2568C65C5CD92E1B6902A /* path_provider_foundation-dummy.m */; };
		E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		E9790B1F7E77B14A0E3A4219B364626F /* FLTThreadSafeEventChannel.h in Headers */ = {isa = PBXBuildFile; fileRef = 80ACD6D2053C602FBE737C099F503FDE /* FLTThreadSafeEventChannel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E9B20BD2AEC00D6E6403104891B3BD58 /* FVPVideoPlayerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = C0664FD26AA9A759556248B8C063580E /* FVPVideoPlayerPlugin.m */; };
		EF4733E951BBE732D3315A2B698CB951 /* FLTCamMediaSettingsAVWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = B59DC7BEDC8BC0D3C57F60C5BB90E233 /* FLTCamMediaSettingsAVWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F43266D8FAE1091DB24817B71D8BDEA9 /* image_gallery_saver-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9F6AEA83843221B953AA23ED567E961B /* image_gallery_saver-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F4BFCDB723DF34139FACCAC17F8BD259 /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8DDDFED04D4D9FC484571373E143869 /* ImageGallerySaverPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = F50B23F86D98ED1B90A057DDDF120C6E /* ImageGallerySaverPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FC51F2ABDBE1E8E1782DD958A1541611 /* FLTCam.h in Headers */ = {isa = PBXBuildFile; fileRef = CFE8A3216265935B7E76A2ACE23D90C5 /* FLTCam.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FFCA2EDDBD48826E1468E93BFAB04C81 /* FLTAssetWriter.h in Headers */ = {isa = PBXBuildFile; fileRef = D38B7F99A5B6682CEA29E08A0D0E2DF7 /* FLTAssetWriter.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		08F4CDB79E582A555C5C45CD4677C11E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D9677C5D523C2FD0AE2B9C4A7BDA913F;
			remoteInfo = "camera_avfoundation-camera_avfoundation_privacy";
		};
		128BEF0D22B75C17245D38E465FF8F1E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		1DE2ED6987F5E85B3D3804E670962991 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		21A0432F8FB94D31224B8812E2450770 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		2E754F62021980EAB3B7663AE809B092 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0B1B7EBF63BB39238A152C7553F0A9FD;
			remoteInfo = camera_avfoundation;
		};
		3D8711CDAAF20DAE8C1E4D8A9A1EA8E7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CACE6618E7996464E38687E13F67D945;
			remoteInfo = "path_provider_foundation-path_provider_foundation_privacy";
		};
		596F09724DE84B9DE634687494D95869 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		5DAE7D626BF74A129A22541320BD19A2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		8061D6BE20BF222C6AAA186C516DFA71 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 37C246C968D3D905C8DE91562C70C1A2;
			remoteInfo = image_gallery_saver;
		};
		93D6797264E3A2BA204043DE4530B0B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AAA98FC9182FB27CC0DC22AD1316E0BC;
			remoteInfo = video_player_avfoundation;
		};
		A3FFA79B4A88E42982DD34BEF88866B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56F581DDCB0A032454E604885E17AE3C;
			remoteInfo = path_provider_foundation;
		};
		A961AD5433FD66C9F4DBBC964C29F0A3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 31E6C7337BFA5AF5C3B52779DC662202;
			remoteInfo = "video_player_avfoundation-video_player_avfoundation_privacy";
		};
		E089E0011456E943E24F90455600C733 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		FC3A2F95835FB84F020634EFACBE2BD3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = ADE86197C9EC7D5D7AB212E24AE13395;
			remoteInfo = integration_test;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		010642736AA7A5D219D25D07A1AB184C /* camera_avfoundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "camera_avfoundation-dummy.m"; sourceTree = "<group>"; };
		042F7E29351E9FA529D492C2B156CB8F /* FVPEventBridge.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPEventBridge.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPEventBridge.h"; sourceTree = "<group>"; };
		097A4AF8D026387A9BAD833BCE6938AD /* FVPFrameUpdater.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPFrameUpdater.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m"; sourceTree = "<group>"; };
		0AC3857A2A8AB25C6628A56CD817E071 /* FLTAssetWriter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTAssetWriter.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTAssetWriter.m"; sourceTree = "<group>"; };
		0C1C64A8C9B38E7D5EEB05F4A4237B10 /* camera_avfoundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = camera_avfoundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		0F3EBFEF5C5EC4F061B0E0CF06B15C9D /* FLTSavePhotoDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTSavePhotoDelegate.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate.h"; sourceTree = "<group>"; };
		0FFFE8A406E3A5EB5F00EC63499D9E17 /* camera_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "camera_avfoundation-Info.plist"; sourceTree = "<group>"; };
		1127A62E38665719680D686282496887 /* FLTImageStreamHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImageStreamHandler.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTImageStreamHandler.m"; sourceTree = "<group>"; };
		1606E2A6395A3668B09FAEF0C3B7A985 /* IntegrationTestIosTest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestIosTest.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h; sourceTree = "<group>"; };
		161B707AEEF2E0518E6EF901215F4227 /* video_player_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "video_player_avfoundation-Info.plist"; sourceTree = "<group>"; };
		1D78074A79998639052B973A3742A802 /* AVAssetTrackUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AVAssetTrackUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m"; sourceTree = "<group>"; };
		1E01ADF02C45990C84161F36CA350671 /* FLTCamMediaSettingsAVWrapper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCamMediaSettingsAVWrapper.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamMediaSettingsAVWrapper.m"; sourceTree = "<group>"; };
		1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = camera_avfoundation.release.xcconfig; sourceTree = "<group>"; };
		1F0F2A51B57BD5515ABC836B35523EE7 /* FLTFormatUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTFormatUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTFormatUtils.h"; sourceTree = "<group>"; };
		1F3A3343D0A2FF744BA013B182B27DB1 /* path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		208FBA9E7E9E723D9CE4CFAD26B4BBFD /* FLTCaptureDevice.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureDevice.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDevice.m"; sourceTree = "<group>"; };
		214C4B9B9D41B451D3379497D1B33861 /* video_player_avfoundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "video_player_avfoundation-dummy.m"; sourceTree = "<group>"; };
		24438D3FB467F1BB768EDF4EA3238A1C /* QueueUtils.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QueueUtils.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.swift"; sourceTree = "<group>"; };
		24F8B3A934D3AC8373D02158B05AEEA5 /* camera_avfoundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "camera_avfoundation-umbrella.h"; sourceTree = "<group>"; };
		26C2E100FCDC9334591E88A3078B4382 /* FVPTextureBasedVideoPlayer_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPTextureBasedVideoPlayer_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer_Test.h"; sourceTree = "<group>"; };
		28D3D64A654C934144934C75D11BCF6A /* image_gallery_saver.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = image_gallery_saver.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/image_gallery_saver.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		2A3E7B1AAB2A147C69421D4B26BB4C68 /* FVPVideoPlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPVideoPlayer.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m"; sourceTree = "<group>"; };
		2CABD4E331501D4EF054E0CA8D0C592E /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE"; sourceTree = "<group>"; };
		2CD76F83AD70324BC04BF2474EF468E2 /* IntegrationTestPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestPlugin.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m; sourceTree = "<group>"; };
		2E7ED98A01C4395E65858967A177056D /* FVPAVFactory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPAVFactory.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h"; sourceTree = "<group>"; };
		2FD708D969ABBDFD56A21C2AA0EB227D /* integration_test-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "integration_test-dummy.m"; sourceTree = "<group>"; };
		3007591DB86A70A758237A18CCB5BD86 /* FVPVideoPlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayer.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h"; sourceTree = "<group>"; };
		317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "video_player_avfoundation-video_player_avfoundation_privacy"; path = video_player_avfoundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		357CF9D7973CA325A3359A17B02945C5 /* ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist"; sourceTree = "<group>"; };
		376FF978AF671AE59567D0BE167A853A /* IntegrationTestPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestPlugin.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h; sourceTree = "<group>"; };
		3AA4A4BD8EE526337B7AE03CC6DF728D /* image_gallery_saver-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "image_gallery_saver-dummy.m"; sourceTree = "<group>"; };
		3BBEBF85E9C9931DA4FE36701B34741E /* FLTCameraDeviceDiscovering.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCameraDeviceDiscovering.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraDeviceDiscovering.h"; sourceTree = "<group>"; };
		3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "path_provider_foundation-path_provider_foundation_privacy"; path = path_provider_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		3E280F8FEC4490389C2AECB33DA3BFF3 /* FLTIntegrationTestRunner.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTIntegrationTestRunner.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m; sourceTree = "<group>"; };
		441FF6270AA3B2B9271D8FB869EA69E4 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		44579597553404BC2637060600B4798B /* FLTThreadSafeEventChannel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTThreadSafeEventChannel.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTThreadSafeEventChannel.m"; sourceTree = "<group>"; };
		44967DCA0041C77AB6811360BAA2B2BD /* FLTCam.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCam.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCam.m"; sourceTree = "<group>"; };
		4584D3A74B4FA13CA70F9AA93B38A384 /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = image_gallery_saver; path = image_gallery_saver.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		493D0C661842ACDBCF9EF988F815487F /* integration_test-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-umbrella.h"; sourceTree = "<group>"; };
		4A4D815B348C1FD1A09292CFE4E1D416 /* video_player_avfoundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = video_player_avfoundation.modulemap; sourceTree = "<group>"; };
		4A552816171BA37AB916D83F2CD12C6D /* QueueUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QueueUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/QueueUtils.h"; sourceTree = "<group>"; };
		4B328BCA07BAB5B77D53BDA4739C7481 /* FVPAVFactory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPAVFactory.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m"; sourceTree = "<group>"; };
		4C09BE5BAEEEFB61F2391B80EDD16474 /* image_gallery_saver-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "image_gallery_saver-Info.plist"; sourceTree = "<group>"; };
		4FA60FD1EE2D268521905E7104813844 /* integration_test.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.debug.xcconfig; sourceTree = "<group>"; };
		5028B090B48794F6595EDF78741F41E7 /* FLTCam_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCam_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam_Test.h"; sourceTree = "<group>"; };
		51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		5347F9B6E3B230FA3AF1FD69E81A807D /* FVPTextureBasedVideoPlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPTextureBasedVideoPlayer.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer.h"; sourceTree = "<group>"; };
		53F96E8B0935B74DE080CBFFD68DA7E6 /* FLTEventChannel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTEventChannel.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTEventChannel.h"; sourceTree = "<group>"; };
		546880632FDD3A3FE6465329DEE94FB5 /* QueueUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QueueUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/QueueUtils.m"; sourceTree = "<group>"; };
		55BEA53412DBC31AE46FFC29C73120C9 /* video_player_avfoundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "video_player_avfoundation-prefix.pch"; sourceTree = "<group>"; };
		572856B2892D765D43A9C1B8ABF1F202 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/LICENSE"; sourceTree = "<group>"; };
		57EC7093C265553F3F96104D34FC8069 /* FLTCamConfiguration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCamConfiguration.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamConfiguration.h"; sourceTree = "<group>"; };
		590EEB5031AA52D68AAB3E757570DD8C /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		5991603B4BC1CA8574431B64361E4A28 /* image_gallery_saver-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "image_gallery_saver-prefix.pch"; sourceTree = "<group>"; };
		599AD37E15BF07EEC6A453E94E297F34 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE"; sourceTree = "<group>"; };
		5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = integration_test; path = integration_test.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5BE6CF81A9A9549249986607F3F790C7 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		5C97A49FAD6F207F51BD7E4AC970BEB7 /* FLTCapturePhotoOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCapturePhotoOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCapturePhotoOutput.h"; sourceTree = "<group>"; };
		5D012600E7F75D2E7042ABD59F0BA783 /* FLTImageStreamHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImageStreamHandler.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTImageStreamHandler.h"; sourceTree = "<group>"; };
		657CC89D9B48DA60004D6D5270FBA32A /* path_provider_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = path_provider_foundation.modulemap; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		692FD6B5D244355A91AEA08351791DF6 /* FVPNativeVideoView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPNativeVideoView.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoView.m"; sourceTree = "<group>"; };
		6A8A6DF427AB82D7AAB8A5D8BB5671B5 /* FLTCaptureConnection.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureConnection.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureConnection.m"; sourceTree = "<group>"; };
		6D4683162ABDFF11BFD78D50F790BCA2 /* FVPVideoPlayer_Internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayer_Internal.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Internal.h"; sourceTree = "<group>"; };
		6E532366679B0BE20CE5FDB3A23F86F2 /* path_provider_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.debug.xcconfig; sourceTree = "<group>"; };
		6EA5ACBC0485FFF7CC1219C4E2C1CE6E /* FLTDeviceOrientationProviding.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTDeviceOrientationProviding.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTDeviceOrientationProviding.m"; sourceTree = "<group>"; };
		70387EFB62A21854FF22C870708EFA88 /* FLTFormatUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTFormatUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTFormatUtils.m"; sourceTree = "<group>"; };
		716F1AC6F57B6C398E4C01AC9C5D815B /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/LICENSE"; sourceTree = "<group>"; };
		71BA40B0F5F6F47991AE8EC1A1AE299F /* FVPVideoEventListener.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoEventListener.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoEventListener.h"; sourceTree = "<group>"; };
		7224C775F567EAC66DA6D831B006DC7A /* CameraProperties.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CameraProperties.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/CameraProperties.m"; sourceTree = "<group>"; };
		7425FF1330AD7A796E167B0218256B29 /* FLTCaptureDevice.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureDevice.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDevice.h"; sourceTree = "<group>"; };
		751A6559064247F9F84F7FA083862422 /* FVPVideoPlayerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayerPlugin.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h"; sourceTree = "<group>"; };
		783C9124A0D070B0D839131B2817F6A7 /* FVPTextureBasedVideoPlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPTextureBasedVideoPlayer.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPTextureBasedVideoPlayer.m"; sourceTree = "<group>"; };
		78EB4A3A81F2568C65C5CD92E1B6902A /* path_provider_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "path_provider_foundation-dummy.m"; sourceTree = "<group>"; };
		79AF209439A6073BB2DAD9C81E658B61 /* video_player_avfoundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = video_player_avfoundation.debug.xcconfig; sourceTree = "<group>"; };
		7A1246DF6159FF87C9A3280C7FE30EEC /* CameraProperties.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CameraProperties.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/CameraProperties.h"; sourceTree = "<group>"; };
		7ACD42F9C402041BE10F2A93E4D83783 /* FLTCaptureSession.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureSession.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureSession.m"; sourceTree = "<group>"; };
		7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		7C69386B4A3C728668E3068E9AEDD4CA /* FLTCaptureDeviceFormat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureDeviceFormat.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDeviceFormat.m"; sourceTree = "<group>"; };
		7CE2766C4E5E5DA23C524C5818663B17 /* ImageGallerySaverPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ImageGallerySaverPlugin.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.m"; sourceTree = "<group>"; };
		7F466989D1C26965A9466A7E75C28ECE /* PathProviderPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PathProviderPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift"; sourceTree = "<group>"; };
		8039219AB7277FCEC407EC487E453E2D /* messages.g.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = messages.g.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/messages.g.h"; sourceTree = "<group>"; };
		80ACD6D2053C602FBE737C099F503FDE /* FLTThreadSafeEventChannel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTThreadSafeEventChannel.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTThreadSafeEventChannel.h"; sourceTree = "<group>"; };
		815F110165A5347FF20D9F1992E01D89 /* FLTCaptureDeviceFormat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureDeviceFormat.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDeviceFormat.h"; sourceTree = "<group>"; };
		8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		8D3DF8C698976AC3F6BB132B3BA96C3E /* camera_avfoundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = camera_avfoundation.debug.xcconfig; sourceTree = "<group>"; };
		8FB93DAC4FCCE59EAADC3F590168FA80 /* FVPFrameUpdater.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPFrameUpdater.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h"; sourceTree = "<group>"; };
		9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.release.xcconfig; sourceTree = "<group>"; };
		908395F656128487B48C9AB4444FAE9B /* FVPCADisplayLink.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPCADisplayLink.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPCADisplayLink.m"; sourceTree = "<group>"; };
		90AEA062F2CB5C158052BD5BD0461BE0 /* camera_avfoundation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = camera_avfoundation.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/camera_avfoundation.h"; sourceTree = "<group>"; };
		94A7140A34B652D1E52004EF912C682B /* FLTPermissionServicing.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTPermissionServicing.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTPermissionServicing.h"; sourceTree = "<group>"; };
		96929D15A9E9CCC603BF3911B78707AA /* integration_test.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.release.xcconfig; sourceTree = "<group>"; };
		96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		97809657A1A0A75CE00277EAD6888424 /* FLTCaptureSession.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureSession.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureSession.h"; sourceTree = "<group>"; };
		99ABDC44B62C1A6E96DE6600EEB2F8D7 /* FLTDeviceOrientationProviding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTDeviceOrientationProviding.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTDeviceOrientationProviding.h"; sourceTree = "<group>"; };
		9B7DAFD02453D26F26330A9AD3543433 /* FLTCameraDeviceDiscovering.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCameraDeviceDiscovering.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraDeviceDiscovering.m"; sourceTree = "<group>"; };
		9D29ED4D7E9F66FF7291962F621D51BA /* FLTSavePhotoDelegate.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTSavePhotoDelegate.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTSavePhotoDelegate.m"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9F6AEA83843221B953AA23ED567E961B /* image_gallery_saver-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "image_gallery_saver-umbrella.h"; sourceTree = "<group>"; };
		9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		9FCF1CF4ECD8EAA8B20BDFAD0E9B9E6E /* FVPViewProvider.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPViewProvider.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPViewProvider.m"; sourceTree = "<group>"; };
		A037ABC3E991AD8BDAD565AF8EE9FFC5 /* messages.g.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = messages.g.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m"; sourceTree = "<group>"; };
		A1FC51BCD74D993A9BFD720D790DAF2F /* path_provider_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = path_provider_foundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A460A1FDEA543FAF251AD9891CA44B6D /* FVPNativeVideoView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPNativeVideoView.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoView.h"; sourceTree = "<group>"; };
		A6051D22CDF80C16A95D4C0CBEC88D09 /* FLTCameraPermissionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCameraPermissionManager.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraPermissionManager.h"; sourceTree = "<group>"; };
		A656D252EE8A33851AE3F75639E7FEAF /* path_provider_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-umbrella.h"; sourceTree = "<group>"; };
		A6F640F32DA593A07ED25DF9741E38A1 /* FVPNativeVideoViewFactory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPNativeVideoViewFactory.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoViewFactory.h"; sourceTree = "<group>"; };
		A9A7954D281A912A3787A97AC592A539 /* DefaultCamera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DefaultCamera.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/DefaultCamera.swift"; sourceTree = "<group>"; };
		AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = camera_avfoundation; path = camera_avfoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AD41C0E16A6A7F377C3D7BF20189265E /* FVPViewProvider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPViewProvider.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPViewProvider.h"; sourceTree = "<group>"; };
		AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = path_provider_foundation; path = path_provider_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AE8A5DDAEB60AA65BC8BAD5A0E83EAAF /* image_gallery_saver.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_gallery_saver.debug.xcconfig; sourceTree = "<group>"; };
		AF1FE46AEE03ED1E5E8F1994D6720EA9 /* integration_test-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-prefix.pch"; sourceTree = "<group>"; };
		B59DC7BEDC8BC0D3C57F60C5BB90E233 /* FLTCamMediaSettingsAVWrapper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCamMediaSettingsAVWrapper.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h"; sourceTree = "<group>"; };
		B6005A10AE569542CBD43D20ADA57ED6 /* AVAssetTrackUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AVAssetTrackUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h"; sourceTree = "<group>"; };
		B6231A1FC7651F5C02A51BBD561B97C4 /* FVPVideoPlayerPlugin_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayerPlugin_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h"; sourceTree = "<group>"; };
		B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = video_player_avfoundation.release.xcconfig; sourceTree = "<group>"; };
		BACB25B643737223BFB8869D04C59E2A /* FLTCaptureVideoDataOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureVideoDataOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureVideoDataOutput.h"; sourceTree = "<group>"; };
		BB35B717489DAF3E4421389A7F7D057F /* FLTCamConfiguration.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCamConfiguration.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamConfiguration.m"; sourceTree = "<group>"; };
		BB99F5920096C31629F014277ED50DE3 /* integration_test.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = integration_test.podspec; path = ../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		BD8FA65FFA2C3CA33860D2D0CF43612D /* FLTWritableData.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTWritableData.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTWritableData.m"; sourceTree = "<group>"; };
		BF1B50AA0BC139719322C05FF10A7435 /* FLTCaptureConnection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureConnection.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureConnection.h"; sourceTree = "<group>"; };
		BF623A583CE00A886C0FCCA327335F7A /* ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist"; sourceTree = "<group>"; };
		C0664FD26AA9A759556248B8C063580E /* FVPVideoPlayerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPVideoPlayerPlugin.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m"; sourceTree = "<group>"; };
		C296E83A780806BA6A7A0028BAFCD1A8 /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		C5346383D8B1E71918D865AE34A3B24E /* SwiftImageGallerySaverPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftImageGallerySaverPlugin.swift; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/SwiftImageGallerySaverPlugin.swift"; sourceTree = "<group>"; };
		C6AA5C2B0E6D43669CE8BB64DD20AB03 /* FVPDisplayLink.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPDisplayLink.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h"; sourceTree = "<group>"; };
		C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		C937C0FFB39216E54DD5C3AEFDA9AAB8 /* FLTCameraPermissionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCameraPermissionManager.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraPermissionManager.m"; sourceTree = "<group>"; };
		C96A09EA402FAA3AFE7E858B0DEB36B1 /* integration_test.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = integration_test.modulemap; sourceTree = "<group>"; };
		C98129621AC9990B8882B0D72953837B /* camera_avfoundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "camera_avfoundation-prefix.pch"; sourceTree = "<group>"; };
		CAF69C510652FB16CBD14EA635BBD0BF /* FLTIntegrationTestRunner.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTIntegrationTestRunner.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h; sourceTree = "<group>"; };
		CC038C6A6C53B34D65D0CE192F4D055B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		CCEBD973F9CD153BF9AF31B801CC9ADA /* messages.g.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = messages.g.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/messages.g.m"; sourceTree = "<group>"; };
		CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		CFE8A3216265935B7E76A2ACE23D90C5 /* FLTCam.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCam.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam.h"; sourceTree = "<group>"; };
		D206DEF61A2CDF09843879D9351DB288 /* FLTCapturePhotoOutput.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCapturePhotoOutput.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCapturePhotoOutput.m"; sourceTree = "<group>"; };
		D2548E7528F34E83FB32A976652EDE29 /* FLTPermissionServicing.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTPermissionServicing.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTPermissionServicing.m"; sourceTree = "<group>"; };
		D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		D38B7F99A5B6682CEA29E08A0D0E2DF7 /* FLTAssetWriter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTAssetWriter.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTAssetWriter.h"; sourceTree = "<group>"; };
		D65B01A47AB2C394204472ED5235A328 /* integration_test-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "integration_test-Info.plist"; sourceTree = "<group>"; };
		D9D635E09914BDB29BD9BCC7A6594C01 /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		DEE2F5B090C5D6DDA1DD6562CE4AAC4C /* IntegrationTestIosTest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestIosTest.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m; sourceTree = "<group>"; };
		DF09DBA551692F6A77FBBDD814CF4919 /* FVPNativeVideoViewFactory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPNativeVideoViewFactory.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPNativeVideoViewFactory.m"; sourceTree = "<group>"; };
		DF62AC4EAA87E5D44EAFB68759436506 /* FVPEventBridge.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPEventBridge.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPEventBridge.m"; sourceTree = "<group>"; };
		DF72F52732914F17C688D8A19152410B /* video_player_avfoundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "video_player_avfoundation-umbrella.h"; sourceTree = "<group>"; };
		E4C0AD57E34570BCBA58ACF21B73AEC2 /* FLTCaptureOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureOutput.h"; sourceTree = "<group>"; };
		E68584642EF5A75794112EE75CAE3684 /* path_provider_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-prefix.pch"; sourceTree = "<group>"; };
		E6D10824EDE0574C644F91B50DDBF526 /* image_gallery_saver.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = image_gallery_saver.modulemap; sourceTree = "<group>"; };
		E7A8A68F328FFCB98FE18C3CF23A6437 /* video_player_avfoundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = video_player_avfoundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		E95C3E491A5C346776B183FE8832E4A5 /* Camera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Camera.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Camera.swift"; sourceTree = "<group>"; };
		ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		ED5E4A58FC675653EAB90B93DCDB95B0 /* CameraPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.swift"; sourceTree = "<group>"; };
		F04D92095EA07B70A3A47E752C77A409 /* camera_avfoundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = camera_avfoundation.modulemap; sourceTree = "<group>"; };
		F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = video_player_avfoundation; path = video_player_avfoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F31A22A66D76A6C601387422E7EDD33F /* image_gallery_saver.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_gallery_saver.release.xcconfig; sourceTree = "<group>"; };
		F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		F50B23F86D98ED1B90A057DDDF120C6E /* ImageGallerySaverPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ImageGallerySaverPlugin.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.h"; sourceTree = "<group>"; };
		F64A70734A55167F6AC526724BECA617 /* FLTCaptureVideoDataOutput.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureVideoDataOutput.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureVideoDataOutput.m"; sourceTree = "<group>"; };
		F835B0C601031BF5ED749DD9C8AFBDEC /* messages.g.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = messages.g.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h"; sourceTree = "<group>"; };
		F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "camera_avfoundation-camera_avfoundation_privacy"; path = camera_avfoundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		F9C5AFC7CCF17CB09B5527C27AEE3D04 /* FLTSavePhotoDelegate_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTSavePhotoDelegate_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h"; sourceTree = "<group>"; };
		FC37902FB86E31D82689ACDF156B222E /* FLTWritableData.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTWritableData.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTWritableData.h"; sourceTree = "<group>"; };
		FC4847692B9CC6B515FB9315E4670058 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		27D9C97B47293802041F7C7AFBCCC6AA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C51CA6EC745ACB63E424757C710B36C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4FE4925632639DBA407BB6D4231D6498 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556D27E7BFC2BEC2841B5B9A9E5A9ACF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				030E9CC53C0A2C293E1316DF96C82228 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		81FD8C60300A3062D2FDDB1533F5A919 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A3222ABF19FC9866D221A287F9BFEBC9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		890633AA2AFB403BE31573A4F8EC6F2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AFE33A2760FC3EF68E47CAC8096CBFB7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7D629EF41C6F8998A19AE82A76EC5AE4 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D37529713F6219C15572CE565F23780E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */,
				CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB6B120BD47FC66302FFF63D264FB73B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C0A7D5B0D751A48FD51D23AD682BAA95 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0029577BA61E58AFB48B152F6BBF1F14 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				3B4DA46482D764EEE57D91F6FF417DC0 /* camera_avfoundation */,
				B34F1FF325E6EF88ECA19D031D303BDE /* Flutter */,
				35F861F6A2B3C36042F317E65F26E127 /* image_gallery_saver */,
				AC8B944D45EDE72E8B3D90526BD820F9 /* integration_test */,
				352C2596BC763BF93F24776D7BFB1B7F /* path_provider_foundation */,
				4B6811718381F68FE4014601FA266839 /* video_player_avfoundation */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		0141E1EBC7E70A4AA92CE047BDA99C75 /* .. */ = {
			isa = PBXGroup;
			children = (
				0C588C845F95040795EC360D5380B2D3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0301A1F20A732391BD5A87C895FF8FD2 /* include */ = {
			isa = PBXGroup;
			children = (
				1771B1968A6203C3C4F2508F698DD14C /* video_player_avfoundation */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		07391BF3AB263D5A49AC60C4F15BA2C8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				357CF9D7973CA325A3359A17B02945C5 /* ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist */,
				4A4D815B348C1FD1A09292CFE4E1D416 /* video_player_avfoundation.modulemap */,
				214C4B9B9D41B451D3379497D1B33861 /* video_player_avfoundation-dummy.m */,
				161B707AEEF2E0518E6EF901215F4227 /* video_player_avfoundation-Info.plist */,
				55BEA53412DBC31AE46FFC29C73120C9 /* video_player_avfoundation-prefix.pch */,
				DF72F52732914F17C688D8A19152410B /* video_player_avfoundation-umbrella.h */,
				79AF209439A6073BB2DAD9C81E658B61 /* video_player_avfoundation.debug.xcconfig */,
				B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/video_player_avfoundation";
			sourceTree = "<group>";
		};
		0955B41906BF05451ED22D9DAFC3F031 /* Products */ = {
			isa = PBXGroup;
			children = (
				AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */,
				F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */,
				4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */,
				5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */,
				AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */,
				3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */,
				337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0B8F2BA253B931E103065FCA8460D40D /* ios */ = {
			isa = PBXGroup;
			children = (
				A7263C03B107022C7532984B8B6F9A8D /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		0C588C845F95040795EC360D5380B2D3 /* .. */ = {
			isa = PBXGroup;
			children = (
				7C2E36FB06DDCB1AF127CD6797182FBD /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0F443469D871D440033E22679526413A /* .. */ = {
			isa = PBXGroup;
			children = (
				834E8A43C143F24B2BEA6A82AD695517 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		10565FE9A374668CEF1E09C61176F24A /* ios */ = {
			isa = PBXGroup;
			children = (
				CE0ED29FFFB82E5C63A226C049001AF4 /* Classes */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		14B498BFF1B5836D6EC4FC17DCF9D205 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				33F4CFD5B88A71BC25B93E7168DFD55E /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		161798835ECD6E7E6C2FBECBE2B4852A /* .. */ = {
			isa = PBXGroup;
			children = (
				46B6637A36814D4CDCB566AEF6359964 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1DE6440B6853131A80C53DDA34375AAB /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		16A23D5A1A1741D431B5656A72B38A48 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F04D92095EA07B70A3A47E752C77A409 /* camera_avfoundation.modulemap */,
				010642736AA7A5D219D25D07A1AB184C /* camera_avfoundation-dummy.m */,
				0FFFE8A406E3A5EB5F00EC63499D9E17 /* camera_avfoundation-Info.plist */,
				C98129621AC9990B8882B0D72953837B /* camera_avfoundation-prefix.pch */,
				24F8B3A934D3AC8373D02158B05AEEA5 /* camera_avfoundation-umbrella.h */,
				8D3DF8C698976AC3F6BB132B3BA96C3E /* camera_avfoundation.debug.xcconfig */,
				1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */,
				BF623A583CE00A886C0FCCA327335F7A /* ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/camera_avfoundation";
			sourceTree = "<group>";
		};
		173D3A3DD840C00E6D51FAA3C49728C4 /* Sources */ = {
			isa = PBXGroup;
			children = (
				2A204379DF2FC7AC27638DD026ED9F21 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		1747768C59E24CB8128E9D5F529DF49C /* manish */ = {
			isa = PBXGroup;
			children = (
				43687A26B76D9F93C9AB32FF4C25EB32 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		1771B1968A6203C3C4F2508F698DD14C /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				B6005A10AE569542CBD43D20ADA57ED6 /* AVAssetTrackUtils.h */,
				2E7ED98A01C4395E65858967A177056D /* FVPAVFactory.h */,
				C6AA5C2B0E6D43669CE8BB64DD20AB03 /* FVPDisplayLink.h */,
				042F7E29351E9FA529D492C2B156CB8F /* FVPEventBridge.h */,
				8FB93DAC4FCCE59EAADC3F590168FA80 /* FVPFrameUpdater.h */,
				A460A1FDEA543FAF251AD9891CA44B6D /* FVPNativeVideoView.h */,
				A6F640F32DA593A07ED25DF9741E38A1 /* FVPNativeVideoViewFactory.h */,
				5347F9B6E3B230FA3AF1FD69E81A807D /* FVPTextureBasedVideoPlayer.h */,
				26C2E100FCDC9334591E88A3078B4382 /* FVPTextureBasedVideoPlayer_Test.h */,
				71BA40B0F5F6F47991AE8EC1A1AE299F /* FVPVideoEventListener.h */,
				3007591DB86A70A758237A18CCB5BD86 /* FVPVideoPlayer.h */,
				6D4683162ABDFF11BFD78D50F790BCA2 /* FVPVideoPlayer_Internal.h */,
				751A6559064247F9F84F7FA083862422 /* FVPVideoPlayerPlugin.h */,
				B6231A1FC7651F5C02A51BBD561B97C4 /* FVPVideoPlayerPlugin_Test.h */,
				AD41C0E16A6A7F377C3D7BF20189265E /* FVPViewProvider.h */,
				F835B0C601031BF5ED749DD9C8AFBDEC /* messages.g.h */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		19186DD614B806A67831F7C04836CA35 /* Pod */ = {
			isa = PBXGroup;
			children = (
				BB99F5920096C31629F014277ED50DE3 /* integration_test.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		1A364992FD29986EA1A06445FE939A19 /* .. */ = {
			isa = PBXGroup;
			children = (
				F031C4F906A02B585CC2FB05A1AAC734 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation";
			sourceTree = "<group>";
		};
		1BE51736D8C12059C9745A68E2309CA0 /* Sources */ = {
			isa = PBXGroup;
			children = (
				C10CD1CF1913DD0292C507DD4724B8BA /* camera_avfoundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		1C482AE8A1F3784ABABFD54866E0E6EB /* darwin */ = {
			isa = PBXGroup;
			children = (
				6A564A3ABEE285DDF42DCCCDEA5D62C8 /* video_player_avfoundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		1D96EC8BFBD876A610E05A3C4743F89A /* .. */ = {
			isa = PBXGroup;
			children = (
				47DC3996C74E974D0E6211163137A65F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1DE6440B6853131A80C53DDA34375AAB /* iOS */ = {
			isa = PBXGroup;
			children = (
				8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */,
				ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		1F2172A4F42C2F33E0ADD3B6E897F63A /* .. */ = {
			isa = PBXGroup;
			children = (
				4E65A5C37CD2E98FE907C87B65ACF75C /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		213E225EF8EA85727AF279E73DCC4E65 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				B27D62CAC13EE49C3F3B7F2853BBFC38 /* Sources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		23C0AE2510E6CF35647F80142A5286B2 /* Pod */ = {
			isa = PBXGroup;
			children = (
				599AD37E15BF07EEC6A453E94E297F34 /* LICENSE */,
				A1FC51BCD74D993A9BFD720D790DAF2F /* path_provider_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		241A1B195A02995BE735A4F960E474D7 /* Documents */ = {
			isa = PBXGroup;
			children = (
				482657D67000D6DE0E601827244D9EB6 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		2624EEB4A5366988C39A38971FE20D3C /* include */ = {
			isa = PBXGroup;
			children = (
				CAF69C510652FB16CBD14EA635BBD0BF /* FLTIntegrationTestRunner.h */,
				1606E2A6395A3668B09FAEF0C3B7A985 /* IntegrationTestIosTest.h */,
				376FF978AF671AE59567D0BE167A853A /* IntegrationTestPlugin.h */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		26DE552947C2B2331DE82E77EE19A85A /* plugins */ = {
			isa = PBXGroup;
			children = (
				14B498BFF1B5836D6EC4FC17DCF9D205 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		2757C6474E4E419B32811D68F71BBEC1 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				59E47CA0DE0A47BFEB5666CCF1F363AC /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		2A204379DF2FC7AC27638DD026ED9F21 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				FC4847692B9CC6B515FB9315E4670058 /* messages.g.swift */,
				7F466989D1C26965A9466A7E75C28ECE /* PathProviderPlugin.swift */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		2A5D56BE369BB541B3B56F72E6ECA1FB /* plugins */ = {
			isa = PBXGroup;
			children = (
				6C8A76EDE71838FE9512293CDBF46BAC /* video_player_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		2AE15384A01A5E244F990F3C19B28D49 /* Pod */ = {
			isa = PBXGroup;
			children = (
				D9D635E09914BDB29BD9BCC7A6594C01 /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		2C47E23A160BD07E1588643C7CD2C57F /* integration_test */ = {
			isa = PBXGroup;
			children = (
				709BA6DF71A5CF9EDBCAD8B9379B58BC /* ios */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		30181493885F0435CB8B88C37F8CF02E /* camera */ = {
			isa = PBXGroup;
			children = (
				BD590798917EA6BCC8F2ED5407533922 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		313FBAFA9E65837188A157CF21D04596 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1D78074A79998639052B973A3742A802 /* AVAssetTrackUtils.m */,
				4B328BCA07BAB5B77D53BDA4739C7481 /* FVPAVFactory.m */,
				908395F656128487B48C9AB4444FAE9B /* FVPCADisplayLink.m */,
				DF62AC4EAA87E5D44EAFB68759436506 /* FVPEventBridge.m */,
				097A4AF8D026387A9BAD833BCE6938AD /* FVPFrameUpdater.m */,
				DF09DBA551692F6A77FBBDD814CF4919 /* FVPNativeVideoViewFactory.m */,
				783C9124A0D070B0D839131B2817F6A7 /* FVPTextureBasedVideoPlayer.m */,
				2A3E7B1AAB2A147C69421D4B26BB4C68 /* FVPVideoPlayer.m */,
				C0664FD26AA9A759556248B8C063580E /* FVPVideoPlayerPlugin.m */,
				9FCF1CF4ECD8EAA8B20BDFAD0E9B9E6E /* FVPViewProvider.m */,
				A037ABC3E991AD8BDAD565AF8EE9FFC5 /* messages.g.m */,
				0301A1F20A732391BD5A87C895FF8FD2 /* include */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		33F4CFD5B88A71BC25B93E7168DFD55E /* darwin */ = {
			isa = PBXGroup;
			children = (
				B59940664BA7020CDB1435DA0DCCD3C9 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		352C2596BC763BF93F24776D7BFB1B7F /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				BF61539143D9AEFBDFF897E3977F813E /* .. */,
				23C0AE2510E6CF35647F80142A5286B2 /* Pod */,
				F5A09BEF101B971CBF9FD618B5903FAE /* Support Files */,
			);
			name = path_provider_foundation;
			path = ../.symlinks/plugins/path_provider_foundation/darwin;
			sourceTree = "<group>";
		};
		35F861F6A2B3C36042F317E65F26E127 /* image_gallery_saver */ = {
			isa = PBXGroup;
			children = (
				E0E2793D3755DD1FFF289DD2F4A36416 /* .. */,
				F347F3B6D8988C33C664B8C70C3D9225 /* Pod */,
				E34247966341205A218709A5588FE9DD /* Support Files */,
			);
			name = image_gallery_saver;
			path = ../.symlinks/plugins/image_gallery_saver/ios;
			sourceTree = "<group>";
		};
		3B4DA46482D764EEE57D91F6FF417DC0 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1A364992FD29986EA1A06445FE939A19 /* .. */,
				ACAE8BDAB769D902C9E6CBA0086662DE /* Pod */,
				16A23D5A1A1741D431B5656A72B38A48 /* Support Files */,
			);
			name = camera_avfoundation;
			path = ../.symlinks/plugins/camera_avfoundation/ios;
			sourceTree = "<group>";
		};
		3EDE4F60DBAC7F6FBB8079426D33F31B /* .. */ = {
			isa = PBXGroup;
			children = (
				B40F0234776EC7F3224F7908B923451D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		3EE9A94CC3CA86CD05E602AD4440B5A8 /* ios */ = {
			isa = PBXGroup;
			children = (
				7451C1D87DE77DED0EAD0097A3479540 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		41AD4FB47FBB40F842CF468A91A373A8 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				9B975765D3FAE2FA8E4257C4FDAA20FE /* Resources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		42BF95BAE5185777A89ECD83A2E8F797 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				54D3CE6CA37110D7ABF8478C476755BD /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		4329D1888D0D0223735D2D8620746F5B /* .. */ = {
			isa = PBXGroup;
			children = (
				3EDE4F60DBAC7F6FBB8079426D33F31B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		43687A26B76D9F93C9AB32FF4C25EB32 /* camera */ = {
			isa = PBXGroup;
			children = (
				69A9289C8B2FE0FE7C42B2C3379DCB99 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		46A2CD5F2A18AF666E3E68A9FC037B26 /* .. */ = {
			isa = PBXGroup;
			children = (
				BFA76EE1998A2A2D08ADF656BB211146 /* Documents */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		46B6637A36814D4CDCB566AEF6359964 /* Documents */ = {
			isa = PBXGroup;
			children = (
				D2D2504128AE15DE5E624BBF25828713 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		47DC3996C74E974D0E6211163137A65F /* .. */ = {
			isa = PBXGroup;
			children = (
				6371CB04498E868248E951738563B29A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		482657D67000D6DE0E601827244D9EB6 /* manish */ = {
			isa = PBXGroup;
			children = (
				561B2873E6B59AB93387A1E10D7C98A6 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		48F49BA4DA6CAC7B979EF0147B0142EF /* Documents */ = {
			isa = PBXGroup;
			children = (
				82149BECE6B219465ADC66F098FB861E /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		4AFE840D1E7CBBC4B08AF7FEBE973508 /* plugins */ = {
			isa = PBXGroup;
			children = (
				2C47E23A160BD07E1588643C7CD2C57F /* integration_test */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		4B6811718381F68FE4014601FA266839 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				A9DB78B8F2F428B486499CD9765F9469 /* .. */,
				F3258E886ABD6293D3D25CE4F43EE6EE /* Pod */,
				07391BF3AB263D5A49AC60C4F15BA2C8 /* Support Files */,
			);
			name = video_player_avfoundation;
			path = ../.symlinks/plugins/video_player_avfoundation/darwin;
			sourceTree = "<group>";
		};
		4C9E5F489AFFAA32F2113190CF6E1117 /* .. */ = {
			isa = PBXGroup;
			children = (
				5B63781854E45D28263B32CCAF802A73 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4D99043793E0B9AD946B9CAD87034CCA /* .. */ = {
			isa = PBXGroup;
			children = (
				AB414112BBB544AB6C3D8F779CA308AB /* .. */,
				48F49BA4DA6CAC7B979EF0147B0142EF /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4E23AE80F1E0F34B9DDC4EB8CAA0482A /* Sources */ = {
			isa = PBXGroup;
			children = (
				C011B5551488AACE923605DDBFA1903D /* integration_test */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		4E65A5C37CD2E98FE907C87B65ACF75C /* Documents */ = {
			isa = PBXGroup;
			children = (
				F2C380166144422575B6F1522606E8C7 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		4ED2634FF3DAF5CE53F6C473521139EC /* camera */ = {
			isa = PBXGroup;
			children = (
				E9D30211E650D9F0048945716394A4A0 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		4F04877B72D19FA7393A554FD7AC8F6C /* .. */ = {
			isa = PBXGroup;
			children = (
				241A1B195A02995BE735A4F960E474D7 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4F1CC8270CA2AA2F19AF7CC1B79EB649 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				E95C3E491A5C346776B183FE8832E4A5 /* Camera.swift */,
				ED5E4A58FC675653EAB90B93DCDB95B0 /* CameraPlugin.swift */,
				A9A7954D281A912A3787A97AC592A539 /* DefaultCamera.swift */,
				24438D3FB467F1BB768EDF4EA3238A1C /* QueueUtils.swift */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		4F7AAD61AA30AD7D54F741FF4BDB9AAD /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				2A5D56BE369BB541B3B56F72E6ECA1FB /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		4F9355C3B1E68B45D9D0A4490644BC6E /* Documents */ = {
			isa = PBXGroup;
			children = (
				C9350318B8E0C7EA520976363C847ADB /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		51533C3EF6134E91064DD1BFB10FB857 /* darwin */ = {
			isa = PBXGroup;
			children = (
				213E225EF8EA85727AF279E73DCC4E65 /* video_player_avfoundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		51F8CE1A9976576F14CE501636820258 /* ios */ = {
			isa = PBXGroup;
			children = (
				42BF95BAE5185777A89ECD83A2E8F797 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		53520FF7D3BC4AD00A4DBDB598E7E7B3 /* Documents */ = {
			isa = PBXGroup;
			children = (
				590E99514C4E23297FB0A8A4A4D678DF /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		54D3CE6CA37110D7ABF8478C476755BD /* plugins */ = {
			isa = PBXGroup;
			children = (
				A28885A805F4395279448C3C48EE0953 /* image_gallery_saver */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		561B2873E6B59AB93387A1E10D7C98A6 /* camera */ = {
			isa = PBXGroup;
			children = (
				0B8F2BA253B931E103065FCA8460D40D /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		590E99514C4E23297FB0A8A4A4D678DF /* manish */ = {
			isa = PBXGroup;
			children = (
				F71345CA7251D2A6D0AFB0AC8C70CCA4 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		59E47CA0DE0A47BFEB5666CCF1F363AC /* darwin */ = {
			isa = PBXGroup;
			children = (
				AB9522FE0FF458743B78DFD06E937C0F /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		5B63781854E45D28263B32CCAF802A73 /* .. */ = {
			isa = PBXGroup;
			children = (
				4D99043793E0B9AD946B9CAD87034CCA /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		602862B0B97149DC5FD67167B05666D8 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				90AEA062F2CB5C158052BD5BD0461BE0 /* camera_avfoundation.h */,
				7A1246DF6159FF87C9A3280C7FE30EEC /* CameraProperties.h */,
				D38B7F99A5B6682CEA29E08A0D0E2DF7 /* FLTAssetWriter.h */,
				CFE8A3216265935B7E76A2ACE23D90C5 /* FLTCam.h */,
				5028B090B48794F6595EDF78741F41E7 /* FLTCam_Test.h */,
				57EC7093C265553F3F96104D34FC8069 /* FLTCamConfiguration.h */,
				3BBEBF85E9C9931DA4FE36701B34741E /* FLTCameraDeviceDiscovering.h */,
				A6051D22CDF80C16A95D4C0CBEC88D09 /* FLTCameraPermissionManager.h */,
				B59DC7BEDC8BC0D3C57F60C5BB90E233 /* FLTCamMediaSettingsAVWrapper.h */,
				BF1B50AA0BC139719322C05FF10A7435 /* FLTCaptureConnection.h */,
				7425FF1330AD7A796E167B0218256B29 /* FLTCaptureDevice.h */,
				815F110165A5347FF20D9F1992E01D89 /* FLTCaptureDeviceFormat.h */,
				E4C0AD57E34570BCBA58ACF21B73AEC2 /* FLTCaptureOutput.h */,
				5C97A49FAD6F207F51BD7E4AC970BEB7 /* FLTCapturePhotoOutput.h */,
				97809657A1A0A75CE00277EAD6888424 /* FLTCaptureSession.h */,
				BACB25B643737223BFB8869D04C59E2A /* FLTCaptureVideoDataOutput.h */,
				99ABDC44B62C1A6E96DE6600EEB2F8D7 /* FLTDeviceOrientationProviding.h */,
				53F96E8B0935B74DE080CBFFD68DA7E6 /* FLTEventChannel.h */,
				1F0F2A51B57BD5515ABC836B35523EE7 /* FLTFormatUtils.h */,
				5D012600E7F75D2E7042ABD59F0BA783 /* FLTImageStreamHandler.h */,
				94A7140A34B652D1E52004EF912C682B /* FLTPermissionServicing.h */,
				0F3EBFEF5C5EC4F061B0E0CF06B15C9D /* FLTSavePhotoDelegate.h */,
				F9C5AFC7CCF17CB09B5527C27AEE3D04 /* FLTSavePhotoDelegate_Test.h */,
				80ACD6D2053C602FBE737C099F503FDE /* FLTThreadSafeEventChannel.h */,
				FC37902FB86E31D82689ACDF156B222E /* FLTWritableData.h */,
				8039219AB7277FCEC407EC487E453E2D /* messages.g.h */,
				4A552816171BA37AB916D83F2CD12C6D /* QueueUtils.h */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		6371CB04498E868248E951738563B29A /* .. */ = {
			isa = PBXGroup;
			children = (
				46A2CD5F2A18AF666E3E68A9FC037B26 /* .. */,
				E5AA723C618BA03169A7B9EB848BF7A0 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6962A6528F11463E5EA7378247913B95 /* camera_avfoundation_objc */ = {
			isa = PBXGroup;
			children = (
				7224C775F567EAC66DA6D831B006DC7A /* CameraProperties.m */,
				0AC3857A2A8AB25C6628A56CD817E071 /* FLTAssetWriter.m */,
				44967DCA0041C77AB6811360BAA2B2BD /* FLTCam.m */,
				BB35B717489DAF3E4421389A7F7D057F /* FLTCamConfiguration.m */,
				9B7DAFD02453D26F26330A9AD3543433 /* FLTCameraDeviceDiscovering.m */,
				C937C0FFB39216E54DD5C3AEFDA9AAB8 /* FLTCameraPermissionManager.m */,
				1E01ADF02C45990C84161F36CA350671 /* FLTCamMediaSettingsAVWrapper.m */,
				6A8A6DF427AB82D7AAB8A5D8BB5671B5 /* FLTCaptureConnection.m */,
				208FBA9E7E9E723D9CE4CFAD26B4BBFD /* FLTCaptureDevice.m */,
				7C69386B4A3C728668E3068E9AEDD4CA /* FLTCaptureDeviceFormat.m */,
				D206DEF61A2CDF09843879D9351DB288 /* FLTCapturePhotoOutput.m */,
				7ACD42F9C402041BE10F2A93E4D83783 /* FLTCaptureSession.m */,
				F64A70734A55167F6AC526724BECA617 /* FLTCaptureVideoDataOutput.m */,
				6EA5ACBC0485FFF7CC1219C4E2C1CE6E /* FLTDeviceOrientationProviding.m */,
				70387EFB62A21854FF22C870708EFA88 /* FLTFormatUtils.m */,
				1127A62E38665719680D686282496887 /* FLTImageStreamHandler.m */,
				D2548E7528F34E83FB32A976652EDE29 /* FLTPermissionServicing.m */,
				9D29ED4D7E9F66FF7291962F621D51BA /* FLTSavePhotoDelegate.m */,
				44579597553404BC2637060600B4798B /* FLTThreadSafeEventChannel.m */,
				BD8FA65FFA2C3CA33860D2D0CF43612D /* FLTWritableData.m */,
				CCEBD973F9CD153BF9AF31B801CC9ADA /* messages.g.m */,
				546880632FDD3A3FE6465329DEE94FB5 /* QueueUtils.m */,
				7D3D7CD5D71A54AF0AFEE90EDDCE881D /* include */,
			);
			name = camera_avfoundation_objc;
			path = camera_avfoundation_objc;
			sourceTree = "<group>";
		};
		69A9289C8B2FE0FE7C42B2C3379DCB99 /* ios */ = {
			isa = PBXGroup;
			children = (
				9572B627A711A5A4112E2E2174476C68 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		6A564A3ABEE285DDF42DCCCDEA5D62C8 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				F330CEF6F70D9D6E813035D4CBD22309 /* Sources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		6B78645C4142B94B837218B40654F6C2 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				C96A09EA402FAA3AFE7E858B0DEB36B1 /* integration_test.modulemap */,
				2FD708D969ABBDFD56A21C2AA0EB227D /* integration_test-dummy.m */,
				D65B01A47AB2C394204472ED5235A328 /* integration_test-Info.plist */,
				AF1FE46AEE03ED1E5E8F1994D6720EA9 /* integration_test-prefix.pch */,
				493D0C661842ACDBCF9EF988F815487F /* integration_test-umbrella.h */,
				4FA60FD1EE2D268521905E7104813844 /* integration_test.debug.xcconfig */,
				96929D15A9E9CCC603BF3911B78707AA /* integration_test.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/integration_test";
			sourceTree = "<group>";
		};
		6C8A76EDE71838FE9512293CDBF46BAC /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1C482AE8A1F3784ABABFD54866E0E6EB /* darwin */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		709BA6DF71A5CF9EDBCAD8B9379B58BC /* ios */ = {
			isa = PBXGroup;
			children = (
				FDCEF58B1BE4E4667323018342965A56 /* integration_test */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		744BF607F847EAC9697D1CA796C97714 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				DD5C638237EAE9B530B601A6B39EF085 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		7451C1D87DE77DED0EAD0097A3479540 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				BB38731DE0354DB74E64147F02323492 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		775083D937A9740FD33A41F0BC7E0BE0 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				E415DEFE33D466E1446FD894C92860AF /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */,
				C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */,
				D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */,
				7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */,
				317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */,
				51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */,
				96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */,
				CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */,
				9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */,
				D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		7C2E36FB06DDCB1AF127CD6797182FBD /* .. */ = {
			isa = PBXGroup;
			children = (
				1D96EC8BFBD876A610E05A3C4743F89A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7D3D7CD5D71A54AF0AFEE90EDDCE881D /* include */ = {
			isa = PBXGroup;
			children = (
				602862B0B97149DC5FD67167B05666D8 /* camera_avfoundation */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		8009E6C9B710A59B69A6B2F702F92E2B /* .. */ = {
			isa = PBXGroup;
			children = (
				53520FF7D3BC4AD00A4DBDB598E7E7B3 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		82149BECE6B219465ADC66F098FB861E /* manish */ = {
			isa = PBXGroup;
			children = (
				DE45C3D3A3D111531995557E681B6F49 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		834E8A43C143F24B2BEA6A82AD695517 /* .. */ = {
			isa = PBXGroup;
			children = (
				EDA5778D83F5F4DBDDA3F8E6BC4C0FCC /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8503E3D9E3D5DACC09A45F429548ACED /* plugins */ = {
			isa = PBXGroup;
			children = (
				F47ECAB1DDB4BB886947F91B36523D0F /* camera_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		8639C13EE7035F031CAA248EEED58A33 /* camera */ = {
			isa = PBXGroup;
			children = (
				51F8CE1A9976576F14CE501636820258 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		884B5BDFA2DD4414D0D9BED3418CC79B /* .. */ = {
			isa = PBXGroup;
			children = (
				DDFD6FCB0009B24C7E8722F256B29CA8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		89B393D3DF61CF713931384740DCB59F /* .. */ = {
			isa = PBXGroup;
			children = (
				C8896643047E680F8B55B011E62D5D2D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8C077CDA292F86C1D99281D88A9DAD18 /* ios */ = {
			isa = PBXGroup;
			children = (
				744BF607F847EAC9697D1CA796C97714 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		8E5CBD08C9798394DE25E4482A0147BF /* ios */ = {
			isa = PBXGroup;
			children = (
				A35C2078DB0F2610FD2566D7F1EC5355 /* camera_avfoundation */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		8FEC76214DD5278C57F12E0B03D77F52 /* Resources */ = {
			isa = PBXGroup;
			children = (
				441FF6270AA3B2B9271D8FB869EA69E4 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		90D67D1F98BADB403EDC8E18A98017A9 /* .. */ = {
			isa = PBXGroup;
			children = (
				0F443469D871D440033E22679526413A /* .. */,
			);
			name = ..;
			path = ../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources;
			sourceTree = "<group>";
		};
		9572B627A711A5A4112E2E2174476C68 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				26DE552947C2B2331DE82E77EE19A85A /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		96FF9CB0E676783582AAB8D129DD47F6 /* .. */ = {
			isa = PBXGroup;
			children = (
				884B5BDFA2DD4414D0D9BED3418CC79B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9B3FC5C5718E77A7495B6E0A66BD1805 /* camera */ = {
			isa = PBXGroup;
			children = (
				3EE9A94CC3CA86CD05E602AD4440B5A8 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		9B975765D3FAE2FA8E4257C4FDAA20FE /* Resources */ = {
			isa = PBXGroup;
			children = (
				5BE6CF81A9A9549249986607F3F790C7 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		9D1B7D621F76C86E75AA1005F316FC8C /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				AE8A1FF32263490E2C0EA41F13C5F4E4 /* Sources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		A28885A805F4395279448C3C48EE0953 /* image_gallery_saver */ = {
			isa = PBXGroup;
			children = (
				10565FE9A374668CEF1E09C61176F24A /* ios */,
			);
			name = image_gallery_saver;
			path = image_gallery_saver;
			sourceTree = "<group>";
		};
		A35C2078DB0F2610FD2566D7F1EC5355 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1BE51736D8C12059C9745A68E2309CA0 /* Sources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		A3AA1A180FA21FA01FC9A8504CF8C456 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				4AFE840D1E7CBBC4B08AF7FEBE973508 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		A4F91F176F681815E7CDF49339E251FA /* ios */ = {
			isa = PBXGroup;
			children = (
				A3AA1A180FA21FA01FC9A8504CF8C456 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		A716F467EB28D9BF271E8412201B0EB9 /* .. */ = {
			isa = PBXGroup;
			children = (
				161798835ECD6E7E6C2FBECBE2B4852A /* .. */,
			);
			name = ..;
			path = ".pub-cache";
			sourceTree = "<group>";
		};
		A7263C03B107022C7532984B8B6F9A8D /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				8503E3D9E3D5DACC09A45F429548ACED /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		A7E47647A5FB6631D9EDA07F0588B4AB /* .. */ = {
			isa = PBXGroup;
			children = (
				D25B7356A6AD5284B4769EB461997343 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A9DB78B8F2F428B486499CD9765F9469 /* .. */ = {
			isa = PBXGroup;
			children = (
				EE376F7C23550D725178C08C1FB3B3DA /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation";
			sourceTree = "<group>";
		};
		AB414112BBB544AB6C3D8F779CA308AB /* .. */ = {
			isa = PBXGroup;
			children = (
				4F04877B72D19FA7393A554FD7AC8F6C /* .. */,
			);
			name = ..;
			path = ".pub-cache";
			sourceTree = "<group>";
		};
		AB9522FE0FF458743B78DFD06E937C0F /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				173D3A3DD840C00E6D51FAA3C49728C4 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		AC3100F1E95DFAF73DBF52F0E9F7EBFC /* manish */ = {
			isa = PBXGroup;
			children = (
				9B3FC5C5718E77A7495B6E0A66BD1805 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		AC8B944D45EDE72E8B3D90526BD820F9 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				90D67D1F98BADB403EDC8E18A98017A9 /* .. */,
				19186DD614B806A67831F7C04836CA35 /* Pod */,
				6B78645C4142B94B837218B40654F6C2 /* Support Files */,
			);
			name = integration_test;
			path = ../.symlinks/plugins/integration_test/ios;
			sourceTree = "<group>";
		};
		ACAE8BDAB769D902C9E6CBA0086662DE /* Pod */ = {
			isa = PBXGroup;
			children = (
				0C1C64A8C9B38E7D5EEB05F4A4237B10 /* camera_avfoundation.podspec */,
				716F1AC6F57B6C398E4C01AC9C5D815B /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		AE600A32AC5359B72604C3C7692A30E4 /* .. */ = {
			isa = PBXGroup;
			children = (
				A716F467EB28D9BF271E8412201B0EB9 /* .. */,
				4F9355C3B1E68B45D9D0A4490644BC6E /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		AE8A1FF32263490E2C0EA41F13C5F4E4 /* Sources */ = {
			isa = PBXGroup;
			children = (
				4F1CC8270CA2AA2F19AF7CC1B79EB649 /* camera_avfoundation */,
				6962A6528F11463E5EA7378247913B95 /* camera_avfoundation_objc */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		B0F2E69CD4B1861C3117024E80B3BDF1 /* .. */ = {
			isa = PBXGroup;
			children = (
				96FF9CB0E676783582AAB8D129DD47F6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B27D62CAC13EE49C3F3B7F2853BBFC38 /* Sources */ = {
			isa = PBXGroup;
			children = (
				D7E98F60412E9B1183DCC9EA478AC661 /* video_player_avfoundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		B34F1FF325E6EF88ECA19D031D303BDE /* Flutter */ = {
			isa = PBXGroup;
			children = (
				2AE15384A01A5E244F990F3C19B28D49 /* Pod */,
				CB8D35FE7C3C086DDE4B0072C2956A98 /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		B40F0234776EC7F3224F7908B923451D /* .. */ = {
			isa = PBXGroup;
			children = (
				1F2172A4F42C2F33E0ADD3B6E897F63A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B59940664BA7020CDB1435DA0DCCD3C9 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				E718C165B858F59E51986BC3D8EEE755 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		BB38731DE0354DB74E64147F02323492 /* plugins */ = {
			isa = PBXGroup;
			children = (
				2757C6474E4E419B32811D68F71BBEC1 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		BD590798917EA6BCC8F2ED5407533922 /* ios */ = {
			isa = PBXGroup;
			children = (
				4F7AAD61AA30AD7D54F741FF4BDB9AAD /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		BE03A93A0C302A4E5B3DE5B2530CA709 /* .. */ = {
			isa = PBXGroup;
			children = (
				4329D1888D0D0223735D2D8620746F5B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		BEA33F17AD55EECDF7D0806471261577 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				51533C3EF6134E91064DD1BFB10FB857 /* darwin */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		BF61539143D9AEFBDFF897E3977F813E /* .. */ = {
			isa = PBXGroup;
			children = (
				E322B7CD9EBDF49E1A0F11981AF6651E /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources";
			sourceTree = "<group>";
		};
		BFA76EE1998A2A2D08ADF656BB211146 /* Documents */ = {
			isa = PBXGroup;
			children = (
				1747768C59E24CB8128E9D5F529DF49C /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		C011B5551488AACE923605DDBFA1903D /* integration_test */ = {
			isa = PBXGroup;
			children = (
				3E280F8FEC4490389C2AECB33DA3BFF3 /* FLTIntegrationTestRunner.m */,
				DEE2F5B090C5D6DDA1DD6562CE4AAC4C /* IntegrationTestIosTest.m */,
				2CD76F83AD70324BC04BF2474EF468E2 /* IntegrationTestPlugin.m */,
				2624EEB4A5366988C39A38971FE20D3C /* include */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		C10CD1CF1913DD0292C507DD4724B8BA /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				8FEC76214DD5278C57F12E0B03D77F52 /* Resources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		C8896643047E680F8B55B011E62D5D2D /* .. */ = {
			isa = PBXGroup;
			children = (
				4C9E5F489AFFAA32F2113190CF6E1117 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C9350318B8E0C7EA520976363C847ADB /* manish */ = {
			isa = PBXGroup;
			children = (
				30181493885F0435CB8B88C37F8CF02E /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		CB8D35FE7C3C086DDE4B0072C2956A98 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				4584D3A74B4FA13CA70F9AA93B38A384 /* Flutter.debug.xcconfig */,
				C296E83A780806BA6A7A0028BAFCD1A8 /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		CE0ED29FFFB82E5C63A226C049001AF4 /* Classes */ = {
			isa = PBXGroup;
			children = (
				F50B23F86D98ED1B90A057DDDF120C6E /* ImageGallerySaverPlugin.h */,
				7CE2766C4E5E5DA23C524C5818663B17 /* ImageGallerySaverPlugin.m */,
				C5346383D8B1E71918D865AE34A3B24E /* SwiftImageGallerySaverPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				0029577BA61E58AFB48B152F6BBF1F14 /* Development Pods */,
				1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */,
				0955B41906BF05451ED22D9DAFC3F031 /* Products */,
				791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D25B7356A6AD5284B4769EB461997343 /* .. */ = {
			isa = PBXGroup;
			children = (
				AE600A32AC5359B72604C3C7692A30E4 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D2D2504128AE15DE5E624BBF25828713 /* manish */ = {
			isa = PBXGroup;
			children = (
				4ED2634FF3DAF5CE53F6C473521139EC /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		D3364FE9923C146C0FAB1B0A3D12D25F /* video_player_avfoundation_ios */ = {
			isa = PBXGroup;
			children = (
				692FD6B5D244355A91AEA08351791DF6 /* FVPNativeVideoView.m */,
			);
			name = video_player_avfoundation_ios;
			path = video_player_avfoundation_ios;
			sourceTree = "<group>";
		};
		D7E98F60412E9B1183DCC9EA478AC661 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				F55587E989AA72936AF42D1369BCB053 /* Resources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		DD5C638237EAE9B530B601A6B39EF085 /* plugins */ = {
			isa = PBXGroup;
			children = (
				F3D81E0E396506D674E7B98DEB58EFE1 /* camera_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		DDFD6FCB0009B24C7E8722F256B29CA8 /* .. */ = {
			isa = PBXGroup;
			children = (
				8009E6C9B710A59B69A6B2F702F92E2B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		DE45C3D3A3D111531995557E681B6F49 /* camera */ = {
			isa = PBXGroup;
			children = (
				8C077CDA292F86C1D99281D88A9DAD18 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		E0E2793D3755DD1FFF289DD2F4A36416 /* .. */ = {
			isa = PBXGroup;
			children = (
				BE03A93A0C302A4E5B3DE5B2530CA709 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios";
			sourceTree = "<group>";
		};
		E27716DB894227347C88D3FFEE90A88F /* .. */ = {
			isa = PBXGroup;
			children = (
				A7E47647A5FB6631D9EDA07F0588B4AB /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E322B7CD9EBDF49E1A0F11981AF6651E /* .. */ = {
			isa = PBXGroup;
			children = (
				0141E1EBC7E70A4AA92CE047BDA99C75 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E34247966341205A218709A5588FE9DD /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E6D10824EDE0574C644F91B50DDBF526 /* image_gallery_saver.modulemap */,
				3AA4A4BD8EE526337B7AE03CC6DF728D /* image_gallery_saver-dummy.m */,
				4C09BE5BAEEEFB61F2391B80EDD16474 /* image_gallery_saver-Info.plist */,
				5991603B4BC1CA8574431B64361E4A28 /* image_gallery_saver-prefix.pch */,
				9F6AEA83843221B953AA23ED567E961B /* image_gallery_saver-umbrella.h */,
				AE8A5DDAEB60AA65BC8BAD5A0E83EAAF /* image_gallery_saver.debug.xcconfig */,
				F31A22A66D76A6C601387422E7EDD33F /* image_gallery_saver.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/image_gallery_saver";
			sourceTree = "<group>";
		};
		E415DEFE33D466E1446FD894C92860AF /* plugins */ = {
			isa = PBXGroup;
			children = (
				BEA33F17AD55EECDF7D0806471261577 /* video_player_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		E5AA723C618BA03169A7B9EB848BF7A0 /* Documents */ = {
			isa = PBXGroup;
			children = (
				AC3100F1E95DFAF73DBF52F0E9F7EBFC /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		E718C165B858F59E51986BC3D8EEE755 /* Sources */ = {
			isa = PBXGroup;
			children = (
				41AD4FB47FBB40F842CF468A91A373A8 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		E7458EE97BFA05F3F4298110AC9481A8 /* .. */ = {
			isa = PBXGroup;
			children = (
				E27716DB894227347C88D3FFEE90A88F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E9D30211E650D9F0048945716394A4A0 /* ios */ = {
			isa = PBXGroup;
			children = (
				775083D937A9740FD33A41F0BC7E0BE0 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		EDA1B31E807433D72418A4F5F93091E6 /* ios */ = {
			isa = PBXGroup;
			children = (
				9D1B7D621F76C86E75AA1005F316FC8C /* camera_avfoundation */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		EDA5778D83F5F4DBDDA3F8E6BC4C0FCC /* .. */ = {
			isa = PBXGroup;
			children = (
				B0F2E69CD4B1861C3117024E80B3BDF1 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		EE376F7C23550D725178C08C1FB3B3DA /* .. */ = {
			isa = PBXGroup;
			children = (
				E7458EE97BFA05F3F4298110AC9481A8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F031C4F906A02B585CC2FB05A1AAC734 /* .. */ = {
			isa = PBXGroup;
			children = (
				89B393D3DF61CF713931384740DCB59F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F2C380166144422575B6F1522606E8C7 /* manish */ = {
			isa = PBXGroup;
			children = (
				8639C13EE7035F031CAA248EEED58A33 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		F3258E886ABD6293D3D25CE4F43EE6EE /* Pod */ = {
			isa = PBXGroup;
			children = (
				572856B2892D765D43A9C1B8ABF1F202 /* LICENSE */,
				E7A8A68F328FFCB98FE18C3CF23A6437 /* video_player_avfoundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		F330CEF6F70D9D6E813035D4CBD22309 /* Sources */ = {
			isa = PBXGroup;
			children = (
				313FBAFA9E65837188A157CF21D04596 /* video_player_avfoundation */,
				D3364FE9923C146C0FAB1B0A3D12D25F /* video_player_avfoundation_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		F347F3B6D8988C33C664B8C70C3D9225 /* Pod */ = {
			isa = PBXGroup;
			children = (
				28D3D64A654C934144934C75D11BCF6A /* image_gallery_saver.podspec */,
				2CABD4E331501D4EF054E0CA8D0C592E /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		F3D81E0E396506D674E7B98DEB58EFE1 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				EDA1B31E807433D72418A4F5F93091E6 /* ios */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		F47ECAB1DDB4BB886947F91B36523D0F /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				8E5CBD08C9798394DE25E4482A0147BF /* ios */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		F55587E989AA72936AF42D1369BCB053 /* Resources */ = {
			isa = PBXGroup;
			children = (
				CC038C6A6C53B34D65D0CE192F4D055B /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		F5A09BEF101B971CBF9FD618B5903FAE /* Support Files */ = {
			isa = PBXGroup;
			children = (
				657CC89D9B48DA60004D6D5270FBA32A /* path_provider_foundation.modulemap */,
				78EB4A3A81F2568C65C5CD92E1B6902A /* path_provider_foundation-dummy.m */,
				1F3A3343D0A2FF744BA013B182B27DB1 /* path_provider_foundation-Info.plist */,
				E68584642EF5A75794112EE75CAE3684 /* path_provider_foundation-prefix.pch */,
				A656D252EE8A33851AE3F75639E7FEAF /* path_provider_foundation-umbrella.h */,
				6E532366679B0BE20CE5FDB3A23F86F2 /* path_provider_foundation.debug.xcconfig */,
				9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */,
				590EEB5031AA52D68AAB3E757570DD8C /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/path_provider_foundation";
			sourceTree = "<group>";
		};
		F71345CA7251D2A6D0AFB0AC8C70CCA4 /* camera */ = {
			isa = PBXGroup;
			children = (
				A4F91F176F681815E7CDF49339E251FA /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		FDCEF58B1BE4E4667323018342965A56 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				4E23AE80F1E0F34B9DDC4EB8CAA0482A /* Sources */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		3D9AFEF292F8A2CA6456DF53DB58B3E7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				F43266D8FAE1091DB24817B71D8BDEA9 /* image_gallery_saver-umbrella.h in Headers */,
				F8DDDFED04D4D9FC484571373E143869 /* ImageGallerySaverPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40F079814914F2DA54591823962D2DFD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				F4BFCDB723DF34139FACCAC17F8BD259 /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		48A7ED21957452002067E9FFAB790DEC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				3C8E01595CF33B68A637AD93FA4978CF /* camera_avfoundation.h in Headers */,
				E72011D855D5B3288771B3D0403FF69C /* camera_avfoundation-umbrella.h in Headers */,
				03A083048C780CBF8BC0B604A364D23A /* CameraProperties.h in Headers */,
				FFCA2EDDBD48826E1468E93BFAB04C81 /* FLTAssetWriter.h in Headers */,
				FC51F2ABDBE1E8E1782DD958A1541611 /* FLTCam.h in Headers */,
				D228C2C1BCE91E70A77EB0A0A5471759 /* FLTCam_Test.h in Headers */,
				B9F642F64F494665E7B60843F0EEDBC1 /* FLTCamConfiguration.h in Headers */,
				BBB6C6DB17FF457BACD35352E23728AB /* FLTCameraDeviceDiscovering.h in Headers */,
				306A8154EC3A848B1E8C2C8CCF4C4370 /* FLTCameraPermissionManager.h in Headers */,
				EF4733E951BBE732D3315A2B698CB951 /* FLTCamMediaSettingsAVWrapper.h in Headers */,
				B9BB08B99B2081914777477B372F8263 /* FLTCaptureConnection.h in Headers */,
				93F3B9616434A0926600C441C08115E2 /* FLTCaptureDevice.h in Headers */,
				4B597D76EB7705A3031CDA82C8BCF7D8 /* FLTCaptureDeviceFormat.h in Headers */,
				B6E7879E6847184C425BA5E681E1370F /* FLTCaptureOutput.h in Headers */,
				409E4C13C57C5485ADD48990495351C2 /* FLTCapturePhotoOutput.h in Headers */,
				D004FC2462D5BDD5EB9C5CA668AEFA42 /* FLTCaptureSession.h in Headers */,
				D806DB15B8D531788D1BD8227BA481B7 /* FLTCaptureVideoDataOutput.h in Headers */,
				480B16C831F07F86A754E83AC08624BA /* FLTDeviceOrientationProviding.h in Headers */,
				57D55E0DEE5B778AAB39E4D7C589388D /* FLTEventChannel.h in Headers */,
				C8AE936F1BB6B30D309F27CE84CC8CBD /* FLTFormatUtils.h in Headers */,
				5483203B538ADCA16734DF0775985CB8 /* FLTImageStreamHandler.h in Headers */,
				83FEF39BCEA5BAF40E139751B32CF026 /* FLTPermissionServicing.h in Headers */,
				74CA127F589CFDE980529C1FD2DDE7D2 /* FLTSavePhotoDelegate.h in Headers */,
				D26EA3617436F7A1604DA5B8006FB30D /* FLTSavePhotoDelegate_Test.h in Headers */,
				E9790B1F7E77B14A0E3A4219B364626F /* FLTThreadSafeEventChannel.h in Headers */,
				8592D3BA6B16769F721129D5D5B4B671 /* FLTWritableData.h in Headers */,
				84004623E384C5AA7874121C30C962DE /* messages.g.h in Headers */,
				BF7FB6F8D1350C2ED36FAB8B83B9EA17 /* QueueUtils.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		861911D29B94589BAEFEDB02CCDC856A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				C1893AFEE2F741B3177B07FF3FFB7A8A /* AVAssetTrackUtils.h in Headers */,
				3B7EAE9E43B19CA3CAE87688F63DD31E /* FVPAVFactory.h in Headers */,
				0C95D39F4B6E53824CC6EC13BCDDEE2D /* FVPDisplayLink.h in Headers */,
				21BD83C33CA9734F112FDAA9C4E42D9C /* FVPEventBridge.h in Headers */,
				18441E89026621F065FD728737ED4CD1 /* FVPFrameUpdater.h in Headers */,
				2BA8F29AF420369EA6922201C5A60DDB /* FVPNativeVideoView.h in Headers */,
				B80EAC8BF0B980336C6E7ED3E0D4E0DB /* FVPNativeVideoViewFactory.h in Headers */,
				02E42A5A466B3E89BFBDC7238104BA0C /* FVPTextureBasedVideoPlayer.h in Headers */,
				1FD558625959E27D7AC35564029736D0 /* FVPTextureBasedVideoPlayer_Test.h in Headers */,
				7B080D6C6F61953AE66DF6A261D84F92 /* FVPVideoEventListener.h in Headers */,
				01CBD73509ECFF3BC31C5BB2BE9CCF4C /* FVPVideoPlayer.h in Headers */,
				452D8EE840CEFF02AD1C0FE930BBC5C9 /* FVPVideoPlayer_Internal.h in Headers */,
				620F968F698B50F0E4084992CDC22F08 /* FVPVideoPlayerPlugin.h in Headers */,
				AC6A99F7C15692400B388F737EA304CC /* FVPVideoPlayerPlugin_Test.h in Headers */,
				9C748B0EF7DC986CB78AEEF8D52616D4 /* FVPViewProvider.h in Headers */,
				CBD57765A1D4F65E34B0A6840CD82007 /* messages.g.h in Headers */,
				03B9D4CEBEC8F075594F2FFC32F406B3 /* video_player_avfoundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F4ADE2337A6718C5688929BDDE859230 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				E240BE0B8D01F2875E4D9613A26BD33D /* FLTIntegrationTestRunner.h in Headers */,
				D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */,
				54C1A725399979F0383179618D742D2D /* IntegrationTestIosTest.h in Headers */,
				CB2EB908281BFF841551013B778FAF4A /* IntegrationTestPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FB10A71DA24C58216A426824478FC86C /* Build configuration list for PBXNativeTarget "camera_avfoundation" */;
			buildPhases = (
				48A7ED21957452002067E9FFAB790DEC /* Headers */,
				E88DAE69939AB076E3D1DA1D4E45A9F6 /* Sources */,
				556D27E7BFC2BEC2841B5B9A9E5A9ACF /* Frameworks */,
				2C98FA4B81E5EC9C1221A578F54B876F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				163D5F12111502F75A69C21A60631760 /* PBXTargetDependency */,
				870DE5E3ECD35D96B6FB7C92BD668408 /* PBXTargetDependency */,
			);
			name = camera_avfoundation;
			productName = camera_avfoundation;
			productReference = AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */;
			productType = "com.apple.product-type.framework";
		};
		31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6E2F2E376C5D8C0931633C8A50A29E3C /* Build configuration list for PBXNativeTarget "video_player_avfoundation-video_player_avfoundation_privacy" */;
			buildPhases = (
				2B7169CFF44887A78DDD591B8F439774 /* Sources */,
				4FE4925632639DBA407BB6D4231D6498 /* Frameworks */,
				5AD4D23561C4C1273FD0771AD7FBE128 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "video_player_avfoundation-video_player_avfoundation_privacy";
			productName = video_player_avfoundation_privacy;
			productReference = 337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 167BDBB4761BC473F1DA7B197AE5EF8F /* Build configuration list for PBXNativeTarget "image_gallery_saver" */;
			buildPhases = (
				3D9AFEF292F8A2CA6456DF53DB58B3E7 /* Headers */,
				9C50247F7DFD6C147327089096B45D79 /* Sources */,
				DB6B120BD47FC66302FFF63D264FB73B /* Frameworks */,
				3B08ECE3C7649373663ED208236564E6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				42AC02F5645FD200E3539619A65D8F76 /* PBXTargetDependency */,
			);
			name = image_gallery_saver;
			productName = image_gallery_saver;
			productReference = 4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */;
			productType = "com.apple.product-type.framework";
		};
		56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */;
			buildPhases = (
				3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */,
				05895E3E1AC56002880CEF9A9AC4086F /* Sources */,
				2C51CA6EC745ACB63E424757C710B36C /* Frameworks */,
				D412229AEE091A9D36A8553E8AA56B31 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				29B1446DE588396CD0D524317E70BFFF /* PBXTargetDependency */,
				7C9985CFF70FB0FDFE599E3A35BA8FC1 /* PBXTargetDependency */,
			);
			name = path_provider_foundation;
			productName = path_provider_foundation;
			productReference = AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */;
			productType = "com.apple.product-type.framework";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 67AB70512C87177AC8400722C99405A6 /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				40F079814914F2DA54591823962D2DFD /* Headers */,
				9101643129C66FFA279596DF4DFE9063 /* Sources */,
				AFE33A2760FC3EF68E47CAC8096CBFB7 /* Frameworks */,
				CACBBC1F2C5D612C0D1E7B9DDBB0416E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1072200AFBA68277765ED0605A74126B /* PBXTargetDependency */,
				1A243742F04ADE409AD1368F62830807 /* PBXTargetDependency */,
				54B20E0F218D44ACF023DC6AC3362E1B /* PBXTargetDependency */,
				B4090463E1351227F1BB28837CFC36FF /* PBXTargetDependency */,
				E7C1429F5E2A24039BCAEFBF1412ED34 /* PBXTargetDependency */,
				6748ECC3E4F807FB7FC5B029A0B1769C /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0A15713BF351C15ABDA9FD1BD2692140 /* Build configuration list for PBXNativeTarget "video_player_avfoundation" */;
			buildPhases = (
				861911D29B94589BAEFEDB02CCDC856A /* Headers */,
				CE74C95B22AC89DFBE9C307A3C33B957 /* Sources */,
				81FD8C60300A3062D2FDDB1533F5A919 /* Frameworks */,
				E9A91F4D1FB36E68C88CEFD7C2662E4B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B2F405FAF2CC097FBEF69B2EF2607E25 /* PBXTargetDependency */,
				25D2E82391C2A2899CD9330FA0F0BEA2 /* PBXTargetDependency */,
			);
			name = video_player_avfoundation;
			productName = video_player_avfoundation;
			productReference = F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */;
			productType = "com.apple.product-type.framework";
		};
		ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */;
			buildPhases = (
				F4ADE2337A6718C5688929BDDE859230 /* Headers */,
				34385A7E8FBAE22793A2738188560FDF /* Sources */,
				D37529713F6219C15572CE565F23780E /* Frameworks */,
				65794A8FCF723FDAECE2B48E7537684A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				03A3A7D03EF94E25B6CF68CF0D46BB39 /* PBXTargetDependency */,
			);
			name = integration_test;
			productName = integration_test;
			productReference = 5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */;
			productType = "com.apple.product-type.framework";
		};
		CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A6167E55EE7E59A4566441695D2EC979 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */;
			buildPhases = (
				AA4175305CA41398568B6E575C29D537 /* Sources */,
				890633AA2AFB403BE31573A4F8EC6F2A /* Frameworks */,
				26FD06FC3F71CFAA8299A03012D6DE68 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "path_provider_foundation-path_provider_foundation_privacy";
			productName = path_provider_foundation_privacy;
			productReference = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B3DD0FBE93EBF6E013BF6C1826020B0B /* Build configuration list for PBXNativeTarget "camera_avfoundation-camera_avfoundation_privacy" */;
			buildPhases = (
				9FC1DADFD0080939CD9FEFE47EBD5C9C /* Sources */,
				27D9C97B47293802041F7C7AFBCCC6AA /* Frameworks */,
				B4D7097166EAEE179DF7C5571BA51BAD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "camera_avfoundation-camera_avfoundation_privacy";
			productName = camera_avfoundation_privacy;
			productReference = F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 0955B41906BF05451ED22D9DAFC3F031 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */,
				D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */,
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */,
				ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */,
				56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */,
				CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */,
				31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		26FD06FC3F71CFAA8299A03012D6DE68 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5817432C68EA5EF661E8A907428B0E18 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C98FA4B81E5EC9C1221A578F54B876F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9B9717221A43BD8DB1BFF7673FE8E871 /* camera_avfoundation-camera_avfoundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B08ECE3C7649373663ED208236564E6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5AD4D23561C4C1273FD0771AD7FBE128 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				869631B36F8C61EB5425E582721D4D20 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		65794A8FCF723FDAECE2B48E7537684A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B4D7097166EAEE179DF7C5571BA51BAD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C26516EDCD883DF759ED69F4D5637326 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CACBBC1F2C5D612C0D1E7B9DDBB0416E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D412229AEE091A9D36A8553E8AA56B31 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E9A91F4D1FB36E68C88CEFD7C2662E4B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C7CAFABA0A77A333BE575FC1A22B1E0D /* video_player_avfoundation-video_player_avfoundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		05895E3E1AC56002880CEF9A9AC4086F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				73427689B342C9A90D275E8BBC38E498 /* messages.g.swift in Sources */,
				E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */,
				875A2A0D4875DA41E3223F8EE8206152 /* PathProviderPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B7169CFF44887A78DDD591B8F439774 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		34385A7E8FBAE22793A2738188560FDF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D90221E29BE7FABD5A6260A677EF217C /* FLTIntegrationTestRunner.m in Sources */,
				8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */,
				36D53000D376A354E6DF0DB75906EA79 /* IntegrationTestIosTest.m in Sources */,
				BEF37003673BC8C426D6A87DB7C4065C /* IntegrationTestPlugin.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9101643129C66FFA279596DF4DFE9063 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3949E86B1BE43619C0F93DEDE78266F5 /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C50247F7DFD6C147327089096B45D79 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				882656F1F7EDA5B1D1D29D160534C190 /* image_gallery_saver-dummy.m in Sources */,
				70D3654AA3300267D29EC5F48B6FC560 /* ImageGallerySaverPlugin.m in Sources */,
				4723E47C3A13863E84C6F94BEDF473BE /* SwiftImageGallerySaverPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9FC1DADFD0080939CD9FEFE47EBD5C9C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AA4175305CA41398568B6E575C29D537 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE74C95B22AC89DFBE9C307A3C33B957 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D1446FD083744FBAB9E078E5A8EB38B9 /* AVAssetTrackUtils.m in Sources */,
				7DAB0FECB69FA62231B48023413B13F3 /* FVPAVFactory.m in Sources */,
				71366A5094E48AD1F2D29BA3D94FFAB9 /* FVPCADisplayLink.m in Sources */,
				C405BC028C4CBAAA7ACE50D3D33B1F6B /* FVPEventBridge.m in Sources */,
				12219F8E744420BD5AB117EF75F36E23 /* FVPFrameUpdater.m in Sources */,
				B899DDDB5C10C59A638E73C4F30C2E8E /* FVPNativeVideoView.m in Sources */,
				A4CD59CF4F0FE09EEC1E982272B21FD9 /* FVPNativeVideoViewFactory.m in Sources */,
				5A7F034D4B282F6A1CBB4D59A983B252 /* FVPTextureBasedVideoPlayer.m in Sources */,
				1BCE1839A3F7F738AD9BB2A248BC513B /* FVPVideoPlayer.m in Sources */,
				E9B20BD2AEC00D6E6403104891B3BD58 /* FVPVideoPlayerPlugin.m in Sources */,
				2079EA2588F437AE5B5DBF480639838B /* FVPViewProvider.m in Sources */,
				82D9E3FE965E3414C2DD3AE8BF1C2BD2 /* messages.g.m in Sources */,
				6536FF32D002043A333C3EB64B3DCA89 /* video_player_avfoundation-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E88DAE69939AB076E3D1DA1D4E45A9F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				351AC965BF44984D6E225ED347912561 /* Camera.swift in Sources */,
				7F351626BE5B7CA9303C04C0A8F25031 /* camera_avfoundation-dummy.m in Sources */,
				813C6AEB824110C60B8D98B7FA221B70 /* CameraPlugin.swift in Sources */,
				049D0B5938C4EAECAAB6AEBCBF16006A /* CameraProperties.m in Sources */,
				06B394B97CCE583F6882B1102013A28C /* DefaultCamera.swift in Sources */,
				2B32705F11377CAC6C8678C341103E53 /* FLTAssetWriter.m in Sources */,
				C964E35A6D72ED7ECEBA91E607A828F7 /* FLTCam.m in Sources */,
				B8B43B2E145F8850F52EA02CDAAFFAED /* FLTCamConfiguration.m in Sources */,
				4392CBA4D2AD75B69AC6AE946B7D04A6 /* FLTCameraDeviceDiscovering.m in Sources */,
				2695AA2D706BABA00D80D7AAF0BAAFE0 /* FLTCameraPermissionManager.m in Sources */,
				B16C4147A69E0088076A80A73277F4DA /* FLTCamMediaSettingsAVWrapper.m in Sources */,
				AFA8A38AC1D494ACE56DE9C7B363AE53 /* FLTCaptureConnection.m in Sources */,
				9957A3C6D1C945E94E4CD46871081DB5 /* FLTCaptureDevice.m in Sources */,
				340B843A2A0B0F262093208609DFB359 /* FLTCaptureDeviceFormat.m in Sources */,
				D1508918E22EBF4D9DAA6ACF9DDDE092 /* FLTCapturePhotoOutput.m in Sources */,
				6975110905151D5F3245A0E9C98E4700 /* FLTCaptureSession.m in Sources */,
				8C3070272E95A2C4984F2E562E0671C8 /* FLTCaptureVideoDataOutput.m in Sources */,
				D89384702678F7189703452E6543BCE9 /* FLTDeviceOrientationProviding.m in Sources */,
				16623A4DCFEF3FBB95D9867F8C354552 /* FLTFormatUtils.m in Sources */,
				8FFEDA1B4F9133249B33B9C706E41CCB /* FLTImageStreamHandler.m in Sources */,
				1B5990F8256644B633A24CE4791B7F25 /* FLTPermissionServicing.m in Sources */,
				BB85A52D2EB5F8E18E58B2A2A0AA30C6 /* FLTSavePhotoDelegate.m in Sources */,
				BEB688201E1B7583B9B281D8625ECF15 /* FLTThreadSafeEventChannel.m in Sources */,
				26EA0D20C737C77BAAB5AAA54543EA9C /* FLTWritableData.m in Sources */,
				8BBF4F70DC31CCA3238BFE7CA43F5545 /* messages.g.m in Sources */,
				3DCDD529F97405FF13C0BEBD2B6D956A /* QueueUtils.m in Sources */,
				875030AD692222B0DF5BD9C991C32896 /* QueueUtils.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		03A3A7D03EF94E25B6CF68CF0D46BB39 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 5DAE7D626BF74A129A22541320BD19A2 /* PBXContainerItemProxy */;
		};
		1072200AFBA68277765ED0605A74126B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = E089E0011456E943E24F90455600C733 /* PBXContainerItemProxy */;
		};
		163D5F12111502F75A69C21A60631760 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 1DE2ED6987F5E85B3D3804E670962991 /* PBXContainerItemProxy */;
		};
		1A243742F04ADE409AD1368F62830807 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = camera_avfoundation;
			target = 0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */;
			targetProxy = 2E754F62021980EAB3B7663AE809B092 /* PBXContainerItemProxy */;
		};
		25D2E82391C2A2899CD9330FA0F0BEA2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "video_player_avfoundation-video_player_avfoundation_privacy";
			target = 31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */;
			targetProxy = A961AD5433FD66C9F4DBBC964C29F0A3 /* PBXContainerItemProxy */;
		};
		29B1446DE588396CD0D524317E70BFFF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 21A0432F8FB94D31224B8812E2450770 /* PBXContainerItemProxy */;
		};
		42AC02F5645FD200E3539619A65D8F76 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 128BEF0D22B75C17245D38E465FF8F1E /* PBXContainerItemProxy */;
		};
		54B20E0F218D44ACF023DC6AC3362E1B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = image_gallery_saver;
			target = 37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */;
			targetProxy = 8061D6BE20BF222C6AAA186C516DFA71 /* PBXContainerItemProxy */;
		};
		6748ECC3E4F807FB7FC5B029A0B1769C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = video_player_avfoundation;
			target = AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */;
			targetProxy = 93D6797264E3A2BA204043DE4530B0B1 /* PBXContainerItemProxy */;
		};
		7C9985CFF70FB0FDFE599E3A35BA8FC1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "path_provider_foundation-path_provider_foundation_privacy";
			target = CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */;
			targetProxy = 3D8711CDAAF20DAE8C1E4D8A9A1EA8E7 /* PBXContainerItemProxy */;
		};
		870DE5E3ECD35D96B6FB7C92BD668408 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "camera_avfoundation-camera_avfoundation_privacy";
			target = D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */;
			targetProxy = 08F4CDB79E582A555C5C45CD4677C11E /* PBXContainerItemProxy */;
		};
		B2F405FAF2CC097FBEF69B2EF2607E25 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 596F09724DE84B9DE634687494D95869 /* PBXContainerItemProxy */;
		};
		B4090463E1351227F1BB28837CFC36FF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = integration_test;
			target = ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */;
			targetProxy = FC3A2F95835FB84F020634EFACBE2BD3 /* PBXContainerItemProxy */;
		};
		E7C1429F5E2A24039BCAEFBF1412ED34 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = path_provider_foundation;
			target = 56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */;
			targetProxy = A3FFA79B4A88E42982DD34BEF88866B1 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		024347E67FAA97D7D5743263CC06C593 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		0316CA68318A621B67362408814959DE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		06CD0D42F178A65398B7329338EA368F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F31A22A66D76A6C601387422E7EDD33F /* image_gallery_saver.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		074D7DAD2C1BC728D5B882E02AAF0BC5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		160DD2451306A828BBCCF4CA0D119552 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1DF60998696FCBBD5D586032EB312CC6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 96929D15A9E9CCC603BF3911B78707AA /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2D84B954EBBA74B2316C66732A780B5A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		2E4879C40540997FD810F484BD8FDDD9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		35222BE8B4B51713EEBEEFCEA83CC6C7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		386AC04547EE6FE3A4080B5C256377EE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 96929D15A9E9CCC603BF3911B78707AA /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		3B00BD1E0C4CA3B5A99A4E69A5E48A17 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6E532366679B0BE20CE5FDB3A23F86F2 /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		6EFF3F08BBDF81109A15EB2A21D52E86 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4584D3A74B4FA13CA70F9AA93B38A384 /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		74E9A7E8B691EA223F37284885A05062 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79AF209439A6073BB2DAD9C81E658B61 /* video_player_avfoundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C296E83A780806BA6A7A0028BAFCD1A8 /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		7FDDA1236F61F8674268E72F5952FB33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F31A22A66D76A6C601387422E7EDD33F /* image_gallery_saver.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		8CADB4D20DEF912C4436DF8F4D2D2488 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9071509935D57DFD930C90AFC3ECE83F /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		A0FB6248D51C59C63D4FC01C0BCCDD57 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		A1202C59D5272B0DEE585943D867C807 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6E532366679B0BE20CE5FDB3A23F86F2 /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A685032006E8C65ABB32F4C5BEFA0D82 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		A91E28FC0F3F51EB26375F3B0B1AC032 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AE8A5DDAEB60AA65BC8BAD5A0E83EAAF /* image_gallery_saver.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		AC731DF571FBB891306E00A80F85F9E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D3DF8C698976AC3F6BB132B3BA96C3E /* camera_avfoundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		B19F338034A241F06CE78582A345D8C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4FA60FD1EE2D268521905E7104813844 /* integration_test.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		BBF92DCCE4818C530349988CC9707B51 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EF6A846F9908E7094F277BBD4B36DD4 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		BD5D163E35C9DD27F002B90013935E1A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D3DF8C698976AC3F6BB132B3BA96C3E /* camera_avfoundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C35A2A94EEC48A545FD3DEDC2AAA914E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D026EFFC32440701C84DA5012CFCEF4E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79AF209439A6073BB2DAD9C81E658B61 /* video_player_avfoundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D5C8AA59A339A498993C18BB2506FA79 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B89B61850E6787E629DB00DF9B92165E /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C296E83A780806BA6A7A0028BAFCD1A8 /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0A15713BF351C15ABDA9FD1BD2692140 /* Build configuration list for PBXNativeTarget "video_player_avfoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D026EFFC32440701C84DA5012CFCEF4E /* Debug */,
				2D84B954EBBA74B2316C66732A780B5A /* Profile */,
				D5C8AA59A339A498993C18BB2506FA79 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		167BDBB4761BC473F1DA7B197AE5EF8F /* Build configuration list for PBXNativeTarget "image_gallery_saver" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A91E28FC0F3F51EB26375F3B0B1AC032 /* Debug */,
				06CD0D42F178A65398B7329338EA368F /* Profile */,
				7FDDA1236F61F8674268E72F5952FB33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		67AB70512C87177AC8400722C99405A6 /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6EFF3F08BBDF81109A15EB2A21D52E86 /* Debug */,
				A0FB6248D51C59C63D4FC01C0BCCDD57 /* Profile */,
				C35A2A94EEC48A545FD3DEDC2AAA914E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6E2F2E376C5D8C0931633C8A50A29E3C /* Build configuration list for PBXNativeTarget "video_player_avfoundation-video_player_avfoundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				74E9A7E8B691EA223F37284885A05062 /* Debug */,
				A685032006E8C65ABB32F4C5BEFA0D82 /* Profile */,
				024347E67FAA97D7D5743263CC06C593 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A6167E55EE7E59A4566441695D2EC979 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B00BD1E0C4CA3B5A99A4E69A5E48A17 /* Debug */,
				8CADB4D20DEF912C4436DF8F4D2D2488 /* Profile */,
				074D7DAD2C1BC728D5B882E02AAF0BC5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3DD0FBE93EBF6E013BF6C1826020B0B /* Build configuration list for PBXNativeTarget "camera_avfoundation-camera_avfoundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AC731DF571FBB891306E00A80F85F9E4 /* Debug */,
				160DD2451306A828BBCCF4CA0D119552 /* Profile */,
				35222BE8B4B51713EEBEEFCEA83CC6C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B19F338034A241F06CE78582A345D8C2 /* Debug */,
				386AC04547EE6FE3A4080B5C256377EE /* Profile */,
				1DF60998696FCBBD5D586032EB312CC6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FB10A71DA24C58216A426824478FC86C /* Build configuration list for PBXNativeTarget "camera_avfoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BD5D163E35C9DD27F002B90013935E1A /* Debug */,
				2E4879C40540997FD810F484BD8FDDD9 /* Profile */,
				BBF92DCCE4818C530349988CC9707B51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1202C59D5272B0DEE585943D867C807 /* Debug */,
				0316CA68318A621B67362408814959DE /* Profile */,
				162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
