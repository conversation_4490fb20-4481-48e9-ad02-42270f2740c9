// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		01CBD73509ECFF3BC31C5BB2BE9CCF4C /* FVPVideoPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = D9153901AD83C1C50F2A87D9206A1267 /* FVPVideoPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		01CD338AFFBD44FFE46C889146E13F94 /* FLTImagePickerMetaDataUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = AB2BDAC3E94CDFEEF2D0D1EB269B7E55 /* FLTImagePickerMetaDataUtil.m */; };
		022C23F1E61018961E323F39E5C156E3 /* FLTImagePickerPhotoAssetUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = A822936B5DC52D708D0E9633057C68B8 /* FLTImagePickerPhotoAssetUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		02E42A5A466B3E89BFBDC7238104BA0C /* FVPTextureBasedVideoPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = FD13E98B1A7661D87C7ED46E5FDF3B62 /* FVPTextureBasedVideoPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		030E9CC53C0A2C293E1316DF96C82228 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		03A083048C780CBF8BC0B604A364D23A /* CameraProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = DD7749036F4F136E7B33C3F6A86ED968 /* CameraProperties.h */; settings = {ATTRIBUTES = (Public, ); }; };
		03B9D4CEBEC8F075594F2FFC32F406B3 /* video_player_avfoundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 5A797474E4CD782D2652F8D942F03F80 /* video_player_avfoundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		049D0B5938C4EAECAAB6AEBCBF16006A /* CameraProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = EE70B09FD24AFCD41A898AE0D393DC8A /* CameraProperties.m */; };
		06B394B97CCE583F6882B1102013A28C /* DefaultCamera.swift in Sources */ = {isa = PBXBuildFile; fileRef = 53A1FCCA7D7ED0C729A593BC350BB083 /* DefaultCamera.swift */; };
		083602A0483FE697CDEF80E2F8FF3CD1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 9B1758A882B06B5CFA37ABEF2D1AF913 /* PrivacyInfo.xcprivacy */; };
		0C95D39F4B6E53824CC6EC13BCDDEE2D /* FVPDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = BB9D01EA6F21723A68169CF9C253BDC0 /* FVPDisplayLink.h */; settings = {ATTRIBUTES = (Public, ); }; };
		11E4D6489CA8A1EF744885E92A5396A0 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0933EB9D2C63093A25E55C1BB61D6B9B /* PrivacyInfo.xcprivacy */; };
		12219F8E744420BD5AB117EF75F36E23 /* FVPFrameUpdater.m in Sources */ = {isa = PBXBuildFile; fileRef = 763DEFD08DF854D6767F7272371BE8D9 /* FVPFrameUpdater.m */; };
		14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */; };
		16623A4DCFEF3FBB95D9867F8C354552 /* FLTFormatUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 4B3882550C970A850327026D23205C64 /* FLTFormatUtils.m */; };
		18441E89026621F065FD728737ED4CD1 /* FVPFrameUpdater.h in Headers */ = {isa = PBXBuildFile; fileRef = 96F2BC6BC3827D8A945A2151F3136170 /* FVPFrameUpdater.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1B5990F8256644B633A24CE4791B7F25 /* FLTPermissionServicing.m in Sources */ = {isa = PBXBuildFile; fileRef = 92C34224083D1FE960535F19554B3A76 /* FLTPermissionServicing.m */; };
		1BCE1839A3F7F738AD9BB2A248BC513B /* FVPVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 367B74AF0CC975EB850A487090FE042B /* FVPVideoPlayer.m */; };
		1F397B25797647C55BAB87120F8F02D3 /* FLTImagePickerImageUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = F50C792ECCA9E3D7BA8815417EDC1FB6 /* FLTImagePickerImageUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1FD558625959E27D7AC35564029736D0 /* FVPTextureBasedVideoPlayer_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = C90A86B37C383D245A4BF1CBEA668146 /* FVPTextureBasedVideoPlayer_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2079EA2588F437AE5B5DBF480639838B /* FVPViewProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 8FED77CAA7E7BDC76A0320E3A370D0A9 /* FVPViewProvider.m */; };
		21BD83C33CA9734F112FDAA9C4E42D9C /* FVPEventBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 6116BFBE9B86CCD66B86E0192F899C4B /* FVPEventBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		222AC4BF833FA50116ED9CBA3B83C9DB /* FLTImagePickerImageUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 97EA34104B616F1CFA03443A5ED203B0 /* FLTImagePickerImageUtil.m */; };
		237D40559884C080FA3E567AEDB732EF /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */; };
		2467A4A50DECCA5C5158B34DDCC48971 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 0765757A0729CEED5B496EB055FC851B /* path_provider_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2695AA2D706BABA00D80D7AAF0BAAFE0 /* FLTCameraPermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 527C35E64D17E2199726BBE8E8413628 /* FLTCameraPermissionManager.m */; };
		26EA0D20C737C77BAAB5AAA54543EA9C /* FLTWritableData.m in Sources */ = {isa = PBXBuildFile; fileRef = EB188A0202B0205AC9927AF17DEF081B /* FLTWritableData.m */; };
		2810C57DC5EDF5DF9350B71CEF172EAC /* FLTImagePickerPhotoAssetUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = EF72AFF4C1CBB2509CB0C1FB59A882B8 /* FLTImagePickerPhotoAssetUtil.m */; };
		2B32705F11377CAC6C8678C341103E53 /* FLTAssetWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = 50EBFAA9AF52A1F827D2901E36E7385D /* FLTAssetWriter.m */; };
		2B5B0F628F0FE3CD032B8CA1FA3A9902 /* image_picker_ios-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6560283180DD2C7B64A3137CCDD2601F /* image_picker_ios-dummy.m */; };
		2BA8F29AF420369EA6922201C5A60DDB /* FVPNativeVideoView.h in Headers */ = {isa = PBXBuildFile; fileRef = 72F3A8334D0963BA074866223A18ADE0 /* FVPNativeVideoView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		306A8154EC3A848B1E8C2C8CCF4C4370 /* FLTCameraPermissionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 663D67A463AFAE4C1F36E0F3C592760E /* FLTCameraPermissionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		340B843A2A0B0F262093208609DFB359 /* FLTCaptureDeviceFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = C700A6972C402A1A194502533A120154 /* FLTCaptureDeviceFormat.m */; };
		351AC965BF44984D6E225ED347912561 /* Camera.swift in Sources */ = {isa = PBXBuildFile; fileRef = 722054A8C8BE1B045612760D1EF9330F /* Camera.swift */; };
		36D53000D376A354E6DF0DB75906EA79 /* IntegrationTestIosTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 345A53BEBE4D014165AA012B3DB5C855 /* IntegrationTestIosTest.m */; };
		3B7EAE9E43B19CA3CAE87688F63DD31E /* FVPAVFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = D9D56AD975B3C019AEAE91370EC11706 /* FVPAVFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C8E01595CF33B68A637AD93FA4978CF /* camera_avfoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = 1FE4610DE4689302DB9B0123864A10F4 /* camera_avfoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3DCDD529F97405FF13C0BEBD2B6D956A /* QueueUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 58E7C03092980B2BB86719555962E3F6 /* QueueUtils.m */; };
		3F197F4F335A992690C00BE47D82C02E /* image_picker_ios-image_picker_ios_privacy in Resources */ = {isa = PBXBuildFile; fileRef = F0C7EFBFF01CFAAB52BA74E6CB40CE2C /* image_picker_ios-image_picker_ios_privacy */; };
		409E4C13C57C5485ADD48990495351C2 /* FLTCapturePhotoOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A26F396610F15D00F694960D4908F03 /* FLTCapturePhotoOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4392CBA4D2AD75B69AC6AE946B7D04A6 /* FLTCameraDeviceDiscovering.m in Sources */ = {isa = PBXBuildFile; fileRef = ADA207B2C217FE3CB4F4C557BEDE6034 /* FLTCameraDeviceDiscovering.m */; };
		452D8EE840CEFF02AD1C0FE930BBC5C9 /* FVPVideoPlayer_Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = AE0DE59512D7F0280B826F47544B67C3 /* FVPVideoPlayer_Internal.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4723E47C3A13863E84C6F94BEDF473BE /* SwiftImageGallerySaverPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B25645F61A5C6EA1B46D9DADB998DA5 /* SwiftImageGallerySaverPlugin.swift */; };
		480B16C831F07F86A754E83AC08624BA /* FLTDeviceOrientationProviding.h in Headers */ = {isa = PBXBuildFile; fileRef = C24AEFD0328097CB13EDCB0FA7375C28 /* FLTDeviceOrientationProviding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4B597D76EB7705A3031CDA82C8BCF7D8 /* FLTCaptureDeviceFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E6C3AA64D5ADFDD318573ED2B884234 /* FLTCaptureDeviceFormat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5483203B538ADCA16734DF0775985CB8 /* FLTImageStreamHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = FDF5FDA80F2C5720F15F0ACC7F84FD7D /* FLTImageStreamHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		54C1A725399979F0383179618D742D2D /* IntegrationTestIosTest.h in Headers */ = {isa = PBXBuildFile; fileRef = BCFC332AF023182F2FD9136AB22969D9 /* IntegrationTestIosTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		57D55E0DEE5B778AAB39E4D7C589388D /* FLTEventChannel.h in Headers */ = {isa = PBXBuildFile; fileRef = 45CBB9B43219CDA17AE03808BDEA9B8B /* FLTEventChannel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5A7F034D4B282F6A1CBB4D59A983B252 /* FVPTextureBasedVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = B977149FC67AFE7CD7588BD42D856F30 /* FVPTextureBasedVideoPlayer.m */; };
		620F968F698B50F0E4084992CDC22F08 /* FVPVideoPlayerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 05447F5163086E0B13149DCE1CE42F70 /* FVPVideoPlayerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6536FF32D002043A333C3EB64B3DCA89 /* video_player_avfoundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 762DE57F1B6EF1710FAA8C660E3D0CF7 /* video_player_avfoundation-dummy.m */; };
		6975110905151D5F3245A0E9C98E4700 /* FLTCaptureSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 02F76617E9D30E16E4221B229FE41D1B /* FLTCaptureSession.m */; };
		70D3654AA3300267D29EC5F48B6FC560 /* ImageGallerySaverPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D13B87F745EF1ED49D0A9B99F50640C /* ImageGallerySaverPlugin.m */; };
		71366A5094E48AD1F2D29BA3D94FFAB9 /* FVPCADisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = E1F1B209D4810478F65D2A9CA330C595 /* FVPCADisplayLink.m */; };
		7187035DE343D15C1E18DD0B4D58F09D /* image_picker_ios-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = DF207E3C1BA2FF09FA0DC50118DF1E1B /* image_picker_ios-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71ABE471529580AFD720CA76711E5637 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		73427689B342C9A90D275E8BBC38E498 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFB4E732031563DC2987261F292863D8 /* messages.g.swift */; };
		74CA127F589CFDE980529C1FD2DDE7D2 /* FLTSavePhotoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 3C15DD01235D4AE95808B8D04AD5959E /* FLTSavePhotoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7A50EECBB5F23A2B2E2F0FB1627D661A /* FLTPHPickerSaveImageToPathOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 5433C219D14DF382FA87E5DA149EF9B5 /* FLTPHPickerSaveImageToPathOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7B080D6C6F61953AE66DF6A261D84F92 /* FVPVideoEventListener.h in Headers */ = {isa = PBXBuildFile; fileRef = 4811AEC7B1D7B27EFAE051A963676305 /* FVPVideoEventListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DAB0FECB69FA62231B48023413B13F3 /* FVPAVFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = D2356C73EAE2E6E7BA5E643C0C6BA0BF /* FVPAVFactory.m */; };
		7F351626BE5B7CA9303C04C0A8F25031 /* camera_avfoundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A8315EE372804831D360B08F364008A2 /* camera_avfoundation-dummy.m */; };
		813C6AEB824110C60B8D98B7FA221B70 /* CameraPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 50D130668646A6C1BEFF61C87643A8A6 /* CameraPlugin.swift */; };
		8153FAC69CE55CC686FFBB0FE3746A64 /* messages.g.m in Sources */ = {isa = PBXBuildFile; fileRef = ED0B325ED9D372DE99B5B9910BF442DD /* messages.g.m */; };
		82D9E3FE965E3414C2DD3AE8BF1C2BD2 /* messages.g.m in Sources */ = {isa = PBXBuildFile; fileRef = 3DBF1BD8632322F623B54336F52FEAC5 /* messages.g.m */; };
		83FEF39BCEA5BAF40E139751B32CF026 /* FLTPermissionServicing.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A9F14DE25449230E69056B718C8F9CB /* FLTPermissionServicing.h */; settings = {ATTRIBUTES = (Public, ); }; };
		84004623E384C5AA7874121C30C962DE /* messages.g.h in Headers */ = {isa = PBXBuildFile; fileRef = 13B959CD361CB6477C2D597CC37233FE /* messages.g.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8592D3BA6B16769F721129D5D5B4B671 /* FLTWritableData.h in Headers */ = {isa = PBXBuildFile; fileRef = DE75FE7017D984D213264E1B1D82AD63 /* FLTWritableData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		875030AD692222B0DF5BD9C991C32896 /* QueueUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 936A426C44BB048B120DB0AD21B8631E /* QueueUtils.swift */; };
		875A2A0D4875DA41E3223F8EE8206152 /* PathProviderPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BCEA2E7EB215216F40FD2EDE87750AB /* PathProviderPlugin.swift */; };
		882656F1F7EDA5B1D1D29D160534C190 /* image_gallery_saver-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C32A7708C8F926CAE59EA2565B5A336 /* image_gallery_saver-dummy.m */; };
		898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = B776F59C7D354B3149B805F9925157C8 /* integration_test-dummy.m */; };
		8A862869EB55A9015F8E29733627911C /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8BBF4F70DC31CCA3238BFE7CA43F5545 /* messages.g.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A26B067159968F9050420E87DD0BAB2 /* messages.g.m */; };
		8C3070272E95A2C4984F2E562E0671C8 /* FLTCaptureVideoDataOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = C3C7420752996B9BD05341B16B518560 /* FLTCaptureVideoDataOutput.m */; };
		8FFEDA1B4F9133249B33B9C706E41CCB /* FLTImageStreamHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E09D7433DEE1A7CEEB0CFF3863350B9 /* FLTImageStreamHandler.m */; };
		90EE2BFF038373FA1BB41486FF181F93 /* FLTImagePickerMetaDataUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 3FD8C1F0506EBAF45C521E9ABB784D18 /* FLTImagePickerMetaDataUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		914B009F5E84802D4CC5D1E7FD5AE0B4 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 53A9B6B29A8633886B44884095E67F23 /* PrivacyInfo.xcprivacy */; };
		93F3B9616434A0926600C441C08115E2 /* FLTCaptureDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 38D589792B1C4D2054874110C16F0DD7 /* FLTCaptureDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9957A3C6D1C945E94E4CD46871081DB5 /* FLTCaptureDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = A7E3BF3BA5BF87EB1DCAA6F5F033C911 /* FLTCaptureDevice.m */; };
		9B9717221A43BD8DB1BFF7673FE8E871 /* camera_avfoundation-camera_avfoundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */; };
		9C748B0EF7DC986CB78AEEF8D52616D4 /* FVPViewProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = E8EA7BF14C4EF1A812093F0C2E875B08 /* FVPViewProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9D1EC64E08C8DE208D7776FD2632CF42 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 38B96D49BB9F60E72775A6409A700E6B /* PrivacyInfo.xcprivacy */; };
		A3222ABF19FC9866D221A287F9BFEBC9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		A4CD59CF4F0FE09EEC1E982272B21FD9 /* FVPNativeVideoViewFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = A1F68769CA98D5FD0A9A4610E2409CDB /* FVPNativeVideoViewFactory.m */; };
		AC6A99F7C15692400B388F737EA304CC /* FVPVideoPlayerPlugin_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 5F26A926C0289BDBEA61CC92CC2145D2 /* FVPVideoPlayerPlugin_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AFA8A38AC1D494ACE56DE9C7B363AE53 /* FLTCaptureConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 508EFF2CEC9665264D9AA70120969E94 /* FLTCaptureConnection.m */; };
		B16C4147A69E0088076A80A73277F4DA /* FLTCamMediaSettingsAVWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 5389F79E1CBE8C93A151C1620A291913 /* FLTCamMediaSettingsAVWrapper.m */; };
		B6E7879E6847184C425BA5E681E1370F /* FLTCaptureOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = 77BB115D1046C3FF079C75B63BB12FD8 /* FLTCaptureOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B80EAC8BF0B980336C6E7ED3E0D4E0DB /* FVPNativeVideoViewFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = A1E1DACB3E3FA9978D5E0E0FFB92BFE5 /* FVPNativeVideoViewFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B899DDDB5C10C59A638E73C4F30C2E8E /* FVPNativeVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = EFB0E94EA35218E795E8EBE4BEF57548 /* FVPNativeVideoView.m */; };
		B8B43B2E145F8850F52EA02CDAAFFAED /* FLTCamConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 612B2D642F64CB43DF918A16AD3485CB /* FLTCamConfiguration.m */; };
		B9BB08B99B2081914777477B372F8263 /* FLTCaptureConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = AC5494856874B1D6150EA4EBDF564146 /* FLTCaptureConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B9F642F64F494665E7B60843F0EEDBC1 /* FLTCamConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = C7D821CD4C8F3C6C86ECA334D90F70D4 /* FLTCamConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BB85A52D2EB5F8E18E58B2A2A0AA30C6 /* FLTSavePhotoDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 2262F7D9BB47705896F54C39779D6AC6 /* FLTSavePhotoDelegate.m */; };
		BBB6C6DB17FF457BACD35352E23728AB /* FLTCameraDeviceDiscovering.h in Headers */ = {isa = PBXBuildFile; fileRef = BEEFB814075376AD9E870E525FC87D4B /* FLTCameraDeviceDiscovering.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BEB688201E1B7583B9B281D8625ECF15 /* FLTThreadSafeEventChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 76F7C6FEFB4D0824D66F653890D5DB57 /* FLTThreadSafeEventChannel.m */; };
		BEF37003673BC8C426D6A87DB7C4065C /* IntegrationTestPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = C9E173CAF428480A67FB86E419A4513B /* IntegrationTestPlugin.m */; };
		BF7FB6F8D1350C2ED36FAB8B83B9EA17 /* QueueUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = B581D4CD2EAC25BD5000D46588511DB9 /* QueueUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C0A7D5B0D751A48FD51D23AD682BAA95 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		C1893AFEE2F741B3177B07FF3FFB7A8A /* AVAssetTrackUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D25E5F498CC3AF4CF7A0DD61C656198 /* AVAssetTrackUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C405BC028C4CBAAA7ACE50D3D33B1F6B /* FVPEventBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = D6138939390B6ADFA615AB46F120FA85 /* FVPEventBridge.m */; };
		C7CAFABA0A77A333BE575FC1A22B1E0D /* video_player_avfoundation-video_player_avfoundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */; };
		C8AE936F1BB6B30D309F27CE84CC8CBD /* FLTFormatUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CD323FB7A9622C06F75ACA4585BA944 /* FLTFormatUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C964E35A6D72ED7ECEBA91E607A828F7 /* FLTCam.m in Sources */ = {isa = PBXBuildFile; fileRef = 7F7EBEF78FC2EA111AADBCB130F76EAA /* FLTCam.m */; };
		CB2EB908281BFF841551013B778FAF4A /* IntegrationTestPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = C751D3DF40D41E47619371BF3E369CD8 /* IntegrationTestPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CBD57765A1D4F65E34B0A6840CD82007 /* messages.g.h in Headers */ = {isa = PBXBuildFile; fileRef = F25DB153FD80092DE1946A17E30C4B2E /* messages.g.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */; };
		D004FC2462D5BDD5EB9C5CA668AEFA42 /* FLTCaptureSession.h in Headers */ = {isa = PBXBuildFile; fileRef = A683B1515B25795095E339D4B3F53213 /* FLTCaptureSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D1446FD083744FBAB9E078E5A8EB38B9 /* AVAssetTrackUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 31543A4510B4DFE1F932A1A1DF5EBCB9 /* AVAssetTrackUtils.m */; };
		D1508918E22EBF4D9DAA6ACF9DDDE092 /* FLTCapturePhotoOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = ABE15F3FB42FFC2EAD5548080220D59E /* FLTCapturePhotoOutput.m */; };
		D228C2C1BCE91E70A77EB0A0A5471759 /* FLTCam_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 382190E5F5AAE63BF289C5867DFD6CBC /* FLTCam_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D26EA3617436F7A1604DA5B8006FB30D /* FLTSavePhotoDelegate_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 4281F53C45315617393A58BDF0C5CC0F /* FLTSavePhotoDelegate_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5F27DE29DF7036FCAA9EC3188A1497 /* integration_test-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D806DB15B8D531788D1BD8227BA481B7 /* FLTCaptureVideoDataOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = 3D15018422D848B6641BA907C2ACF36E /* FLTCaptureVideoDataOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D89384702678F7189703452E6543BCE9 /* FLTDeviceOrientationProviding.m in Sources */ = {isa = PBXBuildFile; fileRef = C5BC31CC691293EE3B36D8716F6287B5 /* FLTDeviceOrientationProviding.m */; };
		D90221E29BE7FABD5A6260A677EF217C /* FLTIntegrationTestRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D96F5B6850D9AC6E2B41688D33FA870 /* FLTIntegrationTestRunner.m */; };
		DABA1D133F168099ED415D88CF078E9B /* FLTImagePickerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = FEEE04973763AFC10CD7B82B742C0C47 /* FLTImagePickerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1C7666B701E23B0EC566B874C20B13C /* FLTPHPickerSaveImageToPathOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 769952C73B10A8322EE4AF43AC0DB2A7 /* FLTPHPickerSaveImageToPathOperation.m */; };
		E240BE0B8D01F2875E4D9613A26BD33D /* FLTIntegrationTestRunner.h in Headers */ = {isa = PBXBuildFile; fileRef = 5ABF6C26C5FC8CD0E8EBBF34D720FB93 /* FLTIntegrationTestRunner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E654C7925C1DE3968EBE02F363FC491E /* FLTImagePickerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 80696045D940FB8D66DCA6629D3841C5 /* FLTImagePickerPlugin.m */; };
		E72011D855D5B3288771B3D0403FF69C /* camera_avfoundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9F256C7BA120B9EB73F86204C8F4539B /* camera_avfoundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = B08006DD8618E05E5F2D084F6C9DD2D8 /* path_provider_foundation-dummy.m */; };
		E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		E9790B1F7E77B14A0E3A4219B364626F /* FLTThreadSafeEventChannel.h in Headers */ = {isa = PBXBuildFile; fileRef = DE16329EF6DA3942A4628C460A7354E2 /* FLTThreadSafeEventChannel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E9B20BD2AEC00D6E6403104891B3BD58 /* FVPVideoPlayerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = AC3B64A02A7A23692C4C17373F853F41 /* FVPVideoPlayerPlugin.m */; };
		EA3FACDA9BF8A6824AD12B35E5BA851A /* messages.g.h in Headers */ = {isa = PBXBuildFile; fileRef = 3CC723BE317F75C978255ECE174B7873 /* messages.g.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EF4733E951BBE732D3315A2B698CB951 /* FLTCamMediaSettingsAVWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FDE79C8CB729B4AF44F5F57680762E9 /* FLTCamMediaSettingsAVWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F43266D8FAE1091DB24817B71D8BDEA9 /* image_gallery_saver-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = F21C1E8CF8142742AADAD28F90FB6CF7 /* image_gallery_saver-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8DDDFED04D4D9FC484571373E143869 /* ImageGallerySaverPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = A546C985D77F0622779FCBF24F24F48B /* ImageGallerySaverPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FC51F2ABDBE1E8E1782DD958A1541611 /* FLTCam.h in Headers */ = {isa = PBXBuildFile; fileRef = 8EC96D0F7517966DC898FA3EC62F4113 /* FLTCam.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FE39DCD0B607D1137F59C0EF8EBA7D7E /* FLTImagePickerPlugin_Test.h in Headers */ = {isa = PBXBuildFile; fileRef = 32CD9DBB059509809AD425F98ED347C8 /* FLTImagePickerPlugin_Test.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FFCA2EDDBD48826E1468E93BFAB04C81 /* FLTAssetWriter.h in Headers */ = {isa = PBXBuildFile; fileRef = 40D741F0756AF24C7AF184E8FF468209 /* FLTAssetWriter.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		264C585BC06523976103ADF078CF6B20 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		285592B66B0142F3564CA6620CC5F33B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56F581DDCB0A032454E604885E17AE3C;
			remoteInfo = path_provider_foundation;
		};
		2916FCB323686CA5DB01A799A7FF345B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		3CE5590432218B6D83C80C8019AA33ED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		4B62FC2FB9883142E6EDD013E714C763 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = ADE86197C9EC7D5D7AB212E24AE13395;
			remoteInfo = integration_test;
		};
		513F9365941BCB769EFDE8B24D41BFEB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		64CA47D4317948674576031BA5CBBFA0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		6EBA39C0DA53265108930F1FB6ACBE54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		74E9B33835CD6BC804E76E5F40ABE941 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 31E6C7337BFA5AF5C3B52779DC662202;
			remoteInfo = "video_player_avfoundation-video_player_avfoundation_privacy";
		};
		855A35197899D2E26708CACEE3210D13 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 845DF30C6C93A1F35C6DCEBAFECA8F8A;
			remoteInfo = image_picker_ios;
		};
		8BC10CB52F3894A85FE4D300C1FE298D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 37C246C968D3D905C8DE91562C70C1A2;
			remoteInfo = image_gallery_saver;
		};
		8CA86F1E21628B8DD26DCA4C58B8BDFE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		95973A722B3F1BB7BABEA4739222974F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A450BF39E3E5256209A256E278D71BFD;
			remoteInfo = "image_picker_ios-image_picker_ios_privacy";
		};
		B0DE1A3DD4B893B2BD713C7227CD6B1F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0B1B7EBF63BB39238A152C7553F0A9FD;
			remoteInfo = camera_avfoundation;
		};
		B76A49A3C71D893075917CD9CFAA02D0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AAA98FC9182FB27CC0DC22AD1316E0BC;
			remoteInfo = video_player_avfoundation;
		};
		DC6F48008875CD35B363400E9C923968 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CACE6618E7996464E38687E13F67D945;
			remoteInfo = "path_provider_foundation-path_provider_foundation_privacy";
		};
		F65AB27076998B9D2F444282295F75B6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D9677C5D523C2FD0AE2B9C4A7BDA913F;
			remoteInfo = "camera_avfoundation-camera_avfoundation_privacy";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		02F76617E9D30E16E4221B229FE41D1B /* FLTCaptureSession.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureSession.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureSession.m"; sourceTree = "<group>"; };
		04B460C9C9B05E4DC36801902AE778E8 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		05447F5163086E0B13149DCE1CE42F70 /* FVPVideoPlayerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayerPlugin.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h"; sourceTree = "<group>"; };
		0765757A0729CEED5B496EB055FC851B /* path_provider_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-umbrella.h"; sourceTree = "<group>"; };
		0933EB9D2C63093A25E55C1BB61D6B9B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		0A2CCE538E8EADC2A909B202FC931D81 /* video_player_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "video_player_avfoundation-Info.plist"; sourceTree = "<group>"; };
		0D568F479E759FB1EE7ED07655455BA0 /* image_gallery_saver-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "image_gallery_saver-Info.plist"; sourceTree = "<group>"; };
		13B959CD361CB6477C2D597CC37233FE /* messages.g.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = messages.g.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/messages.g.h"; sourceTree = "<group>"; };
		1BCEA2E7EB215216F40FD2EDE87750AB /* PathProviderPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PathProviderPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift"; sourceTree = "<group>"; };
		1FE4610DE4689302DB9B0123864A10F4 /* camera_avfoundation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = camera_avfoundation.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/camera_avfoundation.h"; sourceTree = "<group>"; };
		2262F7D9BB47705896F54C39779D6AC6 /* FLTSavePhotoDelegate.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTSavePhotoDelegate.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTSavePhotoDelegate.m"; sourceTree = "<group>"; };
		2A8E31166794289E7A35AA7139952BF0 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/LICENSE"; sourceTree = "<group>"; };
		2CE6840886D00DBE94128BDC7D053E7F /* ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist"; sourceTree = "<group>"; };
		2D13B87F745EF1ED49D0A9B99F50640C /* ImageGallerySaverPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ImageGallerySaverPlugin.m; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.m"; sourceTree = "<group>"; };
		2D25E5F498CC3AF4CF7A0DD61C656198 /* AVAssetTrackUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AVAssetTrackUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h"; sourceTree = "<group>"; };
		31543A4510B4DFE1F932A1A1DF5EBCB9 /* AVAssetTrackUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AVAssetTrackUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m"; sourceTree = "<group>"; };
		317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		32CD9DBB059509809AD425F98ED347C8 /* FLTImagePickerPlugin_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImagePickerPlugin_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h"; sourceTree = "<group>"; };
		337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "video_player_avfoundation-video_player_avfoundation_privacy"; path = video_player_avfoundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		345A53BEBE4D014165AA012B3DB5C855 /* IntegrationTestIosTest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestIosTest.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m; sourceTree = "<group>"; };
		367B74AF0CC975EB850A487090FE042B /* FVPVideoPlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPVideoPlayer.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m"; sourceTree = "<group>"; };
		382190E5F5AAE63BF289C5867DFD6CBC /* FLTCam_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCam_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam_Test.h"; sourceTree = "<group>"; };
		38B96D49BB9F60E72775A6409A700E6B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		38D589792B1C4D2054874110C16F0DD7 /* FLTCaptureDevice.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureDevice.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDevice.h"; sourceTree = "<group>"; };
		3C15DD01235D4AE95808B8D04AD5959E /* FLTSavePhotoDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTSavePhotoDelegate.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate.h"; sourceTree = "<group>"; };
		3CC723BE317F75C978255ECE174B7873 /* messages.g.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = messages.g.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h"; sourceTree = "<group>"; };
		3D15018422D848B6641BA907C2ACF36E /* FLTCaptureVideoDataOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureVideoDataOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureVideoDataOutput.h"; sourceTree = "<group>"; };
		3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "path_provider_foundation-path_provider_foundation_privacy"; path = path_provider_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		3DBF1BD8632322F623B54336F52FEAC5 /* messages.g.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = messages.g.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m"; sourceTree = "<group>"; };
		3E09D7433DEE1A7CEEB0CFF3863350B9 /* FLTImageStreamHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImageStreamHandler.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTImageStreamHandler.m"; sourceTree = "<group>"; };
		3E6C3AA64D5ADFDD318573ED2B884234 /* FLTCaptureDeviceFormat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureDeviceFormat.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDeviceFormat.h"; sourceTree = "<group>"; };
		3FD8C1F0506EBAF45C521E9ABB784D18 /* FLTImagePickerMetaDataUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImagePickerMetaDataUtil.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h"; sourceTree = "<group>"; };
		40208FBFAD25137C46B2187100F6D8D0 /* video_player_avfoundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = video_player_avfoundation.modulemap; sourceTree = "<group>"; };
		40D741F0756AF24C7AF184E8FF468209 /* FLTAssetWriter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTAssetWriter.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTAssetWriter.h"; sourceTree = "<group>"; };
		4281F53C45315617393A58BDF0C5CC0F /* FLTSavePhotoDelegate_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTSavePhotoDelegate_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h"; sourceTree = "<group>"; };
		44780CC92AA27A35B163401BFF25B512 /* camera_avfoundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = camera_avfoundation.modulemap; sourceTree = "<group>"; };
		45CBB9B43219CDA17AE03808BDEA9B8B /* FLTEventChannel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTEventChannel.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTEventChannel.h"; sourceTree = "<group>"; };
		4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = image_gallery_saver; path = image_gallery_saver.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		479E9A08E5092365BAA17F346ECDB977 /* integration_test.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.release.xcconfig; sourceTree = "<group>"; };
		4811AEC7B1D7B27EFAE051A963676305 /* FVPVideoEventListener.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoEventListener.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoEventListener.h"; sourceTree = "<group>"; };
		4B3882550C970A850327026D23205C64 /* FLTFormatUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTFormatUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTFormatUtils.m"; sourceTree = "<group>"; };
		4CD323FB7A9622C06F75ACA4585BA944 /* FLTFormatUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTFormatUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTFormatUtils.h"; sourceTree = "<group>"; };
		4D96F5B6850D9AC6E2B41688D33FA870 /* FLTIntegrationTestRunner.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTIntegrationTestRunner.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m; sourceTree = "<group>"; };
		4DB3674ED8DA3916396A8FE03751A246 /* path_provider_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = path_provider_foundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		4EA5069BC60C8C0D4E0DDE0036B1E2CF /* integration_test.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = integration_test.modulemap; sourceTree = "<group>"; };
		4F371D3EA61A7E2BB66A6020094F04A5 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE"; sourceTree = "<group>"; };
		508EFF2CEC9665264D9AA70120969E94 /* FLTCaptureConnection.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureConnection.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureConnection.m"; sourceTree = "<group>"; };
		50D130668646A6C1BEFF61C87643A8A6 /* CameraPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.swift"; sourceTree = "<group>"; };
		50EBFAA9AF52A1F827D2901E36E7385D /* FLTAssetWriter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTAssetWriter.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTAssetWriter.m"; sourceTree = "<group>"; };
		51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		52745EE7916310169FB7726CEFACBEAA /* ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist"; sourceTree = "<group>"; };
		527C35E64D17E2199726BBE8E8413628 /* FLTCameraPermissionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCameraPermissionManager.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraPermissionManager.m"; sourceTree = "<group>"; };
		5389F79E1CBE8C93A151C1620A291913 /* FLTCamMediaSettingsAVWrapper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCamMediaSettingsAVWrapper.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamMediaSettingsAVWrapper.m"; sourceTree = "<group>"; };
		53A1FCCA7D7ED0C729A593BC350BB083 /* DefaultCamera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DefaultCamera.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/DefaultCamera.swift"; sourceTree = "<group>"; };
		53A9B6B29A8633886B44884095E67F23 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		5433C219D14DF382FA87E5DA149EF9B5 /* FLTPHPickerSaveImageToPathOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTPHPickerSaveImageToPathOperation.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h"; sourceTree = "<group>"; };
		58E7C03092980B2BB86719555962E3F6 /* QueueUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QueueUtils.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/QueueUtils.m"; sourceTree = "<group>"; };
		5A26B067159968F9050420E87DD0BAB2 /* messages.g.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = messages.g.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/messages.g.m"; sourceTree = "<group>"; };
		5A797474E4CD782D2652F8D942F03F80 /* video_player_avfoundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "video_player_avfoundation-umbrella.h"; sourceTree = "<group>"; };
		5ABF6C26C5FC8CD0E8EBBF34D720FB93 /* FLTIntegrationTestRunner.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTIntegrationTestRunner.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h; sourceTree = "<group>"; };
		5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = integration_test; path = integration_test.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5C1E350A62D9205175A6A202D6EC2A32 /* image_gallery_saver.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_gallery_saver.debug.xcconfig; sourceTree = "<group>"; };
		5CC2F29540D1AC21E3FFB0F5A3A19965 /* video_player_avfoundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = video_player_avfoundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		5EB8F129244F591547FAC495D29BEC0F /* integration_test-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-prefix.pch"; sourceTree = "<group>"; };
		5F128005A06313779D460455E8C06AF4 /* integration_test-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "integration_test-Info.plist"; sourceTree = "<group>"; };
		5F26A926C0289BDBEA61CC92CC2145D2 /* FVPVideoPlayerPlugin_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayerPlugin_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h"; sourceTree = "<group>"; };
		5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.release.xcconfig; sourceTree = "<group>"; };
		5FDE79C8CB729B4AF44F5F57680762E9 /* FLTCamMediaSettingsAVWrapper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCamMediaSettingsAVWrapper.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h"; sourceTree = "<group>"; };
		6116BFBE9B86CCD66B86E0192F899C4B /* FVPEventBridge.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPEventBridge.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPEventBridge.h"; sourceTree = "<group>"; };
		612B2D642F64CB43DF918A16AD3485CB /* FLTCamConfiguration.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCamConfiguration.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamConfiguration.m"; sourceTree = "<group>"; };
		6560283180DD2C7B64A3137CCDD2601F /* image_picker_ios-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "image_picker_ios-dummy.m"; sourceTree = "<group>"; };
		663D67A463AFAE4C1F36E0F3C592760E /* FLTCameraPermissionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCameraPermissionManager.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraPermissionManager.h"; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		699E5F4D95B1DE2BABB4BEB1443F5E2F /* path_provider_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-prefix.pch"; sourceTree = "<group>"; };
		6A26F396610F15D00F694960D4908F03 /* FLTCapturePhotoOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCapturePhotoOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCapturePhotoOutput.h"; sourceTree = "<group>"; };
		6F231494874B3CA75A0352B66C45F5D1 /* path_provider_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = path_provider_foundation.modulemap; sourceTree = "<group>"; };
		6FB76F331940DA3AA104E2B2AB301056 /* image_picker_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "image_picker_ios-Info.plist"; sourceTree = "<group>"; };
		722054A8C8BE1B045612760D1EF9330F /* Camera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Camera.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Camera.swift"; sourceTree = "<group>"; };
		72F3A8334D0963BA074866223A18ADE0 /* FVPNativeVideoView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPNativeVideoView.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoView.h"; sourceTree = "<group>"; };
		76027A93368B8A03958EE6E0A31A7E39 /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		762DE57F1B6EF1710FAA8C660E3D0CF7 /* video_player_avfoundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "video_player_avfoundation-dummy.m"; sourceTree = "<group>"; };
		763DEFD08DF854D6767F7272371BE8D9 /* FVPFrameUpdater.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPFrameUpdater.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m"; sourceTree = "<group>"; };
		768975E636D1D2FB85622FB67DB04E5A /* image_picker_ios */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = image_picker_ios; path = image_picker_ios.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		769952C73B10A8322EE4AF43AC0DB2A7 /* FLTPHPickerSaveImageToPathOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTPHPickerSaveImageToPathOperation.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m"; sourceTree = "<group>"; };
		76F7C6FEFB4D0824D66F653890D5DB57 /* FLTThreadSafeEventChannel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTThreadSafeEventChannel.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTThreadSafeEventChannel.m"; sourceTree = "<group>"; };
		77BB115D1046C3FF079C75B63BB12FD8 /* FLTCaptureOutput.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureOutput.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureOutput.h"; sourceTree = "<group>"; };
		792DB32F2CE9E0FB6214027DD4BB15AB /* camera_avfoundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "camera_avfoundation-prefix.pch"; sourceTree = "<group>"; };
		7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		7C32A7708C8F926CAE59EA2565B5A336 /* image_gallery_saver-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "image_gallery_saver-dummy.m"; sourceTree = "<group>"; };
		7F7EBEF78FC2EA111AADBCB130F76EAA /* FLTCam.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCam.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCam.m"; sourceTree = "<group>"; };
		80696045D940FB8D66DCA6629D3841C5 /* FLTImagePickerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImagePickerPlugin.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m"; sourceTree = "<group>"; };
		8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		825C596B7717314F9CBC5DCE37FD8CD4 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/LICENSE"; sourceTree = "<group>"; };
		82BD4F8874FE0E4A80B543CCBF7D90F8 /* image_gallery_saver.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_gallery_saver.release.xcconfig; sourceTree = "<group>"; };
		86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = video_player_avfoundation.release.xcconfig; sourceTree = "<group>"; };
		8A6C1352213A4FF9D2A97A489DDBA7D3 /* image_gallery_saver-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "image_gallery_saver-prefix.pch"; sourceTree = "<group>"; };
		8A84B6E05B215CD488BD8ED58F62C40A /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		8A9F14DE25449230E69056B718C8F9CB /* FLTPermissionServicing.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTPermissionServicing.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTPermissionServicing.h"; sourceTree = "<group>"; };
		8B25645F61A5C6EA1B46D9DADB998DA5 /* SwiftImageGallerySaverPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftImageGallerySaverPlugin.swift; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/SwiftImageGallerySaverPlugin.swift"; sourceTree = "<group>"; };
		8EC96D0F7517966DC898FA3EC62F4113 /* FLTCam.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCam.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam.h"; sourceTree = "<group>"; };
		8FED77CAA7E7BDC76A0320E3A370D0A9 /* FVPViewProvider.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPViewProvider.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPViewProvider.m"; sourceTree = "<group>"; };
		92C34224083D1FE960535F19554B3A76 /* FLTPermissionServicing.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTPermissionServicing.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTPermissionServicing.m"; sourceTree = "<group>"; };
		936A426C44BB048B120DB0AD21B8631E /* QueueUtils.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QueueUtils.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.swift"; sourceTree = "<group>"; };
		940F76A634FD82DD0B3519F59AD02701 /* integration_test.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = integration_test.podspec; path = ../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		96F2BC6BC3827D8A945A2151F3136170 /* FVPFrameUpdater.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPFrameUpdater.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h"; sourceTree = "<group>"; };
		97EA34104B616F1CFA03443A5ED203B0 /* FLTImagePickerImageUtil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImagePickerImageUtil.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m"; sourceTree = "<group>"; };
		9B1758A882B06B5CFA37ABEF2D1AF913 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		9CC86ACB6FC83CC8E4E6116B7F04518D /* ImagePickerPlugin.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; name = ImagePickerPlugin.modulemap; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9F256C7BA120B9EB73F86204C8F4539B /* camera_avfoundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "camera_avfoundation-umbrella.h"; sourceTree = "<group>"; };
		9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		9F9093D2AAFB1DCBDD208D708FCC2DE6 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/LICENSE"; sourceTree = "<group>"; };
		A1E1DACB3E3FA9978D5E0E0FFB92BFE5 /* FVPNativeVideoViewFactory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPNativeVideoViewFactory.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoViewFactory.h"; sourceTree = "<group>"; };
		A1F68769CA98D5FD0A9A4610E2409CDB /* FVPNativeVideoViewFactory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPNativeVideoViewFactory.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPNativeVideoViewFactory.m"; sourceTree = "<group>"; };
		A546C985D77F0622779FCBF24F24F48B /* ImageGallerySaverPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ImageGallerySaverPlugin.h; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.h"; sourceTree = "<group>"; };
		A683B1515B25795095E339D4B3F53213 /* FLTCaptureSession.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureSession.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureSession.h"; sourceTree = "<group>"; };
		A7E3BF3BA5BF87EB1DCAA6F5F033C911 /* FLTCaptureDevice.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureDevice.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDevice.m"; sourceTree = "<group>"; };
		A822936B5DC52D708D0E9633057C68B8 /* FLTImagePickerPhotoAssetUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImagePickerPhotoAssetUtil.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h"; sourceTree = "<group>"; };
		A8315EE372804831D360B08F364008A2 /* camera_avfoundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "camera_avfoundation-dummy.m"; sourceTree = "<group>"; };
		AB2BDAC3E94CDFEEF2D0D1EB269B7E55 /* FLTImagePickerMetaDataUtil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImagePickerMetaDataUtil.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m"; sourceTree = "<group>"; };
		AB3B04F942B4DC001E956484010599C4 /* camera_avfoundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = camera_avfoundation.debug.xcconfig; sourceTree = "<group>"; };
		AB86AC50433AB1809F79FB82C6BD13D0 /* video_player_avfoundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "video_player_avfoundation-prefix.pch"; sourceTree = "<group>"; };
		ABE15F3FB42FFC2EAD5548080220D59E /* FLTCapturePhotoOutput.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCapturePhotoOutput.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCapturePhotoOutput.m"; sourceTree = "<group>"; };
		AC3B64A02A7A23692C4C17373F853F41 /* FVPVideoPlayerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPVideoPlayerPlugin.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m"; sourceTree = "<group>"; };
		AC5494856874B1D6150EA4EBDF564146 /* FLTCaptureConnection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCaptureConnection.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureConnection.h"; sourceTree = "<group>"; };
		AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = camera_avfoundation; path = camera_avfoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AD5F27DE29DF7036FCAA9EC3188A1497 /* integration_test-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-umbrella.h"; sourceTree = "<group>"; };
		ADA207B2C217FE3CB4F4C557BEDE6034 /* FLTCameraDeviceDiscovering.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCameraDeviceDiscovering.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraDeviceDiscovering.m"; sourceTree = "<group>"; };
		AE0DE59512D7F0280B826F47544B67C3 /* FVPVideoPlayer_Internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayer_Internal.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Internal.h"; sourceTree = "<group>"; };
		AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = path_provider_foundation; path = path_provider_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AEBE274F6A4265D789837200C9712D1B /* camera_avfoundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = camera_avfoundation.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		B08006DD8618E05E5F2D084F6C9DD2D8 /* path_provider_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "path_provider_foundation-dummy.m"; sourceTree = "<group>"; };
		B46A90ADB7E3F51315195150380EB95D /* integration_test.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.debug.xcconfig; sourceTree = "<group>"; };
		B581D4CD2EAC25BD5000D46588511DB9 /* QueueUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QueueUtils.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/QueueUtils.h"; sourceTree = "<group>"; };
		B776F59C7D354B3149B805F9925157C8 /* integration_test-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "integration_test-dummy.m"; sourceTree = "<group>"; };
		B977149FC67AFE7CD7588BD42D856F30 /* FVPTextureBasedVideoPlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPTextureBasedVideoPlayer.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPTextureBasedVideoPlayer.m"; sourceTree = "<group>"; };
		BB9D01EA6F21723A68169CF9C253BDC0 /* FVPDisplayLink.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPDisplayLink.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h"; sourceTree = "<group>"; };
		BCFC332AF023182F2FD9136AB22969D9 /* IntegrationTestIosTest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestIosTest.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h; sourceTree = "<group>"; };
		BDCE36D139F84A6956669467DAE1CC99 /* camera_avfoundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "camera_avfoundation-Info.plist"; sourceTree = "<group>"; };
		BEEFB814075376AD9E870E525FC87D4B /* FLTCameraDeviceDiscovering.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCameraDeviceDiscovering.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraDeviceDiscovering.h"; sourceTree = "<group>"; };
		C24AEFD0328097CB13EDCB0FA7375C28 /* FLTDeviceOrientationProviding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTDeviceOrientationProviding.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTDeviceOrientationProviding.h"; sourceTree = "<group>"; };
		C2F17DF8D8C4FFA36E773A9C03245567 /* ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist"; sourceTree = "<group>"; };
		C3C7420752996B9BD05341B16B518560 /* FLTCaptureVideoDataOutput.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureVideoDataOutput.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureVideoDataOutput.m"; sourceTree = "<group>"; };
		C5BC31CC691293EE3B36D8716F6287B5 /* FLTDeviceOrientationProviding.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTDeviceOrientationProviding.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTDeviceOrientationProviding.m"; sourceTree = "<group>"; };
		C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		C700A6972C402A1A194502533A120154 /* FLTCaptureDeviceFormat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTCaptureDeviceFormat.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDeviceFormat.m"; sourceTree = "<group>"; };
		C751D3DF40D41E47619371BF3E369CD8 /* IntegrationTestPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestPlugin.h; path = ../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h; sourceTree = "<group>"; };
		C7D821CD4C8F3C6C86ECA334D90F70D4 /* FLTCamConfiguration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTCamConfiguration.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamConfiguration.h"; sourceTree = "<group>"; };
		C90A86B37C383D245A4BF1CBEA668146 /* FVPTextureBasedVideoPlayer_Test.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPTextureBasedVideoPlayer_Test.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer_Test.h"; sourceTree = "<group>"; };
		C9E173CAF428480A67FB86E419A4513B /* IntegrationTestPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestPlugin.m; path = ../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m; sourceTree = "<group>"; };
		CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		D102193420D22821DC744427C9BD95D1 /* image_picker_ios-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "image_picker_ios-prefix.pch"; sourceTree = "<group>"; };
		D2356C73EAE2E6E7BA5E643C0C6BA0BF /* FVPAVFactory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPAVFactory.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m"; sourceTree = "<group>"; };
		D2E790809A3ECF93B7D0932830E85F84 /* path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		D2FB2F06240D9F979D6912C705DE6401 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE"; sourceTree = "<group>"; };
		D6138939390B6ADFA615AB46F120FA85 /* FVPEventBridge.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPEventBridge.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPEventBridge.m"; sourceTree = "<group>"; };
		D9153901AD83C1C50F2A87D9206A1267 /* FVPVideoPlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPVideoPlayer.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h"; sourceTree = "<group>"; };
		D927B3D3612C0252567CDEE094EF9F5C /* image_gallery_saver.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = image_gallery_saver.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/image_gallery_saver.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		D9D56AD975B3C019AEAE91370EC11706 /* FVPAVFactory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPAVFactory.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h"; sourceTree = "<group>"; };
		D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		DD7749036F4F136E7B33C3F6A86ED968 /* CameraProperties.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CameraProperties.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/CameraProperties.h"; sourceTree = "<group>"; };
		DE16329EF6DA3942A4628C460A7354E2 /* FLTThreadSafeEventChannel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTThreadSafeEventChannel.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTThreadSafeEventChannel.h"; sourceTree = "<group>"; };
		DE75FE7017D984D213264E1B1D82AD63 /* FLTWritableData.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTWritableData.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTWritableData.h"; sourceTree = "<group>"; };
		DF207E3C1BA2FF09FA0DC50118DF1E1B /* image_picker_ios-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "image_picker_ios-umbrella.h"; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h"; sourceTree = "<group>"; };
		DFB4E732031563DC2987261F292863D8 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift"; sourceTree = "<group>"; };
		E1F1B209D4810478F65D2A9CA330C595 /* FVPCADisplayLink.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPCADisplayLink.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPCADisplayLink.m"; sourceTree = "<group>"; };
		E29B714F6E21C0A8E47EFD05CF27BE8A /* path_provider_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.debug.xcconfig; sourceTree = "<group>"; };
		E77759562AF46EB6E88625AC159D4078 /* image_picker_ios.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_picker_ios.debug.xcconfig; sourceTree = "<group>"; };
		E8046A98FE14F55A7160271DCD988537 /* image_picker_ios.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = image_picker_ios.modulemap; sourceTree = "<group>"; };
		E8EA7BF14C4EF1A812093F0C2E875B08 /* FVPViewProvider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPViewProvider.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPViewProvider.h"; sourceTree = "<group>"; };
		EB188A0202B0205AC9927AF17DEF081B /* FLTWritableData.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTWritableData.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTWritableData.m"; sourceTree = "<group>"; };
		ECB5C907BE12C07E2B156775341F1FC0 /* image_picker_ios.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = image_picker_ios.podspec; path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		ED0B325ED9D372DE99B5B9910BF442DD /* messages.g.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = messages.g.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m"; sourceTree = "<group>"; };
		EE70B09FD24AFCD41A898AE0D393DC8A /* CameraProperties.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CameraProperties.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/CameraProperties.m"; sourceTree = "<group>"; };
		EF72AFF4C1CBB2509CB0C1FB59A882B8 /* FLTImagePickerPhotoAssetUtil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTImagePickerPhotoAssetUtil.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m"; sourceTree = "<group>"; };
		EFB0E94EA35218E795E8EBE4BEF57548 /* FVPNativeVideoView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FVPNativeVideoView.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoView.m"; sourceTree = "<group>"; };
		F0C7EFBFF01CFAAB52BA74E6CB40CE2C /* image_picker_ios-image_picker_ios_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "image_picker_ios-image_picker_ios_privacy"; path = image_picker_ios_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		F21C1E8CF8142742AADAD28F90FB6CF7 /* image_gallery_saver-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "image_gallery_saver-umbrella.h"; sourceTree = "<group>"; };
		F25DB153FD80092DE1946A17E30C4B2E /* messages.g.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = messages.g.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h"; sourceTree = "<group>"; };
		F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = video_player_avfoundation; path = video_player_avfoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		F50C792ECCA9E3D7BA8815417EDC1FB6 /* FLTImagePickerImageUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImagePickerImageUtil.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h"; sourceTree = "<group>"; };
		F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = image_picker_ios.release.xcconfig; sourceTree = "<group>"; };
		F86807EFB88B041EAD5C18043774E4C7 /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "camera_avfoundation-camera_avfoundation_privacy"; path = camera_avfoundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		F9A1F15FDCB39965EB7516D1FB7864A2 /* image_gallery_saver.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = image_gallery_saver.modulemap; sourceTree = "<group>"; };
		FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = camera_avfoundation.release.xcconfig; sourceTree = "<group>"; };
		FD13E98B1A7661D87C7ED46E5FDF3B62 /* FVPTextureBasedVideoPlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FVPTextureBasedVideoPlayer.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer.h"; sourceTree = "<group>"; };
		FDF5FDA80F2C5720F15F0ACC7F84FD7D /* FLTImageStreamHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImageStreamHandler.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTImageStreamHandler.h"; sourceTree = "<group>"; };
		FEEE04973763AFC10CD7B82B742C0C47 /* FLTImagePickerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTImagePickerPlugin.h; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h"; sourceTree = "<group>"; };
		FFD634024C7FC9C95E64877ED9979C34 /* video_player_avfoundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = video_player_avfoundation.debug.xcconfig; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2749C4577F56BE5637F958DB04FCB908 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				2467A4A50DECCA5C5158B34DDCC48971 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C51CA6EC745ACB63E424757C710B36C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		556D27E7BFC2BEC2841B5B9A9E5A9ACF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				030E9CC53C0A2C293E1316DF96C82228 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		81FD8C60300A3062D2FDDB1533F5A919 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A3222ABF19FC9866D221A287F9BFEBC9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A78C4AC04297E80F12CCBA45ECC02EF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B7C2FF4A20ABA1FEAD6C6E13A1B1A0F9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				71ABE471529580AFD720CA76711E5637 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3967469D6A5BE413E44FED57BA1B7A0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D37529713F6219C15572CE565F23780E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */,
				CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB6B120BD47FC66302FFF63D264FB73B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C0A7D5B0D751A48FD51D23AD682BAA95 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDFC044BC64A11EC40D9DF966962EBEF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ED107B8EC4AD2BF481D5DB51879E267B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		008841D9E0A504899B11B939746C68BA /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				DFB4E732031563DC2987261F292863D8 /* messages.g.swift */,
				1BCEA2E7EB215216F40FD2EDE87750AB /* PathProviderPlugin.swift */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		0174170A382671527BEADAE2C0C4BDEE /* .. */ = {
			isa = PBXGroup;
			children = (
				97D27363D92C7585437090F27B8AF976 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation";
			sourceTree = "<group>";
		};
		01C7249BCD443F6A0286E85A7FD6B282 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				0DAD43D4EC393FAFC49F6BC7C9045FE6 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		027C17C5EFC6C02F09F4BCFCFEA6DED8 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				722054A8C8BE1B045612760D1EF9330F /* Camera.swift */,
				50D130668646A6C1BEFF61C87643A8A6 /* CameraPlugin.swift */,
				53A1FCCA7D7ED0C729A593BC350BB083 /* DefaultCamera.swift */,
				936A426C44BB048B120DB0AD21B8631E /* QueueUtils.swift */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		0396E99868758AAF1079F46F819FBF7E /* ios */ = {
			isa = PBXGroup;
			children = (
				DFE6EB657A1318D99632A8F054FA3EA9 /* camera_avfoundation */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		04121552C6EE6042E71BC031C14F493A /* Pod */ = {
			isa = PBXGroup;
			children = (
				D927B3D3612C0252567CDEE094EF9F5C /* image_gallery_saver.podspec */,
				D2FB2F06240D9F979D6912C705DE6401 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		046D414EE1044D6A39C2B3BC676F5CCB /* .. */ = {
			isa = PBXGroup;
			children = (
				80967AD011DEC180C19EAB298B21A1A8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		047B0794526A1D983B613488CDADADC1 /* ios */ = {
			isa = PBXGroup;
			children = (
				5FD9881CA3D837C03DFF4E4A77D9D1A6 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		0496025EBD433D1FAE1F2B260FA8C2C0 /* Pod */ = {
			isa = PBXGroup;
			children = (
				ECB5C907BE12C07E2B156775341F1FC0 /* image_picker_ios.podspec */,
				9CC86ACB6FC83CC8E4E6116B7F04518D /* ImagePickerPlugin.modulemap */,
				9F9093D2AAFB1DCBDD208D708FCC2DE6 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		052DEEC8156C97B4291B779B16CA51C5 /* .. */ = {
			isa = PBXGroup;
			children = (
				425BACF20B4BBD4254DA10EF1BA142B6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		056FE19E213C9A856839A28D2F1BC4DF /* ios */ = {
			isa = PBXGroup;
			children = (
				7B115D3C3E6C1F9C26FA2207FC90772C /* integration_test */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		060B939DF8EB41E776462915072408E6 /* ios */ = {
			isa = PBXGroup;
			children = (
				E4EF9B629FBDE20E74B9AF43827A02E3 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		0824FF98EFFA884526CB298217C9EED3 /* ios */ = {
			isa = PBXGroup;
			children = (
				193C63E335F70F2F03DB001CEBC173CB /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		090A32F9BD32AFC0B491306ADFC17022 /* Sources */ = {
			isa = PBXGroup;
			children = (
				6F05E09816832A60E78532F0501133BA /* integration_test */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		09E6F0FFED724A4FDAD5217E7F5C1E72 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				19961B3D9EAD1E576CB37C448F5D2D3A /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		09FEE29972FD8E410192B7E826944EBB /* video_player_avfoundation_ios */ = {
			isa = PBXGroup;
			children = (
				EFB0E94EA35218E795E8EBE4BEF57548 /* FVPNativeVideoView.m */,
			);
			name = video_player_avfoundation_ios;
			path = video_player_avfoundation_ios;
			sourceTree = "<group>";
		};
		0B2ABC8E6765AB40167AD3990BF13EDD /* .. */ = {
			isa = PBXGroup;
			children = (
				9BA14C749B0430EE5FB1D956E67E3C39 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources";
			sourceTree = "<group>";
		};
		0CA444FC684290F8E6C06C9ED0C67AF0 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				31543A4510B4DFE1F932A1A1DF5EBCB9 /* AVAssetTrackUtils.m */,
				D2356C73EAE2E6E7BA5E643C0C6BA0BF /* FVPAVFactory.m */,
				E1F1B209D4810478F65D2A9CA330C595 /* FVPCADisplayLink.m */,
				D6138939390B6ADFA615AB46F120FA85 /* FVPEventBridge.m */,
				763DEFD08DF854D6767F7272371BE8D9 /* FVPFrameUpdater.m */,
				A1F68769CA98D5FD0A9A4610E2409CDB /* FVPNativeVideoViewFactory.m */,
				B977149FC67AFE7CD7588BD42D856F30 /* FVPTextureBasedVideoPlayer.m */,
				367B74AF0CC975EB850A487090FE042B /* FVPVideoPlayer.m */,
				AC3B64A02A7A23692C4C17373F853F41 /* FVPVideoPlayerPlugin.m */,
				8FED77CAA7E7BDC76A0320E3A370D0A9 /* FVPViewProvider.m */,
				3DBF1BD8632322F623B54336F52FEAC5 /* messages.g.m */,
				2029CC2E34339268EA01554FB71986F9 /* include */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		0DAD43D4EC393FAFC49F6BC7C9045FE6 /* plugins */ = {
			isa = PBXGroup;
			children = (
				6891012D9D0565B5D358434A56ACBBFF /* image_picker_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		0DAD5E7C17FAC87E328BF8A628C27339 /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				0B2ABC8E6765AB40167AD3990BF13EDD /* .. */,
				0496025EBD433D1FAE1F2B260FA8C2C0 /* Pod */,
				CE258D8AF67F60C0D32A91B8283BFD04 /* Support Files */,
			);
			name = image_picker_ios;
			path = ../.symlinks/plugins/image_picker_ios/ios;
			sourceTree = "<group>";
		};
		0EDE13318DCD9F55340E5BA44D103DF6 /* Sources */ = {
			isa = PBXGroup;
			children = (
				FF90E657CCB7AE0B27E9C07EB82DE9DA /* image_picker_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		133321F000B3CA5905191FA912104A6F /* .. */ = {
			isa = PBXGroup;
			children = (
				DF0789E8621FF918CFEEFD832C7897D4 /* .. */,
			);
			name = ..;
			path = ../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources;
			sourceTree = "<group>";
		};
		137193D57D939F8C5B02392C805672C4 /* darwin */ = {
			isa = PBXGroup;
			children = (
				C1D4EF5D05784DCDC9B470DFCB2A97FA /* video_player_avfoundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		156988D83C7B4981DF27FD9B1D297FAE /* .. */ = {
			isa = PBXGroup;
			children = (
				B0551F0D64153FBE1D2A89E09B95A473 /* .. */,
				9369B485DADA98193CBB6353194335A3 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		156B406A8994E9D254904C4FC6C674B2 /* Documents */ = {
			isa = PBXGroup;
			children = (
				DCB0B5AAF6F93ED6D3E2C7ADB7B5FFFD /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1DE6440B6853131A80C53DDA34375AAB /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		17651AE656324BAC45A4C9476B86E523 /* ios */ = {
			isa = PBXGroup;
			children = (
				3AB1FB72C4F78EA3FCD47F1521ED1366 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		193C63E335F70F2F03DB001CEBC173CB /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				89DD8970B3200E6A20750B9B5640DA23 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		19961B3D9EAD1E576CB37C448F5D2D3A /* darwin */ = {
			isa = PBXGroup;
			children = (
				7ADBF4EA80F39285EB36AFDBF776FF10 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		1A8A72ACFE7C749E431D85701E096CB3 /* Sources */ = {
			isa = PBXGroup;
			children = (
				6F51D7394CAC50484B89A0D504934700 /* camera_avfoundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		1AD52F88C668F878FC7902C21ED4CB6B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				6F231494874B3CA75A0352B66C45F5D1 /* path_provider_foundation.modulemap */,
				B08006DD8618E05E5F2D084F6C9DD2D8 /* path_provider_foundation-dummy.m */,
				D2E790809A3ECF93B7D0932830E85F84 /* path_provider_foundation-Info.plist */,
				699E5F4D95B1DE2BABB4BEB1443F5E2F /* path_provider_foundation-prefix.pch */,
				0765757A0729CEED5B496EB055FC851B /* path_provider_foundation-umbrella.h */,
				E29B714F6E21C0A8E47EFD05CF27BE8A /* path_provider_foundation.debug.xcconfig */,
				5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */,
				04B460C9C9B05E4DC36801902AE778E8 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/path_provider_foundation";
			sourceTree = "<group>";
		};
		1C11B964EB2C4F8D2A75DA50E8860886 /* plugins */ = {
			isa = PBXGroup;
			children = (
				37375D760B1C36FDFBA0F34E6C086FA5 /* camera_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		1DE6440B6853131A80C53DDA34375AAB /* iOS */ = {
			isa = PBXGroup;
			children = (
				8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */,
				ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		1F8ED5378A2CDC5A182F75738C0ED87B /* .. */ = {
			isa = PBXGroup;
			children = (
				5AF5223316DA59F21BCD05C88AD7063C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		2029CC2E34339268EA01554FB71986F9 /* include */ = {
			isa = PBXGroup;
			children = (
				D50D52DDB3C78A6D93FCFC771FB3281B /* video_player_avfoundation */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		2119CA00A9E74DC2753039A02AD1BC2F /* .. */ = {
			isa = PBXGroup;
			children = (
				D6785EC045831D2E9FEC7A3618172D65 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		244734A9FD7AE70FB6864240773DC7B0 /* plugins */ = {
			isa = PBXGroup;
			children = (
				F20ADEAE1054C3E1F89A7587D378947C /* camera_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		2591812AF2C522A6EC460B8EED03EF53 /* .. */ = {
			isa = PBXGroup;
			children = (
				B67D2B3EAC5CDF18776DBE25FD3FF0F7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		2A3590D9ADB8B2E57DD749A660D2CB49 /* .. */ = {
			isa = PBXGroup;
			children = (
				E6FB7EDEF647BF7DA33B438C2F3CFD59 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		2C8041DAEE06913CA50626775D2B9C38 /* Pod */ = {
			isa = PBXGroup;
			children = (
				AEBE274F6A4265D789837200C9712D1B /* camera_avfoundation.podspec */,
				2A8E31166794289E7A35AA7139952BF0 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		2CAC47D55C0A2D12183BBE9A16BC0961 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				0174170A382671527BEADAE2C0C4BDEE /* .. */,
				2C8041DAEE06913CA50626775D2B9C38 /* Pod */,
				F5BAB90EC08676DDA556319A3211ABF9 /* Support Files */,
			);
			name = camera_avfoundation;
			path = ../.symlinks/plugins/camera_avfoundation/ios;
			sourceTree = "<group>";
		};
		3071F21B4DB7703AEEA8085EF1F46D88 /* ios */ = {
			isa = PBXGroup;
			children = (
				01C7249BCD443F6A0286E85A7FD6B282 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		33D63813AB8A5B03D988798D0D348587 /* Sources */ = {
			isa = PBXGroup;
			children = (
				CD0E67D46C43F905ED65D3D62572FEDC /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		3459B56B2D1D888FF06BF141C13DC4A0 /* manish */ = {
			isa = PBXGroup;
			children = (
				815D5E39F0A8FEBE55D4FE6E11BD9A42 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		34F2B47768A3BEE154891CA99A5D5D95 /* Documents */ = {
			isa = PBXGroup;
			children = (
				6224ED8C43057A06989BFB1694BB7A84 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		355BB99D9B623FE6EFE9D8D9C9B620E3 /* plugins */ = {
			isa = PBXGroup;
			children = (
				5D706D5244ECFC8673CC74D6F9C6128D /* image_gallery_saver */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		358969A1CE72AC6D28D8F24EF03F1CF7 /* ios */ = {
			isa = PBXGroup;
			children = (
				E9385E8B7E226C4AF8D42C6C7FABD51E /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		35A17A4A673566C7B9E5E780AEE2FDED /* ios */ = {
			isa = PBXGroup;
			children = (
				E07B5FCDFA3B0195135A6224BF224316 /* camera_avfoundation */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		37375D760B1C36FDFBA0F34E6C086FA5 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				0396E99868758AAF1079F46F819FBF7E /* ios */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		37CB4A594CAAFDB8E896D15A0F16BCAE /* Pod */ = {
			isa = PBXGroup;
			children = (
				4F371D3EA61A7E2BB66A6020094F04A5 /* LICENSE */,
				4DB3674ED8DA3916396A8FE03751A246 /* path_provider_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		389F28F048252B420E3F82B31AF96389 /* .. */ = {
			isa = PBXGroup;
			children = (
				052DEEC8156C97B4291B779B16CA51C5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		38DC79912D9BF6C7AFF375C5EEE71B1C /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				CE272C57037118703C3AF97C279B6337 /* Sources */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		39C4A5FB4398DE3CF1DC402D250933B5 /* Documents */ = {
			isa = PBXGroup;
			children = (
				DFA915771480A8B9035332F1288C0C45 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		3AB1FB72C4F78EA3FCD47F1521ED1366 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				BDF79271E865058DDDBFEB88728A5EB9 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		3AC1150656D2BBA80E457ABD51164FD0 /* plugins */ = {
			isa = PBXGroup;
			children = (
				830C8C2D0CAEA8E4A19B6D1CD039D33A /* video_player_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		3C5B3FCC96E0E14FB48C610C2A9759B4 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				8FE9BEF3D3823779C62B709E164274F9 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		3EE912D14BECA489F401FEA9AED47162 /* plugins */ = {
			isa = PBXGroup;
			children = (
				B93EEB8FF6E0704766C9BFF9E785B771 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		425BACF20B4BBD4254DA10EF1BA142B6 /* .. */ = {
			isa = PBXGroup;
			children = (
				C5794E8B71BCF11CDB92A59D65DCC6C7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		43A4FEDB671B70EAA4B2A31516C06150 /* manish */ = {
			isa = PBXGroup;
			children = (
				61ACCA2B935E40B85B0B93B4D4F0ED80 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		43C1C8516609FDD99FFD7D71AA79486A /* manish */ = {
			isa = PBXGroup;
			children = (
				7D37D3E4E844011B3A36C194A1FFB876 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		469957AC084E04A8C8005D330E05EB36 /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				F50C792ECCA9E3D7BA8815417EDC1FB6 /* FLTImagePickerImageUtil.h */,
				3FD8C1F0506EBAF45C521E9ABB784D18 /* FLTImagePickerMetaDataUtil.h */,
				A822936B5DC52D708D0E9633057C68B8 /* FLTImagePickerPhotoAssetUtil.h */,
				FEEE04973763AFC10CD7B82B742C0C47 /* FLTImagePickerPlugin.h */,
				32CD9DBB059509809AD425F98ED347C8 /* FLTImagePickerPlugin_Test.h */,
				5433C219D14DF382FA87E5DA149EF9B5 /* FLTPHPickerSaveImageToPathOperation.h */,
				3CC723BE317F75C978255ECE174B7873 /* messages.g.h */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		4A395BD4B5B3843AF7AF9E0181ACDBE4 /* .. */ = {
			isa = PBXGroup;
			children = (
				2A3590D9ADB8B2E57DD749A660D2CB49 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4A854BE529442B96F42D213F5A8DA08A /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1FE4610DE4689302DB9B0123864A10F4 /* camera_avfoundation.h */,
				DD7749036F4F136E7B33C3F6A86ED968 /* CameraProperties.h */,
				40D741F0756AF24C7AF184E8FF468209 /* FLTAssetWriter.h */,
				8EC96D0F7517966DC898FA3EC62F4113 /* FLTCam.h */,
				382190E5F5AAE63BF289C5867DFD6CBC /* FLTCam_Test.h */,
				C7D821CD4C8F3C6C86ECA334D90F70D4 /* FLTCamConfiguration.h */,
				BEEFB814075376AD9E870E525FC87D4B /* FLTCameraDeviceDiscovering.h */,
				663D67A463AFAE4C1F36E0F3C592760E /* FLTCameraPermissionManager.h */,
				5FDE79C8CB729B4AF44F5F57680762E9 /* FLTCamMediaSettingsAVWrapper.h */,
				AC5494856874B1D6150EA4EBDF564146 /* FLTCaptureConnection.h */,
				38D589792B1C4D2054874110C16F0DD7 /* FLTCaptureDevice.h */,
				3E6C3AA64D5ADFDD318573ED2B884234 /* FLTCaptureDeviceFormat.h */,
				77BB115D1046C3FF079C75B63BB12FD8 /* FLTCaptureOutput.h */,
				6A26F396610F15D00F694960D4908F03 /* FLTCapturePhotoOutput.h */,
				A683B1515B25795095E339D4B3F53213 /* FLTCaptureSession.h */,
				3D15018422D848B6641BA907C2ACF36E /* FLTCaptureVideoDataOutput.h */,
				C24AEFD0328097CB13EDCB0FA7375C28 /* FLTDeviceOrientationProviding.h */,
				45CBB9B43219CDA17AE03808BDEA9B8B /* FLTEventChannel.h */,
				4CD323FB7A9622C06F75ACA4585BA944 /* FLTFormatUtils.h */,
				FDF5FDA80F2C5720F15F0ACC7F84FD7D /* FLTImageStreamHandler.h */,
				8A9F14DE25449230E69056B718C8F9CB /* FLTPermissionServicing.h */,
				3C15DD01235D4AE95808B8D04AD5959E /* FLTSavePhotoDelegate.h */,
				4281F53C45315617393A58BDF0C5CC0F /* FLTSavePhotoDelegate_Test.h */,
				DE16329EF6DA3942A4628C460A7354E2 /* FLTThreadSafeEventChannel.h */,
				DE75FE7017D984D213264E1B1D82AD63 /* FLTWritableData.h */,
				13B959CD361CB6477C2D597CC37233FE /* messages.g.h */,
				B581D4CD2EAC25BD5000D46588511DB9 /* QueueUtils.h */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		4B03DC0EF86ED2794031D7C2301314B9 /* manish */ = {
			isa = PBXGroup;
			children = (
				E2F23F72085210325567BDE1769C9972 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		529A0098450B6BB457ECA7BD56AE08E2 /* Resources */ = {
			isa = PBXGroup;
			children = (
				0933EB9D2C63093A25E55C1BB61D6B9B /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		56612F7D286AC03591F575DE03D67C49 /* Documents */ = {
			isa = PBXGroup;
			children = (
				7782A464B76B191E6ECA9FF4A154DCAD /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		578FD24C8DDA12C49B18429BE07E02C7 /* .. */ = {
			isa = PBXGroup;
			children = (
				D908C8EAE3CB4454676EC667F8D91D67 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		58A86620A4A0CA12346768B34655654F /* .. */ = {
			isa = PBXGroup;
			children = (
				EDC906EB377E759D6CAA5306C4AC5960 /* Documents */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		5AF5223316DA59F21BCD05C88AD7063C /* .. */ = {
			isa = PBXGroup;
			children = (
				D113D8618155F4D0FBDB5357C5C79BCD /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		5B45D38BD2D3DEDF2A71BBD983D1832E /* Classes */ = {
			isa = PBXGroup;
			children = (
				A546C985D77F0622779FCBF24F24F48B /* ImageGallerySaverPlugin.h */,
				2D13B87F745EF1ED49D0A9B99F50640C /* ImageGallerySaverPlugin.m */,
				8B25645F61A5C6EA1B46D9DADB998DA5 /* SwiftImageGallerySaverPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		5D706D5244ECFC8673CC74D6F9C6128D /* image_gallery_saver */ = {
			isa = PBXGroup;
			children = (
				C1F18E1818A46362F114E967C29CB875 /* ios */,
			);
			name = image_gallery_saver;
			path = image_gallery_saver;
			sourceTree = "<group>";
		};
		5F36792473663056548E724E661BEE83 /* Resources */ = {
			isa = PBXGroup;
			children = (
				53A9B6B29A8633886B44884095E67F23 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		5FD9881CA3D837C03DFF4E4A77D9D1A6 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				3AC1150656D2BBA80E457ABD51164FD0 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		6105994B70D12E5FF45A3DDCE4EAC85A /* Pod */ = {
			isa = PBXGroup;
			children = (
				825C596B7717314F9CBC5DCE37FD8CD4 /* LICENSE */,
				5CC2F29540D1AC21E3FFB0F5A3A19965 /* video_player_avfoundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		61ACCA2B935E40B85B0B93B4D4F0ED80 /* camera */ = {
			isa = PBXGroup;
			children = (
				887C60D8800220D6D5E427590A8B73B4 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		6224ED8C43057A06989BFB1694BB7A84 /* manish */ = {
			isa = PBXGroup;
			children = (
				F5D291FA09A5471AE9CF8661979E0CE8 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		6314F1CA0418C7BE943F41757FA3A597 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				3EE912D14BECA489F401FEA9AED47162 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		653AF48A034C5049BE02BA6CA4EBFDF3 /* ios */ = {
			isa = PBXGroup;
			children = (
				3C5B3FCC96E0E14FB48C610C2A9759B4 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		67EFF371AF1D298F8F8EFB8DCF3EF95B /* .. */ = {
			isa = PBXGroup;
			children = (
				1F8ED5378A2CDC5A182F75738C0ED87B /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios";
			sourceTree = "<group>";
		};
		6891012D9D0565B5D358434A56ACBBFF /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				EEC33A12110CF9EF8712818BE6C39054 /* ios */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		6AA4F824E8ED0E039F76C06A33CD9DD6 /* manish */ = {
			isa = PBXGroup;
			children = (
				6D2BD61C9307BCBBF83D0DB424F03D83 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		6CCE212984971DD47711DC038A49FF17 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				133321F000B3CA5905191FA912104A6F /* .. */,
				B7BBD0BB857D4F2266BA631A80A01A3E /* Pod */,
				C38B74E5DCAC8C34D12B7C7FF50F8D4E /* Support Files */,
			);
			name = integration_test;
			path = ../.symlinks/plugins/integration_test/ios;
			sourceTree = "<group>";
		};
		6D2BD61C9307BCBBF83D0DB424F03D83 /* camera */ = {
			isa = PBXGroup;
			children = (
				047B0794526A1D983B613488CDADADC1 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		6F05E09816832A60E78532F0501133BA /* integration_test */ = {
			isa = PBXGroup;
			children = (
				4D96F5B6850D9AC6E2B41688D33FA870 /* FLTIntegrationTestRunner.m */,
				345A53BEBE4D014165AA012B3DB5C855 /* IntegrationTestIosTest.m */,
				C9E173CAF428480A67FB86E419A4513B /* IntegrationTestPlugin.m */,
				B7CAFDAFE0D701040D97272090610BF0 /* include */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		6F51D7394CAC50484B89A0D504934700 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				B94C32A41B22083C965F09FED17B8970 /* Resources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		7159316214143858D48FA7838BBFD3B9 /* .. */ = {
			isa = PBXGroup;
			children = (
				7181BEC3B6EB38F3B443E039E4AC797B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7181BEC3B6EB38F3B443E039E4AC797B /* .. */ = {
			isa = PBXGroup;
			children = (
				72674E28DB4534B86D3132123A5E7283 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		719EA000DC52EE2B2D4D4562E62E699B /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				2CAC47D55C0A2D12183BBE9A16BC0961 /* camera_avfoundation */,
				B64AA5450EDA4B14386E4C84EFE89CC0 /* Flutter */,
				EBD98669BA166C2BD8AF259BB53E1969 /* image_gallery_saver */,
				0DAD5E7C17FAC87E328BF8A628C27339 /* image_picker_ios */,
				6CCE212984971DD47711DC038A49FF17 /* integration_test */,
				E3B0B199FE3EACD56D30B87E9BCE7710 /* path_provider_foundation */,
				A60EB2C25D6DC17155CCAEAD3CF97D90 /* video_player_avfoundation */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		722B93FC8EAB347ECAD49649B125C456 /* include */ = {
			isa = PBXGroup;
			children = (
				4A854BE529442B96F42D213F5A8DA08A /* camera_avfoundation */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		72674E28DB4534B86D3132123A5E7283 /* Documents */ = {
			isa = PBXGroup;
			children = (
				43A4FEDB671B70EAA4B2A31516C06150 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		73D6B18C672B02F97BAC80F3D1902977 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				CB5399F6448EC968EA3DAF3BED00AD69 /* Resources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		746E6CB4CCF611056E540D67425A5F6D /* darwin */ = {
			isa = PBXGroup;
			children = (
				B607B0084BFD4C8BBA979E4FAF397872 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		7551BBFCE3909768663B92FD9D7A39F6 /* camera */ = {
			isa = PBXGroup;
			children = (
				17651AE656324BAC45A4C9476B86E523 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		7782A464B76B191E6ECA9FF4A154DCAD /* manish */ = {
			isa = PBXGroup;
			children = (
				7DA1C1B2585EDCBA18B1BBEA7F58012B /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		7A7020D26783321DB3A754FE3F8F0ADD /* .. */ = {
			isa = PBXGroup;
			children = (
				D53CAB2CBC36B0D319F0293D17206CD5 /* Documents */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		7ADBF4EA80F39285EB36AFDBF776FF10 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				F62847B4DFEE52F7DA778BA48F9FB972 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */,
				C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */,
				D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */,
				7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */,
				317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */,
				51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */,
				96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */,
				CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */,
				9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */,
				D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		7B115D3C3E6C1F9C26FA2207FC90772C /* integration_test */ = {
			isa = PBXGroup;
			children = (
				090A32F9BD32AFC0B491306ADFC17022 /* Sources */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		7D053C88849A4730E54528FF9B07F028 /* camera_avfoundation_objc */ = {
			isa = PBXGroup;
			children = (
				EE70B09FD24AFCD41A898AE0D393DC8A /* CameraProperties.m */,
				50EBFAA9AF52A1F827D2901E36E7385D /* FLTAssetWriter.m */,
				7F7EBEF78FC2EA111AADBCB130F76EAA /* FLTCam.m */,
				612B2D642F64CB43DF918A16AD3485CB /* FLTCamConfiguration.m */,
				ADA207B2C217FE3CB4F4C557BEDE6034 /* FLTCameraDeviceDiscovering.m */,
				527C35E64D17E2199726BBE8E8413628 /* FLTCameraPermissionManager.m */,
				5389F79E1CBE8C93A151C1620A291913 /* FLTCamMediaSettingsAVWrapper.m */,
				508EFF2CEC9665264D9AA70120969E94 /* FLTCaptureConnection.m */,
				A7E3BF3BA5BF87EB1DCAA6F5F033C911 /* FLTCaptureDevice.m */,
				C700A6972C402A1A194502533A120154 /* FLTCaptureDeviceFormat.m */,
				ABE15F3FB42FFC2EAD5548080220D59E /* FLTCapturePhotoOutput.m */,
				02F76617E9D30E16E4221B229FE41D1B /* FLTCaptureSession.m */,
				C3C7420752996B9BD05341B16B518560 /* FLTCaptureVideoDataOutput.m */,
				C5BC31CC691293EE3B36D8716F6287B5 /* FLTDeviceOrientationProviding.m */,
				4B3882550C970A850327026D23205C64 /* FLTFormatUtils.m */,
				3E09D7433DEE1A7CEEB0CFF3863350B9 /* FLTImageStreamHandler.m */,
				92C34224083D1FE960535F19554B3A76 /* FLTPermissionServicing.m */,
				2262F7D9BB47705896F54C39779D6AC6 /* FLTSavePhotoDelegate.m */,
				76F7C6FEFB4D0824D66F653890D5DB57 /* FLTThreadSafeEventChannel.m */,
				EB188A0202B0205AC9927AF17DEF081B /* FLTWritableData.m */,
				5A26B067159968F9050420E87DD0BAB2 /* messages.g.m */,
				58E7C03092980B2BB86719555962E3F6 /* QueueUtils.m */,
				722B93FC8EAB347ECAD49649B125C456 /* include */,
			);
			name = camera_avfoundation_objc;
			path = camera_avfoundation_objc;
			sourceTree = "<group>";
		};
		7D37D3E4E844011B3A36C194A1FFB876 /* camera */ = {
			isa = PBXGroup;
			children = (
				CBBF66FAA033AAE7990899791B9F26F4 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		7DA1C1B2585EDCBA18B1BBEA7F58012B /* camera */ = {
			isa = PBXGroup;
			children = (
				3071F21B4DB7703AEEA8085EF1F46D88 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		7ECB4DFC1748B560C9C44C3991B4E8AD /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				F9251EA00A9F63D676732400F4659674 /* darwin */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		80967AD011DEC180C19EAB298B21A1A8 /* .. */ = {
			isa = PBXGroup;
			children = (
				34F2B47768A3BEE154891CA99A5D5D95 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		815D5E39F0A8FEBE55D4FE6E11BD9A42 /* camera */ = {
			isa = PBXGroup;
			children = (
				8F5875C637C4172D798D53A6CDB4F22D /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		830C8C2D0CAEA8E4A19B6D1CD039D33A /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				137193D57D939F8C5B02392C805672C4 /* darwin */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		8357CE30F0D0BDF60C1FA6674E141580 /* .. */ = {
			isa = PBXGroup;
			children = (
				FF5B41BE8C5365FFF31BC9B3B268F349 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8371FD70596E562E0D088CBE39F4E5DF /* Sources */ = {
			isa = PBXGroup;
			children = (
				73D6B18C672B02F97BAC80F3D1902977 /* video_player_avfoundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		85E388AA56DDE3B1E2BFC8D2D2A49C3E /* .. */ = {
			isa = PBXGroup;
			children = (
				CDBB9DF48A7905927C6CF7758A8CE941 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		85EDE02A6778E81A1AC9CDB7701F539C /* .. */ = {
			isa = PBXGroup;
			children = (
				8B59E553556D6DFBA524B9FD4811E09C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8728FD3805CD82AF7D9E3C4510050648 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				355BB99D9B623FE6EFE9D8D9C9B620E3 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		887C60D8800220D6D5E427590A8B73B4 /* ios */ = {
			isa = PBXGroup;
			children = (
				8728FD3805CD82AF7D9E3C4510050648 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		89DD8970B3200E6A20750B9B5640DA23 /* plugins */ = {
			isa = PBXGroup;
			children = (
				BFCFEB82264CA8C42628F20F63BF0A09 /* integration_test */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		8B59E553556D6DFBA524B9FD4811E09C /* .. */ = {
			isa = PBXGroup;
			children = (
				578FD24C8DDA12C49B18429BE07E02C7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8B5E6C68BE3990112D83512AC62C84E8 /* .. */ = {
			isa = PBXGroup;
			children = (
				AF7BE47A3289BA2B5BCC82C91EC67389 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8DDDC84AADBB309BF219236BC6C5F46C /* .. */ = {
			isa = PBXGroup;
			children = (
				D124A97930A8B243D9CD20BD69FC9A10 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8E86B45245C48E825124EDB1A1922722 /* plugins */ = {
			isa = PBXGroup;
			children = (
				EEDCE3A461678107A5CCF1C17F437A93 /* image_picker_ios */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		8EFA88BF040D34BC9896B269AF89CB46 /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				0EDE13318DCD9F55340E5BA44D103DF6 /* Sources */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		8F5875C637C4172D798D53A6CDB4F22D /* ios */ = {
			isa = PBXGroup;
			children = (
				6314F1CA0418C7BE943F41757FA3A597 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		8FE9BEF3D3823779C62B709E164274F9 /* plugins */ = {
			isa = PBXGroup;
			children = (
				7ECB4DFC1748B560C9C44C3991B4E8AD /* video_player_avfoundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		9369B485DADA98193CBB6353194335A3 /* Documents */ = {
			isa = PBXGroup;
			children = (
				6AA4F824E8ED0E039F76C06A33CD9DD6 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		946460961AE572AA7BB17EF7C312A40E /* .. */ = {
			isa = PBXGroup;
			children = (
				2591812AF2C522A6EC460B8EED03EF53 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		96121E9294B9DD042FDE8DDFBAC25252 /* .. */ = {
			isa = PBXGroup;
			children = (
				7A7020D26783321DB3A754FE3F8F0ADD /* .. */,
				56612F7D286AC03591F575DE03D67C49 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		96BA76C90808F002009470B44D9CC387 /* Sources */ = {
			isa = PBXGroup;
			children = (
				027C17C5EFC6C02F09F4BCFCFEA6DED8 /* camera_avfoundation */,
				7D053C88849A4730E54528FF9B07F028 /* camera_avfoundation_objc */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		979F6244FB55DFF36CDF2C38C5940899 /* .. */ = {
			isa = PBXGroup;
			children = (
				C3E94A9724FBE840A08E391521C1AEA3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		97D27363D92C7585437090F27B8AF976 /* .. */ = {
			isa = PBXGroup;
			children = (
				F4C9FC741FD2BA2C788B8B936C53870B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9BA14C749B0430EE5FB1D956E67E3C39 /* .. */ = {
			isa = PBXGroup;
			children = (
				D66D80178C3558050BA080CEB6C092C0 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9C6F08EC874432C5D876FFB62FC42272 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				244734A9FD7AE70FB6864240773DC7B0 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		A50B1004EF4466F179D5380D19CC778A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F9A1F15FDCB39965EB7516D1FB7864A2 /* image_gallery_saver.modulemap */,
				7C32A7708C8F926CAE59EA2565B5A336 /* image_gallery_saver-dummy.m */,
				0D568F479E759FB1EE7ED07655455BA0 /* image_gallery_saver-Info.plist */,
				8A6C1352213A4FF9D2A97A489DDBA7D3 /* image_gallery_saver-prefix.pch */,
				F21C1E8CF8142742AADAD28F90FB6CF7 /* image_gallery_saver-umbrella.h */,
				5C1E350A62D9205175A6A202D6EC2A32 /* image_gallery_saver.debug.xcconfig */,
				82BD4F8874FE0E4A80B543CCBF7D90F8 /* image_gallery_saver.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/image_gallery_saver";
			sourceTree = "<group>";
		};
		A60EB2C25D6DC17155CCAEAD3CF97D90 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				DDDE68509345253E8C120274D7B3B070 /* .. */,
				6105994B70D12E5FF45A3DDCE4EAC85A /* Pod */,
				BA123AE90A7DD3DEBC9BE04BD41CF42B /* Support Files */,
			);
			name = video_player_avfoundation;
			path = ../.symlinks/plugins/video_player_avfoundation/darwin;
			sourceTree = "<group>";
		};
		AC98CA9EEC60AF778F561970D65439F5 /* manish */ = {
			isa = PBXGroup;
			children = (
				AFCF3060DE59D99EE448EE70F832581E /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		AD4FA2921425A69D3FB6C88B9C366257 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				76027A93368B8A03958EE6E0A31A7E39 /* Flutter.debug.xcconfig */,
				8A84B6E05B215CD488BD8ED58F62C40A /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		AF7BE47A3289BA2B5BCC82C91EC67389 /* Documents */ = {
			isa = PBXGroup;
			children = (
				AC98CA9EEC60AF778F561970D65439F5 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		AFCF3060DE59D99EE448EE70F832581E /* camera */ = {
			isa = PBXGroup;
			children = (
				653AF48A034C5049BE02BA6CA4EBFDF3 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		B0551F0D64153FBE1D2A89E09B95A473 /* .. */ = {
			isa = PBXGroup;
			children = (
				8B5E6C68BE3990112D83512AC62C84E8 /* .. */,
			);
			name = ..;
			path = ".pub-cache";
			sourceTree = "<group>";
		};
		B176F6D3551B4E9A53D45F1B4E109753 /* camera */ = {
			isa = PBXGroup;
			children = (
				358969A1CE72AC6D28D8F24EF03F1CF7 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		B607B0084BFD4C8BBA979E4FAF397872 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				33D63813AB8A5B03D988798D0D348587 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		B64AA5450EDA4B14386E4C84EFE89CC0 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				D92BA2F68B5C751E1D9D208A67FF75C2 /* Pod */,
				AD4FA2921425A69D3FB6C88B9C366257 /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		B67D2B3EAC5CDF18776DBE25FD3FF0F7 /* .. */ = {
			isa = PBXGroup;
			children = (
				58A86620A4A0CA12346768B34655654F /* .. */,
				39C4A5FB4398DE3CF1DC402D250933B5 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B6BDD0902A208F7B5F411C9B2B900F9C /* Products */ = {
			isa = PBXGroup;
			children = (
				AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */,
				F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */,
				4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */,
				768975E636D1D2FB85622FB67DB04E5A /* image_picker_ios */,
				F0C7EFBFF01CFAAB52BA74E6CB40CE2C /* image_picker_ios-image_picker_ios_privacy */,
				5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */,
				AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */,
				3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */,
				337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B7BBD0BB857D4F2266BA631A80A01A3E /* Pod */ = {
			isa = PBXGroup;
			children = (
				940F76A634FD82DD0B3519F59AD02701 /* integration_test.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		B7CAFDAFE0D701040D97272090610BF0 /* include */ = {
			isa = PBXGroup;
			children = (
				5ABF6C26C5FC8CD0E8EBBF34D720FB93 /* FLTIntegrationTestRunner.h */,
				BCFC332AF023182F2FD9136AB22969D9 /* IntegrationTestIosTest.h */,
				C751D3DF40D41E47619371BF3E369CD8 /* IntegrationTestPlugin.h */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		B8C733BED61DF34B0168715B30B69212 /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				97EA34104B616F1CFA03443A5ED203B0 /* FLTImagePickerImageUtil.m */,
				AB2BDAC3E94CDFEEF2D0D1EB269B7E55 /* FLTImagePickerMetaDataUtil.m */,
				EF72AFF4C1CBB2509CB0C1FB59A882B8 /* FLTImagePickerPhotoAssetUtil.m */,
				80696045D940FB8D66DCA6629D3841C5 /* FLTImagePickerPlugin.m */,
				769952C73B10A8322EE4AF43AC0DB2A7 /* FLTPHPickerSaveImageToPathOperation.m */,
				ED0B325ED9D372DE99B5B9910BF442DD /* messages.g.m */,
				D902B000E460B680066C9135FFEE83F0 /* include */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		B93EEB8FF6E0704766C9BFF9E785B771 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				746E6CB4CCF611056E540D67425A5F6D /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		B94C32A41B22083C965F09FED17B8970 /* Resources */ = {
			isa = PBXGroup;
			children = (
				9B1758A882B06B5CFA37ABEF2D1AF913 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		BA123AE90A7DD3DEBC9BE04BD41CF42B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				2CE6840886D00DBE94128BDC7D053E7F /* ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist */,
				40208FBFAD25137C46B2187100F6D8D0 /* video_player_avfoundation.modulemap */,
				762DE57F1B6EF1710FAA8C660E3D0CF7 /* video_player_avfoundation-dummy.m */,
				0A2CCE538E8EADC2A909B202FC931D81 /* video_player_avfoundation-Info.plist */,
				AB86AC50433AB1809F79FB82C6BD13D0 /* video_player_avfoundation-prefix.pch */,
				5A797474E4CD782D2652F8D942F03F80 /* video_player_avfoundation-umbrella.h */,
				FFD634024C7FC9C95E64877ED9979C34 /* video_player_avfoundation.debug.xcconfig */,
				86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/video_player_avfoundation";
			sourceTree = "<group>";
		};
		BDF79271E865058DDDBFEB88728A5EB9 /* plugins */ = {
			isa = PBXGroup;
			children = (
				09E6F0FFED724A4FDAD5217E7F5C1E72 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		BFCFEB82264CA8C42628F20F63BF0A09 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				056FE19E213C9A856839A28D2F1BC4DF /* ios */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		C1D4EF5D05784DCDC9B470DFCB2A97FA /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				E2D16C12BEAA4A4DD4D4DE3EE17536EA /* Sources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		C1F18E1818A46362F114E967C29CB875 /* ios */ = {
			isa = PBXGroup;
			children = (
				5B45D38BD2D3DEDF2A71BBD983D1832E /* Classes */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		C28F4C1C99299F3952A01853606D0C3A /* .. */ = {
			isa = PBXGroup;
			children = (
				156B406A8994E9D254904C4FC6C674B2 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C38B74E5DCAC8C34D12B7C7FF50F8D4E /* Support Files */ = {
			isa = PBXGroup;
			children = (
				4EA5069BC60C8C0D4E0DDE0036B1E2CF /* integration_test.modulemap */,
				B776F59C7D354B3149B805F9925157C8 /* integration_test-dummy.m */,
				5F128005A06313779D460455E8C06AF4 /* integration_test-Info.plist */,
				5EB8F129244F591547FAC495D29BEC0F /* integration_test-prefix.pch */,
				AD5F27DE29DF7036FCAA9EC3188A1497 /* integration_test-umbrella.h */,
				B46A90ADB7E3F51315195150380EB95D /* integration_test.debug.xcconfig */,
				479E9A08E5092365BAA17F346ECDB977 /* integration_test.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/integration_test";
			sourceTree = "<group>";
		};
		C3E94A9724FBE840A08E391521C1AEA3 /* .. */ = {
			isa = PBXGroup;
			children = (
				8357CE30F0D0BDF60C1FA6674E141580 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C5794E8B71BCF11CDB92A59D65DCC6C7 /* .. */ = {
			isa = PBXGroup;
			children = (
				946460961AE572AA7BB17EF7C312A40E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C8BE26AD6605ECE9C54C523D79A34FA3 /* .. */ = {
			isa = PBXGroup;
			children = (
				C28F4C1C99299F3952A01853606D0C3A /* .. */,
			);
			name = ..;
			path = ".pub-cache";
			sourceTree = "<group>";
		};
		CB5399F6448EC968EA3DAF3BED00AD69 /* Resources */ = {
			isa = PBXGroup;
			children = (
				38B96D49BB9F60E72775A6409A700E6B /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		CBBF66FAA033AAE7990899791B9F26F4 /* ios */ = {
			isa = PBXGroup;
			children = (
				9C6F08EC874432C5D876FFB62FC42272 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		CD0E67D46C43F905ED65D3D62572FEDC /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				529A0098450B6BB457ECA7BD56AE08E2 /* Resources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		CDBB9DF48A7905927C6CF7758A8CE941 /* .. */ = {
			isa = PBXGroup;
			children = (
				8DDDC84AADBB309BF219236BC6C5F46C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CE258D8AF67F60C0D32A91B8283BFD04 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E8046A98FE14F55A7160271DCD988537 /* image_picker_ios.modulemap */,
				6560283180DD2C7B64A3137CCDD2601F /* image_picker_ios-dummy.m */,
				6FB76F331940DA3AA104E2B2AB301056 /* image_picker_ios-Info.plist */,
				D102193420D22821DC744427C9BD95D1 /* image_picker_ios-prefix.pch */,
				E77759562AF46EB6E88625AC159D4078 /* image_picker_ios.debug.xcconfig */,
				F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */,
				C2F17DF8D8C4FFA36E773A9C03245567 /* ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/image_picker_ios";
			sourceTree = "<group>";
		};
		CE272C57037118703C3AF97C279B6337 /* Sources */ = {
			isa = PBXGroup;
			children = (
				B8C733BED61DF34B0168715B30B69212 /* image_picker_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				719EA000DC52EE2B2D4D4562E62E699B /* Development Pods */,
				1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */,
				B6BDD0902A208F7B5F411C9B2B900F9C /* Products */,
				791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D03F1AF879B2D875D7B0F8DCC5F9B294 /* ios */ = {
			isa = PBXGroup;
			children = (
				8EFA88BF040D34BC9896B269AF89CB46 /* image_picker_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		D113D8618155F4D0FBDB5357C5C79BCD /* .. */ = {
			isa = PBXGroup;
			children = (
				7159316214143858D48FA7838BBFD3B9 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D124A97930A8B243D9CD20BD69FC9A10 /* .. */ = {
			isa = PBXGroup;
			children = (
				C8BE26AD6605ECE9C54C523D79A34FA3 /* .. */,
				D3E1916E3859B50B3960B51A7062DF08 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D3E1916E3859B50B3960B51A7062DF08 /* Documents */ = {
			isa = PBXGroup;
			children = (
				43C1C8516609FDD99FFD7D71AA79486A /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		D50D52DDB3C78A6D93FCFC771FB3281B /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				2D25E5F498CC3AF4CF7A0DD61C656198 /* AVAssetTrackUtils.h */,
				D9D56AD975B3C019AEAE91370EC11706 /* FVPAVFactory.h */,
				BB9D01EA6F21723A68169CF9C253BDC0 /* FVPDisplayLink.h */,
				6116BFBE9B86CCD66B86E0192F899C4B /* FVPEventBridge.h */,
				96F2BC6BC3827D8A945A2151F3136170 /* FVPFrameUpdater.h */,
				72F3A8334D0963BA074866223A18ADE0 /* FVPNativeVideoView.h */,
				A1E1DACB3E3FA9978D5E0E0FFB92BFE5 /* FVPNativeVideoViewFactory.h */,
				FD13E98B1A7661D87C7ED46E5FDF3B62 /* FVPTextureBasedVideoPlayer.h */,
				C90A86B37C383D245A4BF1CBEA668146 /* FVPTextureBasedVideoPlayer_Test.h */,
				4811AEC7B1D7B27EFAE051A963676305 /* FVPVideoEventListener.h */,
				D9153901AD83C1C50F2A87D9206A1267 /* FVPVideoPlayer.h */,
				AE0DE59512D7F0280B826F47544B67C3 /* FVPVideoPlayer_Internal.h */,
				05447F5163086E0B13149DCE1CE42F70 /* FVPVideoPlayerPlugin.h */,
				5F26A926C0289BDBEA61CC92CC2145D2 /* FVPVideoPlayerPlugin_Test.h */,
				E8EA7BF14C4EF1A812093F0C2E875B08 /* FVPViewProvider.h */,
				F25DB153FD80092DE1946A17E30C4B2E /* messages.g.h */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		D53CAB2CBC36B0D319F0293D17206CD5 /* Documents */ = {
			isa = PBXGroup;
			children = (
				4B03DC0EF86ED2794031D7C2301314B9 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		D66D80178C3558050BA080CEB6C092C0 /* .. */ = {
			isa = PBXGroup;
			children = (
				979F6244FB55DFF36CDF2C38C5940899 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D6785EC045831D2E9FEC7A3618172D65 /* .. */ = {
			isa = PBXGroup;
			children = (
				4A395BD4B5B3843AF7AF9E0181ACDBE4 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D902B000E460B680066C9135FFEE83F0 /* include */ = {
			isa = PBXGroup;
			children = (
				DF207E3C1BA2FF09FA0DC50118DF1E1B /* image_picker_ios-umbrella.h */,
				469957AC084E04A8C8005D330E05EB36 /* image_picker_ios */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		D908C8EAE3CB4454676EC667F8D91D67 /* .. */ = {
			isa = PBXGroup;
			children = (
				FE9956AD008B99904814A359666FFDD6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D92BA2F68B5C751E1D9D208A67FF75C2 /* Pod */ = {
			isa = PBXGroup;
			children = (
				F86807EFB88B041EAD5C18043774E4C7 /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		DCB0B5AAF6F93ED6D3E2C7ADB7B5FFFD /* manish */ = {
			isa = PBXGroup;
			children = (
				B176F6D3551B4E9A53D45F1B4E109753 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		DDDE68509345253E8C120274D7B3B070 /* .. */ = {
			isa = PBXGroup;
			children = (
				2119CA00A9E74DC2753039A02AD1BC2F /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation";
			sourceTree = "<group>";
		};
		DF0789E8621FF918CFEEFD832C7897D4 /* .. */ = {
			isa = PBXGroup;
			children = (
				85EDE02A6778E81A1AC9CDB7701F539C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		DFA915771480A8B9035332F1288C0C45 /* manish */ = {
			isa = PBXGroup;
			children = (
				7551BBFCE3909768663B92FD9D7A39F6 /* camera */,
			);
			name = manish;
			path = manish;
			sourceTree = "<group>";
		};
		DFE6EB657A1318D99632A8F054FA3EA9 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				1A8A72ACFE7C749E431D85701E096CB3 /* Sources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		E07B5FCDFA3B0195135A6224BF224316 /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				96BA76C90808F002009470B44D9CC387 /* Sources */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		E237C72E67EAD57D0D43354ED3DFC305 /* video_player_avfoundation */ = {
			isa = PBXGroup;
			children = (
				8371FD70596E562E0D088CBE39F4E5DF /* Sources */,
			);
			name = video_player_avfoundation;
			path = video_player_avfoundation;
			sourceTree = "<group>";
		};
		E2D16C12BEAA4A4DD4D4DE3EE17536EA /* Sources */ = {
			isa = PBXGroup;
			children = (
				0CA444FC684290F8E6C06C9ED0C67AF0 /* video_player_avfoundation */,
				09FEE29972FD8E410192B7E826944EBB /* video_player_avfoundation_ios */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		E2F23F72085210325567BDE1769C9972 /* camera */ = {
			isa = PBXGroup;
			children = (
				060B939DF8EB41E776462915072408E6 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		E3B0B199FE3EACD56D30B87E9BCE7710 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				F49D38C2F6CC8BF9A40B5518C65E2C57 /* .. */,
				37CB4A594CAAFDB8E896D15A0F16BCAE /* Pod */,
				1AD52F88C668F878FC7902C21ED4CB6B /* Support Files */,
			);
			name = path_provider_foundation;
			path = ../.symlinks/plugins/path_provider_foundation/darwin;
			sourceTree = "<group>";
		};
		E4EF9B629FBDE20E74B9AF43827A02E3 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				8E86B45245C48E825124EDB1A1922722 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		E6FB7EDEF647BF7DA33B438C2F3CFD59 /* .. */ = {
			isa = PBXGroup;
			children = (
				156988D83C7B4981DF27FD9B1D297FAE /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E9385E8B7E226C4AF8D42C6C7FABD51E /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				1C11B964EB2C4F8D2A75DA50E8860886 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		EBD98669BA166C2BD8AF259BB53E1969 /* image_gallery_saver */ = {
			isa = PBXGroup;
			children = (
				67EFF371AF1D298F8F8EFB8DCF3EF95B /* .. */,
				04121552C6EE6042E71BC031C14F493A /* Pod */,
				A50B1004EF4466F179D5380D19CC778A /* Support Files */,
			);
			name = image_gallery_saver;
			path = ../.symlinks/plugins/image_gallery_saver/ios;
			sourceTree = "<group>";
		};
		EDC906EB377E759D6CAA5306C4AC5960 /* Documents */ = {
			isa = PBXGroup;
			children = (
				3459B56B2D1D888FF06BF141C13DC4A0 /* manish */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		EEC33A12110CF9EF8712818BE6C39054 /* ios */ = {
			isa = PBXGroup;
			children = (
				38DC79912D9BF6C7AFF375C5EEE71B1C /* image_picker_ios */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		EEDCE3A461678107A5CCF1C17F437A93 /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				D03F1AF879B2D875D7B0F8DCC5F9B294 /* ios */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
		F20ADEAE1054C3E1F89A7587D378947C /* camera_avfoundation */ = {
			isa = PBXGroup;
			children = (
				35A17A4A673566C7B9E5E780AEE2FDED /* ios */,
			);
			name = camera_avfoundation;
			path = camera_avfoundation;
			sourceTree = "<group>";
		};
		F49D38C2F6CC8BF9A40B5518C65E2C57 /* .. */ = {
			isa = PBXGroup;
			children = (
				389F28F048252B420E3F82B31AF96389 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources";
			sourceTree = "<group>";
		};
		F4C9FC741FD2BA2C788B8B936C53870B /* .. */ = {
			isa = PBXGroup;
			children = (
				85E388AA56DDE3B1E2BFC8D2D2A49C3E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F5BAB90EC08676DDA556319A3211ABF9 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				44780CC92AA27A35B163401BFF25B512 /* camera_avfoundation.modulemap */,
				A8315EE372804831D360B08F364008A2 /* camera_avfoundation-dummy.m */,
				BDCE36D139F84A6956669467DAE1CC99 /* camera_avfoundation-Info.plist */,
				792DB32F2CE9E0FB6214027DD4BB15AB /* camera_avfoundation-prefix.pch */,
				9F256C7BA120B9EB73F86204C8F4539B /* camera_avfoundation-umbrella.h */,
				AB3B04F942B4DC001E956484010599C4 /* camera_avfoundation.debug.xcconfig */,
				FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */,
				52745EE7916310169FB7726CEFACBEAA /* ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/camera_avfoundation";
			sourceTree = "<group>";
		};
		F5D291FA09A5471AE9CF8661979E0CE8 /* camera */ = {
			isa = PBXGroup;
			children = (
				0824FF98EFFA884526CB298217C9EED3 /* ios */,
			);
			name = camera;
			path = camera;
			sourceTree = "<group>";
		};
		F62847B4DFEE52F7DA778BA48F9FB972 /* Sources */ = {
			isa = PBXGroup;
			children = (
				008841D9E0A504899B11B939746C68BA /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		F9251EA00A9F63D676732400F4659674 /* darwin */ = {
			isa = PBXGroup;
			children = (
				E237C72E67EAD57D0D43354ED3DFC305 /* video_player_avfoundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		FE9956AD008B99904814A359666FFDD6 /* .. */ = {
			isa = PBXGroup;
			children = (
				046D414EE1044D6A39C2B3BC676F5CCB /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		FF5B41BE8C5365FFF31BC9B3B268F349 /* .. */ = {
			isa = PBXGroup;
			children = (
				96121E9294B9DD042FDE8DDFBAC25252 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		FF90E657CCB7AE0B27E9C07EB82DE9DA /* image_picker_ios */ = {
			isa = PBXGroup;
			children = (
				5F36792473663056548E724E661BEE83 /* Resources */,
			);
			name = image_picker_ios;
			path = image_picker_ios;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		3D9AFEF292F8A2CA6456DF53DB58B3E7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				F43266D8FAE1091DB24817B71D8BDEA9 /* image_gallery_saver-umbrella.h in Headers */,
				F8DDDFED04D4D9FC484571373E143869 /* ImageGallerySaverPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		48A7ED21957452002067E9FFAB790DEC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				3C8E01595CF33B68A637AD93FA4978CF /* camera_avfoundation.h in Headers */,
				E72011D855D5B3288771B3D0403FF69C /* camera_avfoundation-umbrella.h in Headers */,
				03A083048C780CBF8BC0B604A364D23A /* CameraProperties.h in Headers */,
				FFCA2EDDBD48826E1468E93BFAB04C81 /* FLTAssetWriter.h in Headers */,
				FC51F2ABDBE1E8E1782DD958A1541611 /* FLTCam.h in Headers */,
				D228C2C1BCE91E70A77EB0A0A5471759 /* FLTCam_Test.h in Headers */,
				B9F642F64F494665E7B60843F0EEDBC1 /* FLTCamConfiguration.h in Headers */,
				BBB6C6DB17FF457BACD35352E23728AB /* FLTCameraDeviceDiscovering.h in Headers */,
				306A8154EC3A848B1E8C2C8CCF4C4370 /* FLTCameraPermissionManager.h in Headers */,
				EF4733E951BBE732D3315A2B698CB951 /* FLTCamMediaSettingsAVWrapper.h in Headers */,
				B9BB08B99B2081914777477B372F8263 /* FLTCaptureConnection.h in Headers */,
				93F3B9616434A0926600C441C08115E2 /* FLTCaptureDevice.h in Headers */,
				4B597D76EB7705A3031CDA82C8BCF7D8 /* FLTCaptureDeviceFormat.h in Headers */,
				B6E7879E6847184C425BA5E681E1370F /* FLTCaptureOutput.h in Headers */,
				409E4C13C57C5485ADD48990495351C2 /* FLTCapturePhotoOutput.h in Headers */,
				D004FC2462D5BDD5EB9C5CA668AEFA42 /* FLTCaptureSession.h in Headers */,
				D806DB15B8D531788D1BD8227BA481B7 /* FLTCaptureVideoDataOutput.h in Headers */,
				480B16C831F07F86A754E83AC08624BA /* FLTDeviceOrientationProviding.h in Headers */,
				57D55E0DEE5B778AAB39E4D7C589388D /* FLTEventChannel.h in Headers */,
				C8AE936F1BB6B30D309F27CE84CC8CBD /* FLTFormatUtils.h in Headers */,
				5483203B538ADCA16734DF0775985CB8 /* FLTImageStreamHandler.h in Headers */,
				83FEF39BCEA5BAF40E139751B32CF026 /* FLTPermissionServicing.h in Headers */,
				74CA127F589CFDE980529C1FD2DDE7D2 /* FLTSavePhotoDelegate.h in Headers */,
				D26EA3617436F7A1604DA5B8006FB30D /* FLTSavePhotoDelegate_Test.h in Headers */,
				E9790B1F7E77B14A0E3A4219B364626F /* FLTThreadSafeEventChannel.h in Headers */,
				8592D3BA6B16769F721129D5D5B4B671 /* FLTWritableData.h in Headers */,
				84004623E384C5AA7874121C30C962DE /* messages.g.h in Headers */,
				BF7FB6F8D1350C2ED36FAB8B83B9EA17 /* QueueUtils.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5FFC90B284EA29DB418C32A1C66BB71A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				8A862869EB55A9015F8E29733627911C /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		861911D29B94589BAEFEDB02CCDC856A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				C1893AFEE2F741B3177B07FF3FFB7A8A /* AVAssetTrackUtils.h in Headers */,
				3B7EAE9E43B19CA3CAE87688F63DD31E /* FVPAVFactory.h in Headers */,
				0C95D39F4B6E53824CC6EC13BCDDEE2D /* FVPDisplayLink.h in Headers */,
				21BD83C33CA9734F112FDAA9C4E42D9C /* FVPEventBridge.h in Headers */,
				18441E89026621F065FD728737ED4CD1 /* FVPFrameUpdater.h in Headers */,
				2BA8F29AF420369EA6922201C5A60DDB /* FVPNativeVideoView.h in Headers */,
				B80EAC8BF0B980336C6E7ED3E0D4E0DB /* FVPNativeVideoViewFactory.h in Headers */,
				02E42A5A466B3E89BFBDC7238104BA0C /* FVPTextureBasedVideoPlayer.h in Headers */,
				1FD558625959E27D7AC35564029736D0 /* FVPTextureBasedVideoPlayer_Test.h in Headers */,
				7B080D6C6F61953AE66DF6A261D84F92 /* FVPVideoEventListener.h in Headers */,
				01CBD73509ECFF3BC31C5BB2BE9CCF4C /* FVPVideoPlayer.h in Headers */,
				452D8EE840CEFF02AD1C0FE930BBC5C9 /* FVPVideoPlayer_Internal.h in Headers */,
				620F968F698B50F0E4084992CDC22F08 /* FVPVideoPlayerPlugin.h in Headers */,
				AC6A99F7C15692400B388F737EA304CC /* FVPVideoPlayerPlugin_Test.h in Headers */,
				9C748B0EF7DC986CB78AEEF8D52616D4 /* FVPViewProvider.h in Headers */,
				CBD57765A1D4F65E34B0A6840CD82007 /* messages.g.h in Headers */,
				03B9D4CEBEC8F075594F2FFC32F406B3 /* video_player_avfoundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ABB8D2A0B00BBB06C4066C8B11BED0AC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				1F397B25797647C55BAB87120F8F02D3 /* FLTImagePickerImageUtil.h in Headers */,
				90EE2BFF038373FA1BB41486FF181F93 /* FLTImagePickerMetaDataUtil.h in Headers */,
				022C23F1E61018961E323F39E5C156E3 /* FLTImagePickerPhotoAssetUtil.h in Headers */,
				DABA1D133F168099ED415D88CF078E9B /* FLTImagePickerPlugin.h in Headers */,
				FE39DCD0B607D1137F59C0EF8EBA7D7E /* FLTImagePickerPlugin_Test.h in Headers */,
				7A50EECBB5F23A2B2E2F0FB1627D661A /* FLTPHPickerSaveImageToPathOperation.h in Headers */,
				7187035DE343D15C1E18DD0B4D58F09D /* image_picker_ios-umbrella.h in Headers */,
				EA3FACDA9BF8A6824AD12B35E5BA851A /* messages.g.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F4ADE2337A6718C5688929BDDE859230 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				E240BE0B8D01F2875E4D9613A26BD33D /* FLTIntegrationTestRunner.h in Headers */,
				D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */,
				54C1A725399979F0383179618D742D2D /* IntegrationTestIosTest.h in Headers */,
				CB2EB908281BFF841551013B778FAF4A /* IntegrationTestPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FB10A71DA24C58216A426824478FC86C /* Build configuration list for PBXNativeTarget "camera_avfoundation" */;
			buildPhases = (
				48A7ED21957452002067E9FFAB790DEC /* Headers */,
				E88DAE69939AB076E3D1DA1D4E45A9F6 /* Sources */,
				556D27E7BFC2BEC2841B5B9A9E5A9ACF /* Frameworks */,
				2C98FA4B81E5EC9C1221A578F54B876F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B9D99F1E6C56697A1848B6A1FBB6ABF4 /* PBXTargetDependency */,
				20114E3F7226DB99E2A9172DBCA2F8B8 /* PBXTargetDependency */,
			);
			name = camera_avfoundation;
			productName = camera_avfoundation;
			productReference = AD23E40CD48FC8E37DB61F915EBE813E /* camera_avfoundation */;
			productType = "com.apple.product-type.framework";
		};
		31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8723945AE7A379A2B13E83A9E7CB6833 /* Build configuration list for PBXNativeTarget "video_player_avfoundation-video_player_avfoundation_privacy" */;
			buildPhases = (
				CA1146DA9DDB3598941CF8B806439330 /* Sources */,
				ED107B8EC4AD2BF481D5DB51879E267B /* Frameworks */,
				B4084904859D22345E3B258C835EC410 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "video_player_avfoundation-video_player_avfoundation_privacy";
			productName = video_player_avfoundation_privacy;
			productReference = 337652A9806A3FED0B8DC4CD2DE81878 /* video_player_avfoundation-video_player_avfoundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 167BDBB4761BC473F1DA7B197AE5EF8F /* Build configuration list for PBXNativeTarget "image_gallery_saver" */;
			buildPhases = (
				3D9AFEF292F8A2CA6456DF53DB58B3E7 /* Headers */,
				9C50247F7DFD6C147327089096B45D79 /* Sources */,
				DB6B120BD47FC66302FFF63D264FB73B /* Frameworks */,
				3B08ECE3C7649373663ED208236564E6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				478791FE79A356FC44D9AED3E9C7E394 /* PBXTargetDependency */,
			);
			name = image_gallery_saver;
			productName = image_gallery_saver;
			productReference = 4727FF638E3EEEF5BD8C01E2CBD23503 /* image_gallery_saver */;
			productType = "com.apple.product-type.framework";
		};
		56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */;
			buildPhases = (
				3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */,
				05895E3E1AC56002880CEF9A9AC4086F /* Sources */,
				2C51CA6EC745ACB63E424757C710B36C /* Frameworks */,
				D412229AEE091A9D36A8553E8AA56B31 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				67DE71A3EAB3A453B6B04FE085B21A2F /* PBXTargetDependency */,
				06D3866567140C2717A3A5E76D551479 /* PBXTargetDependency */,
			);
			name = path_provider_foundation;
			productName = path_provider_foundation;
			productReference = AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */;
			productType = "com.apple.product-type.framework";
		};
		845DF30C6C93A1F35C6DCEBAFECA8F8A /* image_picker_ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4B1508164EFB91BCA6B7DFAAAE1EDDF9 /* Build configuration list for PBXNativeTarget "image_picker_ios" */;
			buildPhases = (
				ABB8D2A0B00BBB06C4066C8B11BED0AC /* Headers */,
				A4DD926A06AEB5F3B2521297C2FCF9FA /* Sources */,
				B7C2FF4A20ABA1FEAD6C6E13A1B1A0F9 /* Frameworks */,
				E068C7E9C4F7A1B731D2D2A12E421A00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B5F709D945325C5578D2F9BB2C303120 /* PBXTargetDependency */,
				B0B32D0751570822D29895C336C346D8 /* PBXTargetDependency */,
			);
			name = image_picker_ios;
			productName = image_picker_ios;
			productReference = 768975E636D1D2FB85622FB67DB04E5A /* image_picker_ios */;
			productType = "com.apple.product-type.framework";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8C78822020181F25E8ABFBEA01AB67D /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				5FFC90B284EA29DB418C32A1C66BB71A /* Headers */,
				057DC567B23154BB59B4FDCB1BC5FBD6 /* Sources */,
				2749C4577F56BE5637F958DB04FCB908 /* Frameworks */,
				F1AC3C1F1AE3588562EF409CE2258D67 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8D744357F28BA903B1350075586256C5 /* PBXTargetDependency */,
				6CB53E7DAD0B40836C829D5973A196D8 /* PBXTargetDependency */,
				09D88A947E4F3AEC8D21251133CD31D2 /* PBXTargetDependency */,
				39FD91E1E03598A39315503770E05CA6 /* PBXTargetDependency */,
				23655E5E0D2A51B19541935D3BB42D92 /* PBXTargetDependency */,
				00F073AB7D22F6B977B1922A3F2C94DF /* PBXTargetDependency */,
				84CCB77518CA4A228BEE9121C4EFD7CF /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		A450BF39E3E5256209A256E278D71BFD /* image_picker_ios-image_picker_ios_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1292F8C500D5535EA3F65DDD9D4057E1 /* Build configuration list for PBXNativeTarget "image_picker_ios-image_picker_ios_privacy" */;
			buildPhases = (
				FC903A148CACE3FC60BB229688BABE94 /* Sources */,
				C3967469D6A5BE413E44FED57BA1B7A0 /* Frameworks */,
				83F29562A6C3499E9162C820FB952CDC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "image_picker_ios-image_picker_ios_privacy";
			productName = image_picker_ios_privacy;
			productReference = F0C7EFBFF01CFAAB52BA74E6CB40CE2C /* image_picker_ios-image_picker_ios_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0A15713BF351C15ABDA9FD1BD2692140 /* Build configuration list for PBXNativeTarget "video_player_avfoundation" */;
			buildPhases = (
				861911D29B94589BAEFEDB02CCDC856A /* Headers */,
				CE74C95B22AC89DFBE9C307A3C33B957 /* Sources */,
				81FD8C60300A3062D2FDDB1533F5A919 /* Frameworks */,
				E9A91F4D1FB36E68C88CEFD7C2662E4B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				29754AC6E341DC3535743974480FDA12 /* PBXTargetDependency */,
				5E1217641C833CEEC21FA1DFFE083302 /* PBXTargetDependency */,
			);
			name = video_player_avfoundation;
			productName = video_player_avfoundation;
			productReference = F30519B33821643382CCBFBCFA6B7525 /* video_player_avfoundation */;
			productType = "com.apple.product-type.framework";
		};
		ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */;
			buildPhases = (
				F4ADE2337A6718C5688929BDDE859230 /* Headers */,
				34385A7E8FBAE22793A2738188560FDF /* Sources */,
				D37529713F6219C15572CE565F23780E /* Frameworks */,
				65794A8FCF723FDAECE2B48E7537684A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C6B76C55E16ACFF4D485EAF04CF434C8 /* PBXTargetDependency */,
			);
			name = integration_test;
			productName = integration_test;
			productReference = 5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */;
			productType = "com.apple.product-type.framework";
		};
		CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9ACBE4FD5184461F292A360DD5F3D10B /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */;
			buildPhases = (
				D4429069620DEB60155D0751DD3EC7C1 /* Sources */,
				DDFC044BC64A11EC40D9DF966962EBEF /* Frameworks */,
				34AA6EBB6390D933970D048D0DA45EE3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "path_provider_foundation-path_provider_foundation_privacy";
			productName = path_provider_foundation_privacy;
			productReference = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 828434B969112D9ACACD772AFCAA62EC /* Build configuration list for PBXNativeTarget "camera_avfoundation-camera_avfoundation_privacy" */;
			buildPhases = (
				EDA752DE90810D1DFC0465245229FA37 /* Sources */,
				A78C4AC04297E80F12CCBA45ECC02EF1 /* Frameworks */,
				86362D5FE1578230B4315010F0CC6A99 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "camera_avfoundation-camera_avfoundation_privacy";
			productName = camera_avfoundation_privacy;
			productReference = F98E22378D717282AC5FEBD48292E0CC /* camera_avfoundation-camera_avfoundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = B6BDD0902A208F7B5F411C9B2B900F9C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */,
				D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */,
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */,
				845DF30C6C93A1F35C6DCEBAFECA8F8A /* image_picker_ios */,
				A450BF39E3E5256209A256E278D71BFD /* image_picker_ios-image_picker_ios_privacy */,
				ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */,
				56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */,
				CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */,
				31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2C98FA4B81E5EC9C1221A578F54B876F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9B9717221A43BD8DB1BFF7673FE8E871 /* camera_avfoundation-camera_avfoundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		34AA6EBB6390D933970D048D0DA45EE3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				11E4D6489CA8A1EF744885E92A5396A0 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B08ECE3C7649373663ED208236564E6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		65794A8FCF723FDAECE2B48E7537684A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83F29562A6C3499E9162C820FB952CDC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				914B009F5E84802D4CC5D1E7FD5AE0B4 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		86362D5FE1578230B4315010F0CC6A99 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				083602A0483FE697CDEF80E2F8FF3CD1 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B4084904859D22345E3B258C835EC410 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9D1EC64E08C8DE208D7776FD2632CF42 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D412229AEE091A9D36A8553E8AA56B31 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E068C7E9C4F7A1B731D2D2A12E421A00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3F197F4F335A992690C00BE47D82C02E /* image_picker_ios-image_picker_ios_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E9A91F4D1FB36E68C88CEFD7C2662E4B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C7CAFABA0A77A333BE575FC1A22B1E0D /* video_player_avfoundation-video_player_avfoundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F1AC3C1F1AE3588562EF409CE2258D67 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		057DC567B23154BB59B4FDCB1BC5FBD6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				237D40559884C080FA3E567AEDB732EF /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		05895E3E1AC56002880CEF9A9AC4086F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				73427689B342C9A90D275E8BBC38E498 /* messages.g.swift in Sources */,
				E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */,
				875A2A0D4875DA41E3223F8EE8206152 /* PathProviderPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		34385A7E8FBAE22793A2738188560FDF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D90221E29BE7FABD5A6260A677EF217C /* FLTIntegrationTestRunner.m in Sources */,
				8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */,
				36D53000D376A354E6DF0DB75906EA79 /* IntegrationTestIosTest.m in Sources */,
				BEF37003673BC8C426D6A87DB7C4065C /* IntegrationTestPlugin.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C50247F7DFD6C147327089096B45D79 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				882656F1F7EDA5B1D1D29D160534C190 /* image_gallery_saver-dummy.m in Sources */,
				70D3654AA3300267D29EC5F48B6FC560 /* ImageGallerySaverPlugin.m in Sources */,
				4723E47C3A13863E84C6F94BEDF473BE /* SwiftImageGallerySaverPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A4DD926A06AEB5F3B2521297C2FCF9FA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				222AC4BF833FA50116ED9CBA3B83C9DB /* FLTImagePickerImageUtil.m in Sources */,
				01CD338AFFBD44FFE46C889146E13F94 /* FLTImagePickerMetaDataUtil.m in Sources */,
				2810C57DC5EDF5DF9350B71CEF172EAC /* FLTImagePickerPhotoAssetUtil.m in Sources */,
				E654C7925C1DE3968EBE02F363FC491E /* FLTImagePickerPlugin.m in Sources */,
				E1C7666B701E23B0EC566B874C20B13C /* FLTPHPickerSaveImageToPathOperation.m in Sources */,
				2B5B0F628F0FE3CD032B8CA1FA3A9902 /* image_picker_ios-dummy.m in Sources */,
				8153FAC69CE55CC686FFBB0FE3746A64 /* messages.g.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CA1146DA9DDB3598941CF8B806439330 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE74C95B22AC89DFBE9C307A3C33B957 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D1446FD083744FBAB9E078E5A8EB38B9 /* AVAssetTrackUtils.m in Sources */,
				7DAB0FECB69FA62231B48023413B13F3 /* FVPAVFactory.m in Sources */,
				71366A5094E48AD1F2D29BA3D94FFAB9 /* FVPCADisplayLink.m in Sources */,
				C405BC028C4CBAAA7ACE50D3D33B1F6B /* FVPEventBridge.m in Sources */,
				12219F8E744420BD5AB117EF75F36E23 /* FVPFrameUpdater.m in Sources */,
				B899DDDB5C10C59A638E73C4F30C2E8E /* FVPNativeVideoView.m in Sources */,
				A4CD59CF4F0FE09EEC1E982272B21FD9 /* FVPNativeVideoViewFactory.m in Sources */,
				5A7F034D4B282F6A1CBB4D59A983B252 /* FVPTextureBasedVideoPlayer.m in Sources */,
				1BCE1839A3F7F738AD9BB2A248BC513B /* FVPVideoPlayer.m in Sources */,
				E9B20BD2AEC00D6E6403104891B3BD58 /* FVPVideoPlayerPlugin.m in Sources */,
				2079EA2588F437AE5B5DBF480639838B /* FVPViewProvider.m in Sources */,
				82D9E3FE965E3414C2DD3AE8BF1C2BD2 /* messages.g.m in Sources */,
				6536FF32D002043A333C3EB64B3DCA89 /* video_player_avfoundation-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D4429069620DEB60155D0751DD3EC7C1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E88DAE69939AB076E3D1DA1D4E45A9F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				351AC965BF44984D6E225ED347912561 /* Camera.swift in Sources */,
				7F351626BE5B7CA9303C04C0A8F25031 /* camera_avfoundation-dummy.m in Sources */,
				813C6AEB824110C60B8D98B7FA221B70 /* CameraPlugin.swift in Sources */,
				049D0B5938C4EAECAAB6AEBCBF16006A /* CameraProperties.m in Sources */,
				06B394B97CCE583F6882B1102013A28C /* DefaultCamera.swift in Sources */,
				2B32705F11377CAC6C8678C341103E53 /* FLTAssetWriter.m in Sources */,
				C964E35A6D72ED7ECEBA91E607A828F7 /* FLTCam.m in Sources */,
				B8B43B2E145F8850F52EA02CDAAFFAED /* FLTCamConfiguration.m in Sources */,
				4392CBA4D2AD75B69AC6AE946B7D04A6 /* FLTCameraDeviceDiscovering.m in Sources */,
				2695AA2D706BABA00D80D7AAF0BAAFE0 /* FLTCameraPermissionManager.m in Sources */,
				B16C4147A69E0088076A80A73277F4DA /* FLTCamMediaSettingsAVWrapper.m in Sources */,
				AFA8A38AC1D494ACE56DE9C7B363AE53 /* FLTCaptureConnection.m in Sources */,
				9957A3C6D1C945E94E4CD46871081DB5 /* FLTCaptureDevice.m in Sources */,
				340B843A2A0B0F262093208609DFB359 /* FLTCaptureDeviceFormat.m in Sources */,
				D1508918E22EBF4D9DAA6ACF9DDDE092 /* FLTCapturePhotoOutput.m in Sources */,
				6975110905151D5F3245A0E9C98E4700 /* FLTCaptureSession.m in Sources */,
				8C3070272E95A2C4984F2E562E0671C8 /* FLTCaptureVideoDataOutput.m in Sources */,
				D89384702678F7189703452E6543BCE9 /* FLTDeviceOrientationProviding.m in Sources */,
				16623A4DCFEF3FBB95D9867F8C354552 /* FLTFormatUtils.m in Sources */,
				8FFEDA1B4F9133249B33B9C706E41CCB /* FLTImageStreamHandler.m in Sources */,
				1B5990F8256644B633A24CE4791B7F25 /* FLTPermissionServicing.m in Sources */,
				BB85A52D2EB5F8E18E58B2A2A0AA30C6 /* FLTSavePhotoDelegate.m in Sources */,
				BEB688201E1B7583B9B281D8625ECF15 /* FLTThreadSafeEventChannel.m in Sources */,
				26EA0D20C737C77BAAB5AAA54543EA9C /* FLTWritableData.m in Sources */,
				8BBF4F70DC31CCA3238BFE7CA43F5545 /* messages.g.m in Sources */,
				3DCDD529F97405FF13C0BEBD2B6D956A /* QueueUtils.m in Sources */,
				875030AD692222B0DF5BD9C991C32896 /* QueueUtils.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EDA752DE90810D1DFC0465245229FA37 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FC903A148CACE3FC60BB229688BABE94 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00F073AB7D22F6B977B1922A3F2C94DF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = path_provider_foundation;
			target = 56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */;
			targetProxy = 285592B66B0142F3564CA6620CC5F33B /* PBXContainerItemProxy */;
		};
		06D3866567140C2717A3A5E76D551479 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "path_provider_foundation-path_provider_foundation_privacy";
			target = CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */;
			targetProxy = DC6F48008875CD35B363400E9C923968 /* PBXContainerItemProxy */;
		};
		09D88A947E4F3AEC8D21251133CD31D2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = image_gallery_saver;
			target = 37C246C968D3D905C8DE91562C70C1A2 /* image_gallery_saver */;
			targetProxy = 8BC10CB52F3894A85FE4D300C1FE298D /* PBXContainerItemProxy */;
		};
		20114E3F7226DB99E2A9172DBCA2F8B8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "camera_avfoundation-camera_avfoundation_privacy";
			target = D9677C5D523C2FD0AE2B9C4A7BDA913F /* camera_avfoundation-camera_avfoundation_privacy */;
			targetProxy = F65AB27076998B9D2F444282295F75B6 /* PBXContainerItemProxy */;
		};
		23655E5E0D2A51B19541935D3BB42D92 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = integration_test;
			target = ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */;
			targetProxy = 4B62FC2FB9883142E6EDD013E714C763 /* PBXContainerItemProxy */;
		};
		29754AC6E341DC3535743974480FDA12 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 6EBA39C0DA53265108930F1FB6ACBE54 /* PBXContainerItemProxy */;
		};
		39FD91E1E03598A39315503770E05CA6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = image_picker_ios;
			target = 845DF30C6C93A1F35C6DCEBAFECA8F8A /* image_picker_ios */;
			targetProxy = 855A35197899D2E26708CACEE3210D13 /* PBXContainerItemProxy */;
		};
		478791FE79A356FC44D9AED3E9C7E394 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 8CA86F1E21628B8DD26DCA4C58B8BDFE /* PBXContainerItemProxy */;
		};
		5E1217641C833CEEC21FA1DFFE083302 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "video_player_avfoundation-video_player_avfoundation_privacy";
			target = 31E6C7337BFA5AF5C3B52779DC662202 /* video_player_avfoundation-video_player_avfoundation_privacy */;
			targetProxy = 74E9B33835CD6BC804E76E5F40ABE941 /* PBXContainerItemProxy */;
		};
		67DE71A3EAB3A453B6B04FE085B21A2F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 513F9365941BCB769EFDE8B24D41BFEB /* PBXContainerItemProxy */;
		};
		6CB53E7DAD0B40836C829D5973A196D8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = camera_avfoundation;
			target = 0B1B7EBF63BB39238A152C7553F0A9FD /* camera_avfoundation */;
			targetProxy = B0DE1A3DD4B893B2BD713C7227CD6B1F /* PBXContainerItemProxy */;
		};
		84CCB77518CA4A228BEE9121C4EFD7CF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = video_player_avfoundation;
			target = AAA98FC9182FB27CC0DC22AD1316E0BC /* video_player_avfoundation */;
			targetProxy = B76A49A3C71D893075917CD9CFAA02D0 /* PBXContainerItemProxy */;
		};
		8D744357F28BA903B1350075586256C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 64CA47D4317948674576031BA5CBBFA0 /* PBXContainerItemProxy */;
		};
		B0B32D0751570822D29895C336C346D8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "image_picker_ios-image_picker_ios_privacy";
			target = A450BF39E3E5256209A256E278D71BFD /* image_picker_ios-image_picker_ios_privacy */;
			targetProxy = 95973A722B3F1BB7BABEA4739222974F /* PBXContainerItemProxy */;
		};
		B5F709D945325C5578D2F9BB2C303120 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 2916FCB323686CA5DB01A799A7FF345B /* PBXContainerItemProxy */;
		};
		B9D99F1E6C56697A1848B6A1FBB6ABF4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 3CE5590432218B6D83C80C8019AA33ED /* PBXContainerItemProxy */;
		};
		C6B76C55E16ACFF4D485EAF04CF434C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 264C585BC06523976103ADF078CF6B20 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0316CA68318A621B67362408814959DE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		03937209E5FDAB9889D0F7C834ADFD83 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FFD634024C7FC9C95E64877ED9979C34 /* video_player_avfoundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		03A9B8F1E8714523FF04BE67AC030318 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		03D386AB8086CABEA4D0A70B7139D479 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/image_picker_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_picker_ios/image_picker_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_picker_ios;
				PRODUCT_NAME = image_picker_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		06CD0D42F178A65398B7329338EA368F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 82BD4F8874FE0E4A80B543CCBF7D90F8 /* image_gallery_saver.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		0F12B41F07FF1089F19F6600B40A4D82 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1DF60998696FCBBD5D586032EB312CC6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 479E9A08E5092365BAA17F346ECDB977 /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		26B5628C0A675C3EC6C2193E48548805 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2D84B954EBBA74B2316C66732A780B5A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		2E4879C40540997FD810F484BD8FDDD9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		36615B829953716B6CDF5218A046F39E /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		386AC04547EE6FE3A4080B5C256377EE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 479E9A08E5092365BAA17F346ECDB977 /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		4AD8A2352F2B7BA1478614F070E71F5A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/image_picker_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = image_picker_ios;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = image_picker_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		4EF73162B3046FFF3B9956EAA37F295A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/image_picker_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_picker_ios/image_picker_ios.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_picker_ios;
				PRODUCT_NAME = image_picker_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 76027A93368B8A03958EE6E0A31A7E39 /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8A84B6E05B215CD488BD8ED58F62C40A /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		7FDDA1236F61F8674268E72F5952FB33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 82BD4F8874FE0E4A80B543CCBF7D90F8 /* image_gallery_saver.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		958D52B6AAA6245A71E056860123F8D7 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6C4794628951D7BADD0A5CAB285A374 /* image_picker_ios.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/image_picker_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = image_picker_ios;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = image_picker_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		96F4EFBAFC60EB29612440B5AA76738C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E29B714F6E21C0A8E47EFD05CF27BE8A /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		9F27B74061073A4B00033B43D5C89630 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		9F5674BC21F71501B846798DFB7FF8BA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E77759562AF46EB6E88625AC159D4078 /* image_picker_ios.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/image_picker_ios-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_picker_ios/image_picker_ios.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_picker_ios;
				PRODUCT_NAME = image_picker_ios;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A1202C59D5272B0DEE585943D867C807 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E29B714F6E21C0A8E47EFD05CF27BE8A /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A2D5BEF44D0B608D6F2BEFFAC1123740 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AB3B04F942B4DC001E956484010599C4 /* camera_avfoundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		A91E28FC0F3F51EB26375F3B0B1AC032 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5C1E350A62D9205175A6A202D6EC2A32 /* image_gallery_saver.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/image_gallery_saver/image_gallery_saver-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/image_gallery_saver/image_gallery_saver.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = image_gallery_saver;
				PRODUCT_NAME = image_gallery_saver;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B19F338034A241F06CE78582A345D8C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B46A90ADB7E3F51315195150380EB95D /* integration_test.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B2FA99BEDFD3A677367110EFAEF06DED /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E77759562AF46EB6E88625AC159D4078 /* image_picker_ios.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/image_picker_ios";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = image_picker_ios;
				INFOPLIST_FILE = "Target Support Files/image_picker_ios/ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = image_picker_ios_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		BBF92DCCE4818C530349988CC9707B51 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		BD5D163E35C9DD27F002B90013935E1A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AB3B04F942B4DC001E956484010599C4 /* camera_avfoundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = camera_avfoundation;
				PRODUCT_NAME = camera_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C37E1966042F41F1ABCC460FAC48930B /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		CB69A822B5829FA6A52024BB4887E2D0 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/video_player_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = video_player_avfoundation;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = video_player_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		D026EFFC32440701C84DA5012CFCEF4E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FFD634024C7FC9C95E64877ED9979C34 /* video_player_avfoundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D5C8AA59A339A498993C18BB2506FA79 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86A7D62BFABFD9231D59FCC9064DEC18 /* video_player_avfoundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = video_player_avfoundation;
				PRODUCT_NAME = video_player_avfoundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E8399B4170C2F3F02BECF328DBD9596C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCC20587A7A92F62AE7B1FE65304C5A1 /* camera_avfoundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/camera_avfoundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = camera_avfoundation;
				INFOPLIST_FILE = "Target Support Files/camera_avfoundation/ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = camera_avfoundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		F2BF50A774118A79C973E65E0B3A97E9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5F3FE79D845E5E5834FFCABA3A6BA5EA /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8A84B6E05B215CD488BD8ED58F62C40A /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0A15713BF351C15ABDA9FD1BD2692140 /* Build configuration list for PBXNativeTarget "video_player_avfoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D026EFFC32440701C84DA5012CFCEF4E /* Debug */,
				2D84B954EBBA74B2316C66732A780B5A /* Profile */,
				D5C8AA59A339A498993C18BB2506FA79 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1292F8C500D5535EA3F65DDD9D4057E1 /* Build configuration list for PBXNativeTarget "image_picker_ios-image_picker_ios_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B2FA99BEDFD3A677367110EFAEF06DED /* Debug */,
				958D52B6AAA6245A71E056860123F8D7 /* Profile */,
				4AD8A2352F2B7BA1478614F070E71F5A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		167BDBB4761BC473F1DA7B197AE5EF8F /* Build configuration list for PBXNativeTarget "image_gallery_saver" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A91E28FC0F3F51EB26375F3B0B1AC032 /* Debug */,
				06CD0D42F178A65398B7329338EA368F /* Profile */,
				7FDDA1236F61F8674268E72F5952FB33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4B1508164EFB91BCA6B7DFAAAE1EDDF9 /* Build configuration list for PBXNativeTarget "image_picker_ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F5674BC21F71501B846798DFB7FF8BA /* Debug */,
				4EF73162B3046FFF3B9956EAA37F295A /* Profile */,
				03D386AB8086CABEA4D0A70B7139D479 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		828434B969112D9ACACD772AFCAA62EC /* Build configuration list for PBXNativeTarget "camera_avfoundation-camera_avfoundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A2D5BEF44D0B608D6F2BEFFAC1123740 /* Debug */,
				C37E1966042F41F1ABCC460FAC48930B /* Profile */,
				E8399B4170C2F3F02BECF328DBD9596C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8723945AE7A379A2B13E83A9E7CB6833 /* Build configuration list for PBXNativeTarget "video_player_avfoundation-video_player_avfoundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03937209E5FDAB9889D0F7C834ADFD83 /* Debug */,
				CB69A822B5829FA6A52024BB4887E2D0 /* Profile */,
				0F12B41F07FF1089F19F6600B40A4D82 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9ACBE4FD5184461F292A360DD5F3D10B /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96F4EFBAFC60EB29612440B5AA76738C /* Debug */,
				36615B829953716B6CDF5218A046F39E /* Profile */,
				F2BF50A774118A79C973E65E0B3A97E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B19F338034A241F06CE78582A345D8C2 /* Debug */,
				386AC04547EE6FE3A4080B5C256377EE /* Profile */,
				1DF60998696FCBBD5D586032EB312CC6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8C78822020181F25E8ABFBEA01AB67D /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				26B5628C0A675C3EC6C2193E48548805 /* Debug */,
				9F27B74061073A4B00033B43D5C89630 /* Profile */,
				03A9B8F1E8714523FF04BE67AC030318 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FB10A71DA24C58216A426824478FC86C /* Build configuration list for PBXNativeTarget "camera_avfoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BD5D163E35C9DD27F002B90013935E1A /* Debug */,
				2E4879C40540997FD810F484BD8FDDD9 /* Profile */,
				BBF92DCCE4818C530349988CC9707B51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1202C59D5272B0DEE585943D867C807 /* Debug */,
				0316CA68318A621B67362408814959DE /* Profile */,
				162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
