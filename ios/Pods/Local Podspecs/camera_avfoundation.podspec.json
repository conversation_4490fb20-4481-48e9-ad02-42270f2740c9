{"name": "camera_avfoundation", "version": "0.0.1", "summary": "Flutter Camera", "description": "A Flutter plugin to use the camera from your Flutter app.", "homepage": "https://github.com/flutter/packages", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/camera_avfoundation"}, "documentation_url": "https://pub.dev/packages/camera_avfoundation", "source_files": "camera_avfoundation/Sources/camera_avfoundation*/**/*.{h,m,swift}", "public_header_files": "camera_avfoundation/Sources/camera_avfoundation_objc/include/**/*.h", "swift_versions": "5.0", "xcconfig": {"LIBRARY_SEARCH_PATHS": "$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift", "LD_RUNPATH_SEARCH_PATHS": "/usr/lib/swift"}, "dependencies": {"Flutter": []}, "platforms": {"ios": "12.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"camera_avfoundation_privacy": ["camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy"]}, "swift_version": "5.0"}