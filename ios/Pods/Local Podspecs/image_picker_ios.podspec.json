{"name": "image_picker_ios", "version": "0.0.1", "summary": "Flutter plugin that shows an image picker.", "description": "A Flutter plugin for picking images from the image library, and taking new pictures with the camera.\nDownloaded by pub (not CocoaPods).", "homepage": "https://github.com/flutter/packages", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/image_picker_ios"}, "documentation_url": "https://pub.dev/packages/image_picker_ios", "source_files": "image_picker_ios/Sources/image_picker_ios/**/*.{h,m}", "public_header_files": "image_picker_ios/Sources/image_picker_ios/**/*.h", "module_map": "image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "dependencies": {"Flutter": []}, "platforms": {"ios": "12.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"image_picker_ios_privacy": ["image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy"]}}