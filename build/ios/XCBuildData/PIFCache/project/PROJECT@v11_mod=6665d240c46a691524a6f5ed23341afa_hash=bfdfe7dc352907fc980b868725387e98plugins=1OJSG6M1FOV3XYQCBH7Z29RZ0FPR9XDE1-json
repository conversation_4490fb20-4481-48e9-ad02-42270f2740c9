{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9896200b9d2d0a2e0c331cacbbd9d01145", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fcac8de2b20759ba13168b9688fcab01", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe4d012349d89a6870869d27f5c582f5", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a423bb6f6fa0e327a0cdae5e966e8546", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aadccece5ac0b1e6cfb03e0506af512e", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887a60b26e0f38fd2f7953b634c9d1345", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988897c8aee7c3d292fa1469cee7d901ad", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98225f2b6e17be67d28472181927872558", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6f64f197061af1aab5f68d494dc5890", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef0cbe1c9e9df056880450973be786bb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834994efbb21acd914bee145faf7da1fe", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aea62ac1d5b9908b859a332b03d4920a", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b1eca200c9a057fdf1c69c530b880cd", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd5177194b2e7f12897f0331f2ae180e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cb53e264a38177a7e268b450e1b66b1", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988488935ba22d2afad4f7331a466284da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Camera.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e0acfd55913869db28bb7db1f642bd0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981df99e0a595640f1a4901452a83fe228", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/DefaultCamera.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98923a0f20b40adf73659f158fa8b2e1e4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98321793ebf8e1065aed57e068265783d7", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863b706bf48878bfc0fca25aef66c4cad", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878d2dbbd251156dd26e90cb5a1e1d5ec", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTAssetWriter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bc7efc499d17d2a4fac1c6bc94b79cb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d9412acead563714bf072014f317c41", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2cbf0c4261f3ec93656e76af4d6a720", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraDeviceDiscovering.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98092d596b2dcb4540b60f595fd8deaf5e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraPermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826930a24e06216e7781ea11a0be3ec53", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e229e31c49d4d381d79db566048ec11", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureConnection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98041d3468c82bed1aa71db1e6b5ac8402", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDevice.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98671252c23fe32bf67d846decfeffef39", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDeviceFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861ebe851d2b30c60206043cda77dacad", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCapturePhotoOutput.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983539312bcbd01828adf4362954b153f5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureSession.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853d06841edbcbb185320b9f77c1b636f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureVideoDataOutput.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850fb769c35c550008640aa44b2a39951", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTDeviceOrientationProviding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a615a52cbb653c8508abcbf0a257a67", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTFormatUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3527e86423bf8bab58993cdad55f60d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTImageStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868bb19416ce3f6589c4757f9ddc6c928", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTPermissionServicing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acb1436845b4bd4fda6897283e34b17c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9882437ffb86024eb47abe97939d0808a1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa23f52443865de02fc17f4a716b8ced", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTWritableData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98919ed086a3eeb446a9b1bc5ad32c7ad0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983aad32a0c95d7da6b7cae0df1299231a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98430f0bb5b17933104b0c99ed255b6158", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/camera_avfoundation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d6342f91d6356c14ff77df02bb3c606", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f66da60507ed994ee3b648cae4234b99", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTAssetWriter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f8cc4013338802002bfd96a3ad2364b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98144aa26a12c48b712bfed3b465e9693e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850b014036d72b2b61832b1a1212b8683", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880e93fd5de19f645a85aceea56895bce", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraDeviceDiscovering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4aca9a22553e1e213a02ed1959a338b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraPermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897a62c7a59164975398c37b7b5ef8c85", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98233b5278d5e9d6506078581c35575d47", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureConnection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a91fe4a2297389032c91c8fe3ee2f2d7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDevice.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985baa657abf62c22f5495c4f082721cfa", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDeviceFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985483d3c4e6c7ee02d8b576c1232e9eeb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98785df5d9b35b4ae921e826c96d89db0a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCapturePhotoOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a83c0394f7ea5cfae11c7e584ba134e2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7c92e10c58f65913d3491524aee5cda", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureVideoDataOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae5785dcee934accae9a042d9a64603d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTDeviceOrientationProviding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a299f34a2417e10e4b50b91eecce492a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98342930e3cb7ebf768a7a14ebe922aade", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTFormatUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876149c2e44aba1b6767859d934d3c65c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTImageStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ac3c905de1f760a4e3022a9cbd2ce90", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTPermissionServicing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863615b583a5e33af3f5abd7728087c50", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df7f8422d46a095db2307bb6a6810c94", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982eadc799df3a1c199d8fde76fe39314f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aefc2099eb00a7e827305edcf727173a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTWritableData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d61ec6df667ba1982140650ba414497c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be3cddb74be2e20a61c6fe1815ed5730", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b26cfaa66a8524d541db0325e10ab1f3", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98943664f6db6e1e4b857e0abceb8dd703", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c0c9ade5605ab1745cd22694e0de25e", "name": "camera_avfoundation_objc", "path": "camera_avfoundation_objc", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833358498dc81f520d89810740010ab44", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982162fe8da65b2a8a18a96a6524a536dc", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820891800e8fb9517250afe902fb91e82", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6fd287abb34944e44881ce48ba4c4ef", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819b06ed3cc22137eab6f1f633d2442de", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc717013a9ca5ac32f6bcffda792ca39", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d012fff6c2409dd2b9ffb9f44742181", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815b1a13ca7514e45db6df085d73afe25", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988dd49478ce0bd6933cb2e92ca8c6b3ba", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888a36951f52311b5172c2c5bb5e00ab3", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98335080d1bd78322f2ce6e9884a5dc995", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e537b1ba812b3aec068ab9c5da2ab1a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98943f92f5cd7413b7ffc9c5bb0970d66a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dfd422bae5c4c9f7ca4b04c5604addc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a5c409eeb68e2df80358d9a2ef64be3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1e788e6fcb5758906726c038e7886cd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a67f498553f5d2f2b23405fe1a3fac6f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989038a9ed0ae37d15eced9d12b1e29be5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9873c0e89caedad10a80ba162b48d7e6b3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983706d35ae636ca1acd26ae7a4f34ec56", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9875935e30c9143e2dec958a581f034215", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecc125dd58c73546c6e6d38b6f7d8080", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d6ec32240e73727f5ac73789d5c32393", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9823cb40063504ee0c075aa46a5245a64c", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818f43ed9aaa0c87abad9d09aaaadba9d", "path": "camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c2b1c8badb7d0fec0e7d6f01bcd9062b", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cc49f783b26359a6282578d12e95afee", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981fb31a7d87ef05d83bdc555f2104cc35", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9814026e96ffe82e80908e45646e11bdca", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809bc65a964bf2745a171ed43bb6d3432", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987d0b2660c6947b7cf624437fdee876de", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896f791e08bee7191524dbdc159efb7c2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9840915e5b9dc01c8eb3282f4cc44375e1", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984aafa66536abf464828b07201c6de611", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc5f04008eeb8e64318caa9058ea791f", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cad9b7d2cce7299c17d5dd1ebaa1f172", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2f74233b3b8c4041e83f5f814bc55dc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c5a1da8c5c0abde81a26331fe85ed7a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875ae710b8801ba19d68412270e708224", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/SwiftImageGallerySaverPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ecb3b880feaea70d6e415ebfad3df7d0", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cbb1df3c84bcab197f250e85a1d5e69", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98140025706ab11729f0faf0aedced65c0", "name": "image_gallery_saver", "path": "image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e3d987b0ffafade47ae5ca3186e2586", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854d278f4b1d4d8511c6ae6ed74e90bca", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e30bf0822a574f4d44b8b6ce6c923d0c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98911583ea953a49c438beadff1b2a54d0", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988531f1417feece7b9c2c85fc83fb972a", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd6698131e8206df31ad4ded55851ae4", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985af134c95734682fbcaf3344b13d5c8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dce7b687b339e3118d82ef6248db368", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811e94543ffa4a5618b5855487256023b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea3ca28c19dfa5795475529d38f5a145", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf2c4a4824ab5fec020eb804743a6aa7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d945705ca153bfa2340b2ca74cc8f07e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a23b1dfd1d495a64e6576490b83e711a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/image_gallery_saver.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b11aa55ac10cb09843eab612177a6b9b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9875714377982cc35fdb206beb07c396ed", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9854bf8e01f5a7450298395e1c47859086", "path": "image_gallery_saver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817044b03638f60eff0027e26dde2cee0", "path": "image_gallery_saver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9806f064c12c6d503a27acc11f0412f5ce", "path": "image_gallery_saver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c2036ec455c0fdd6f503072da855f5a", "path": "image_gallery_saver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d13e5b3f7c2d75e7f5500cfd6dc31b8e", "path": "image_gallery_saver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98937b28102cad665295ecff886420d9b7", "path": "image_gallery_saver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa0d5a6313a0631d571e2e0896aa91cb", "path": "image_gallery_saver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c20c34d8549c193de7d86f6752b97ea", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf4faabfc9fdff8ac661e175500ec4a", "name": "image_gallery_saver", "path": "../.symlinks/plugins/image_gallery_saver/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98abc7d074eb85bab2fb3168607057a7d5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a0f896edf9bcf7c1b5dc34b8e3e0bb4c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5e5d00ff487bb212e9fb33c8c15cbb8", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897a102b6290299a6ed153e546771f134", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caa47bd07a44086c9206fda02119a589", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abc31754dc0f0a932b18959b65861b18", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815cdb09af37991865dedbb75b8a85e02", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e3b03339ffaff62a026d7e4d6eb13f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824a313fc453283f27745bf2a64bce680", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c313ca40adad73a8ad47281034d4099", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896f52a3d1f86b4dc6c8b52946e2ad464", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b407d4a0777b1970718d4ae9e800707", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e166799d240c00e3f536bca245c55aeb", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98869884d06a3a9be3b1ad33143ddc30bf", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f56e3f0c772e7de708771f98837b8545", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8fc20b50387eb184a3cf5fe8e3a05dc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98875cbc2c841585729595d842e9f8140b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f56e3a73bb6f7e6aa8df6c719bfe3db8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984510a59a4066a538114b1dfe0a4f7f8d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd1224d62c041683cf613cee087c2354", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987add2a7de95809c17324594a7a3d26ad", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e6e26c659d98c2d628104fbe0def711", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0c6bb8866783dc7ab8ade9ace1988b4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3e8f4f97e34e9f2f15266eccaad1658", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f805aae9a267e6f8e3c0784c3f30f55e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e594eb39192708d48ad6a82a0fda164", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874d8499e4bff6918a8700353b54f1ed6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7e483cc2b198a523bdb34f739b65821", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ce1a11ba1af6f6933e0be6a30824d8d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b86e929a64b1f65ed5c5294eb98effd9", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b031856ac4cede2d4646d29ce160fe", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb2c9a76efd3c337509cb86f961cb978", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd6082324eadb316f1dd5a5a03e2ddbe", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e373684684492c44645fb96b4b8b1809", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d7d85bb24f3dc182eb889442518bd2a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f66e1817645400cd11cc4909aa01bc45", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff27c7b7aa24f37a63a8564e99bf8caa", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884b86aa95036a8607e12e3a51d75cdbd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987343cc8c0c5fcc2453a258a8a280eaaf", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab0dbfda69b5c254ebaa48579cfd52d9", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c90effd8a8e24d12df7bc1821c28e927", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827a3ef6d2a8e5cb67793fcc2ce6c618f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbd1cc929405bc3e82238e3b499aa41a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a182346257c937cb634411da9d936142", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98986efe86d945d46bebca1fd68ee20dc8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986086a93c4ce0a187e6329cbeaae3780a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ae02f17c569031062825156c3905281", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f04db02d699565c7b314d7f48a3819ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824b61a0a1bc73fdae298804a151c1b52", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a19fbfcab63d7b9d152cc373c95d7325", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d535d9ee3c8c954baad594dc18764715", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98699b2ff749648826c97d94e31548a2eb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98895da2a11a30c8bc0333c5b2636c42ca", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98354b1fe3932d2d71e602d51117a1eced", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987bc2c637a0722a8410729d0854066c56", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987bb74aee4adaf9ce73abf0f1190cc414", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec6249bdc1d0cafaa74df02c225e8bfd", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d069280dc9e1b06efaa3a255b347c01f", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e9c0a64f6bcb15d609f1f96e1435ccb8", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e08efcc71ccc2728b3548a0944e049d9", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983d3834c62f7a809f55bd04f89ea8bbdb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98109262d28915ee25f2a5ced8f8065fbb", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcf55fe675066b423a45eece40180f6a", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981791f22b405dd449b4eb66fcc6f87489", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825afc92e03fc2340fc772f395a8be719", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a91310d7dda40fe59bb5747946dafd14", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d27d54f9a763c97bcf839111a4f083f", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e62515e86da9500731abbd507b87cc6c", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cffca54412602b8efa70fe1caa394c8a", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869cbf6b21e7d5697f31359ad2315218d", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f3525d20872ac2afb485090ba993f35", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f31c708521d4276db48f8f3899ca403", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a350ff98c551a9018ae3fa0f4a6d74b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb4db201c7b87385350f442b8258d578", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98362377975096adf75d6dfcdb486a264a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839fbd89d757593d820c25f4f044bcefe", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98baa88c5b33f9155f93d98d1519f2da89", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d07e7a95a1e37fbff102a5678eaa9bbe", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e037f2f618e04029b66536da6f213dd0", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5a7fc33e56fcb3c8401465afd54acda", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecde1ccc692269a1b1bd008cdbd429b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983750b9fe2a54990dc7f6ca78823be811", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f49f1a93cddb134a92548a211918fdf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e476e492e6fb8c6d836a0f7190afbe0c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac12e45c968986a079d19d1c29b35bf9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffd9d4956e815b39b01a0dfecfa77226", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811020af47b0e2de9f90113588fa3af55", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98862ff534ed85df10c38f97b09ec5d7aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895347bb77bfb3201474605ca4fb0d1d0", "name": "..", "path": "../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ed1ec1c4991adb0e232fd0d2c9825aa2", "path": "../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ab2f7276c6901db9fb7d79ae950f62eb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980022b5e17d5b48c9d6efded98b934b77", "path": "integration_test.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821757d5c2681db7b0e3b6c2f9b44c8dc", "path": "integration_test-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9821dd1fe29a021d8f9a00d7476ec83f93", "path": "integration_test-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893dc98020b482c17f5628340f900e968", "path": "integration_test-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e4797af8688b22188e97e5a88180191", "path": "integration_test-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a80b418e80c11860f2e3202c32f021d2", "path": "integration_test.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987e08ea4de3e947063b9dd009210fc1b7", "path": "integration_test.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9872494bec2e3474b597d9f05ef5402f8f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988230299d4f725bc200badc39457acb22", "name": "integration_test", "path": "../.symlinks/plugins/integration_test/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986061d45e51e5fc925556547da87ba3e9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b0e3e0fb6981a44e01c709dfd6577023", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98082073131ff859719b4717989d3d1314", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896f1ad71b774ff351a6da3f413e0550f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98563175d34c65d77201df83eb478fd04d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaddb2c0377f94d1f3893a39030648f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811dc02aa3d8a4aa71e0f1f032a79c834", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843698b5ec73cd71fe89c940c714c28d6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842b56f494f22c0aac6be89beb00bf6b0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f29e63e348f17a9f8d54599c97805b2c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ada8dc567db67c318f140e38cd4d1506", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d67b2c987b04d8a5e8215852f2fae6e", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98859f09ff639c2f411486292290c57ae7", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98166724a13584240bbec620ec1339a470", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2c7bcfdd52a5acf3d040623f79b27e8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4481134da41aba4acec74c18a8343a5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983567bd65133f80a25479f58492637744", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d330cf5b5cbc0a0812793ede39f13c6", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb0a7d03a1821b07d481c182316411db", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899112141b1b45ebc2b427d3f15579007", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d98c1577db370e366501a48586ce7d9e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98028f7d54df4c65d3fe664cbcf33c419c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989250860ea58a481ec3dc3806fac88262", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fd62ae0ff4baad156708ff57245d373", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815737b41c7b0ebfca5f1d9b6e7de5f40", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811d4f54e0aa69bfcb196e97c008b2f17", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98685f25d9ce06a3c0e2ba98c11d7279f8", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bdf454a3f33154c37c379b866358a1b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eab7acf3823d75c8d455d21638956817", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98094965385cb337d1af2338edbbad3aab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985aca74e1c102043a94582df424b11670", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989651bd7123d81e6cf670421b659f0030", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98572b5741a791cd03c41711df40e71817", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f726179ffc9a64b09d1c3415dbbae76", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8f46627ae2ee0cd7f6370a512b5a950", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98718baedc7e85c385f3bd12cfee79c0ef", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983edf1821a099e78e96f8997c97826d3a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cca88af47c24e2ee74392c44fcce1e38", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9851cf66aaab6bed8bc9b90295409d83f2", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bd5528590c7b280d5046a9b94ad9b61", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c19661faaf13013518e601aa58bd156", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de7589007d48d15d5a99eab4b1ae95b0", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98998ec55062e1681468b25d56d6f936f1", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9820647911a124f9bf8ceea1c1e7f28b7f", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98462371abd5beaf4f4fda3b7551c23854", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9892005a8c961c80997fe758f77304d5fb", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a312ca0c331f0c86e7399a387a897af", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987255bc7fc73bb42183227378f5bec181", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988fe23ebd23fccb6e57909577a5f99462", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98179c21736246c17ecf73d26c518fb6ac", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980502fdd99ef370fb49f52189510fe40e", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818903da62467c69e671779b023bf9936", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887b4509d386a4f5b6940eef22db627bb", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e597364b0498f7a84bca7d2cd211a18", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea39793a077d2c76e054873110e751d8", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98960738fc6afd215484bc2c99bed91781", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2338df4ffcca2fa04dc319349f8b1ef", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846c0387654c30c088b38720fd0da8e2e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b37eff6a402e972b6562ea832e5da549", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd0b99cf11deb9501bd19dcf40781d37", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988304ebfc8a3a4a4f8a5b69cfe08e91a2", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879563ec8da377978b076b55ba9c9bbbf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984226e2ba151b742ee0743e78d5693011", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988147d9d702275f3bee5b55366c34ec7f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828b16a34477c017530097cb979b0c1f5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a9b3ad7621fb5adccdabc51e3b2e61a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPCADisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e5060629cda9defc7eb25dc3ca4c2bd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPEventBridge.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2e3ffc598f18b9b1dbf305f91634f4f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985312ed809ad027968c8b85c32715a8b7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPNativeVideoViewFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981488d135a53fe52a487827506a4a5ce2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPTextureBasedVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b2861363028fab0e46084716910492f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836eb2360de7ef87b9393ba9987e20b55", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852e00af3efabb9da806a4e7cacd3604d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPViewProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984bea572271e5032d5223657b97591eb9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f3f3b32e7f99ff2bb4b7157d0ebeac3a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0343a9f29ce637389bb328e85c0d22d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3e8b65f05af06f7c5a709f49376bad6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae05776e64848920ac9627c65e64ef7c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPEventBridge.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fecfea98023b699185a26874d3c43232", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98768f943f3f01e51511e85593f9e09b3d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a19c7f8be7ff988ef2a50565f3aebcc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoViewFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc897e7f1791a5354b9a36b6cbaf81a7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894c9d594e50d2ca8e82c8562bba494e0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988782eccb42d92e14423649b041848b67", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoEventListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e60fbf2cb642d09b280f7a163478be6d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c66bcbfe94b76057d8ff0853e282781a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c5c32faa53d452c9e8fb47c1b620e83", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988fcd0488cddc66f4e77258fc266c1dc8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98960e08253413cfafce0c207b9d76fd47", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPViewProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1edeb15e9ae54fd2203f07ae0147921", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a7eae1be9a41c9da4fe427546e4e6d54", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802b424679f6532ecfdb16d770493dbb0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee6dccff0e400753326c0ecbde793fd1", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe8c87a249a58728d56759c73ffa08e0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoView.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981024a907a05fcf2c500aff5cc06a2e89", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9d0244b0a0318e377d4c0bd347551da", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c3699d680b1e977610438ae5e35eb6c", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856c9d78aedc78579921d7e8f9a7627ec", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846e4ed6915adf19b4295378ef4f907a6", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f66d0f551379fdd2194b48f9a420075c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9706716dfbe51011658d1ac2d283fc4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861689b3b4b09047210ff778b2e13f041", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d68ac21241249b5dab3f3b8dd5a33a0", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893dde1370bd8f764d315adfb4293da88", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f3d00848f38c2cd99d32bc61056fa1b", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98317c993e27f84d4f7f52392705f79bdd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98409f8177ee36e69ceaad0fe4c96afea8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98173a9be0919712a1db22f4a709219539", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaf6bae35ce1751a9ef3c93c1c1359af", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98988ed887926430ac3508ce3eb1e99a77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dff587b9fd2cbf6af70ffc2534dca056", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b8ab258b9efa309bc935c3ee61c1556", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988995f6b04cc9e3e77c19dd5f4e8d0b98", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984ded248d1f69507b7a90c9f0ee8bcaf6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984e9bee7e4386c6b99095d64765c78cd4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9800d2356184ab2fe4c70fd77990f91762", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98eb95081457a6573fba264f7f403ef666", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a6ff1d814756e5732873230363243f9", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982d16663a1d3a096a7357285d0eb62339", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895f7472b364bef02cb3a78fbd5fbc5df", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5c12231b95abebab165d223a67fadf0", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a930d5080e2acf463c66f374810ecf69", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c4921c4a26b0734d9e00edf090570bba", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f05a95e0cf06b06605af278d1097c966", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb22124b5279108226ac5170fb5bbad9", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a28398ad493e99b47822f8305d7eb7bf", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983953e1a581f4e5498bf4d0ec845524be", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc7d34944f58a053e3e511b07e5e1943", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093e0d274405de132c9336fa40762070", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e9883b29c17ebfe27e1c1dcdebd2f2e4c6c", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983e3485d5de14c283a1380298e18a4526", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98810f0c8e3d9cb80091b9909448cd6626", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb913630fcec08bc27ac2263f59bdea4", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857bc931e356d7b7843fda47492893ed5", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98d15413e70e37fef51b4d07973f5f0bba", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987d99e57ff5a2fbc3c868ddd022b1a2a0", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a9b3ef9ba473b1f1f86ce82470c7d25", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986e1523d54ce0c59eb376d15073c00a62", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa6d3f4a0bb22c1d653f7652a809829a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ac3979f5c1325345624fa4eb4525c7db", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f0ddcbd73ecfe1dd17d3452c7fbe2d4", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d38343279834dca6061bda437330c834", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Documents/manish/camera/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/manish/camera/ios/Pods", "targets": ["TARGET@v11_hash=2d2ba25bad7de218d74cf31433ce4de8", "TARGET@v11_hash=20f1cd2302accab9120f4a0c77171157", "TARGET@v11_hash=344ded8e64960a57228e36013decc299", "TARGET@v11_hash=11856b74d2301e110ce714b21332bb16", "TARGET@v11_hash=914830e155cdf9d81f92aa66e76e086d", "TARGET@v11_hash=245706530ca2e4da4d427df06be0e109", "TARGET@v11_hash=a42f8fa63f13e517e73c4f8f3ccf51ae", "TARGET@v11_hash=2a795607402853ffa82f7d1e7715a55f", "TARGET@v11_hash=a3c85e83d732e1dfa9b8f3143a825de1", "TARGET@v11_hash=6b9c188efaf173a84688e22df6fc7654", "TARGET@v11_hash=69e355b6e63b41d1534a3473aa31adcf", "TARGET@v11_hash=0343c1ba8f3c784a4ad60949c228eecc"]}