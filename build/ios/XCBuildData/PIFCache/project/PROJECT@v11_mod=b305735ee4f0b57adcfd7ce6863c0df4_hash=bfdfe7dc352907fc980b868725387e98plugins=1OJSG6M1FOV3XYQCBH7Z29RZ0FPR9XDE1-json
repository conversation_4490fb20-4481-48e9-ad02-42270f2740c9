{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b04ced6eed7454fa7b0d3466a04d8fd1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f5056d9094dd3b73f254717daf656a5a", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856a3f871b1660c81b6e6a46dee384077", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c1dd92e3c007177127667b4f429cb4a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853a668b464e2f31606bceac346195489", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0949494581d0a07debe7f28bdd1458d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7a3d67f5350aa1e77f1dd0de8cd05a1", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b013955547089f0d500d405cb40e00ad", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e1a859f4a3f44a3f80b00e7b55203d9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aae0bb0b0f08e4e247e7725ba745cd03", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831801ec675cff019a22652f2674691b3", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c6cedbf84f5f2e4aedf1a07555fda6", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3a3b717b1e4ee155d0d0ae367f96f62", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a80e31a10d0b529a89828b32c81f535", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ccab241d3cf8abe414493851954b45b", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987aefd5fb314167bca743cf6ab61c05bd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/Camera.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3399a5baa9d150220277269ff4f1e17", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98464edf36a0bf1cf41c0baac5d0a4960b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/DefaultCamera.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98caafd1b414b4d701384bb57d2fc72ea8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98998384cf4f7f50eb7c404c1c513ff79e", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a3c159ef47349c83946f47f7aa3ac8a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895c1578de735b6983e080389fb98ed98", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTAssetWriter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989afaa5efa17320c08ada625356d320d7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890755a3551145dd6aa15818e174ebb12", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f17e54c3c127d8fbb00e60386d54320", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraDeviceDiscovering.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98393d2247e615e66e21f45ad4b6e92302", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCameraPermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820e779d295ec6d8fe4b3282a3de7f7ab", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987af381def737b7e1744ee79f718ce27e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureConnection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989884ffc7067a9551e7279bf008920c99", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDevice.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881200ea14dfa24acb60d149f320fcd38", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureDeviceFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985768b440f78bbcb7bfa3c8baee8c75e9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCapturePhotoOutput.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fce6a16a96d66839030c408756fcf37", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureSession.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983363629f15c33c509cafefea82c66cb7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTCaptureVideoDataOutput.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b758c7d5fa6902a5e1cf38272c72d5e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTDeviceOrientationProviding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db0ac3e9878e41630596e25be7195e93", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTFormatUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f79542f6c86e3e629efeec59e6e8ad6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTImageStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe8dbcb3080d5888c9d52a3479af9268", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTPermissionServicing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d219d5fc5072d31b049d17a1be50ac56", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d28b4dcf0c6a59945d2359d06ce0da8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98297560b847dc893295d3d619b146cf81", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/FLTWritableData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986af4a91fc9d4e0d6fb7183c68609fae0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880beac51fe1b56d35c4e53ec1a20ccf0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdd24166e882df96b3e735a24466b3c9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/camera_avfoundation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98726906f32e7f6680385744b0243054c5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a523f1fbf75197eac0e317440d71909", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTAssetWriter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98934c2beb3e2b7ed571ff0ca28546b0cf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c31fac731c5723f88a0739cc42b80b1c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98761e7ca130e92737e000f901b4c0ce43", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820ede442239f67abfe8d7a80eafd332d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraDeviceDiscovering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be5c1124b2a1710941abb282869a959f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCameraPermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7f05b494c1c583a3825aadf81024a40", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d13364127859f4d2943188178cb06d87", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureConnection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e258911e76bb412b013d0912757f47a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDevice.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d10ad5d0c87c73e42b8d3756aabceaa7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureDeviceFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c82d76ed8ba04d0b8fd6ad094469b573", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982936c3c7b79f6a16d7da3154478b3949", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCapturePhotoOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dc98880ad3da01037512fe1a526fb49", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d896e7773f5bbda85d223c82a302856d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTCaptureVideoDataOutput.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804645c79b195603ab2443a68fd9b93cf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTDeviceOrientationProviding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a94cc74bf14aea3187c470e82ed2887e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850160a6560f2e9fac99874504292d5cf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTFormatUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8f7f4e1076b4e578e299a34074b685a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTImageStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b49a5596609c8832aed4d8a7a633aad1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTPermissionServicing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aed9f03025c3f8f294ba0b2adb00cb7c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef8dc45dd92095611e69b372ce3c52ce", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983286e47c8acee7d1b60820265be2bd12", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b73cbc8145e4a5c0e63db7b3c97f039", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/FLTWritableData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983aeb3371d9b4bd59db38837377ad0875", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986584422537928c71a9f0aebd37cdb9f5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation/Sources/camera_avfoundation_objc/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9865f1296a06b9e52e29a0f4178f4dbe9d", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebf8d5a93ebc877de197210ecb3bbb2e", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a3423233b13a4c9f4ccbe2dd161b995", "name": "camera_avfoundation_objc", "path": "camera_avfoundation_objc", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e92ffd002e2e4b6bd78b793c4d9055fa", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc3afd59314689189109ac021cb34137", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b2cd30028b387b15272eb24f8b1f641", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98690bacfc1dc7e388982a469db53b1a03", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817c9de9cf04465cb032c27664c165291", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a967f472e9cc31a98968dc048bb0bc89", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a033ebb78cc40fcdc1981fc1128bd0b0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98064d1823b936ca7728f37d4f75e95b02", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6482f314d267c90019bb330f7f0bbe6", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adb3981792d75fc02ac886073101bdec", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882dc305c1559c917b98f03b7816578ba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b0860b510329453b51958a83561ece4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808f9706fc2e5eac6d5161c5b9b4bf7cb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed73d9353ebae1eabf6a504b315d709f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bdaef8ac295e8cf63ba8dcd90b9deaf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed794290c04d3de6b1a3a98e851bb618", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c20aae62c3d0da90c92fc3ccf742ece", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982b4cf25bdc0593a5c7c8a8dfa6a81afb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986bec7ab3b49f5418374fd45cadc6185e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9823bd69faf00231c98327a6593237222d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d679218bef2d8535289f0346ef2e5f90", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fb5a189ab1a7621710d57d67403cd50", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bc5f9a85fd41d1abb7cb0052f981e27c", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e3fde8af24c2907f55e1b03f254ef8c", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e8cca56dba2af75f862871e698fd796", "path": "camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98604150b1c79337fe49a8d5628c190f9e", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9862c2aed8ae45fcc1e6caf036727b6c6c", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980a39d2781a47b4ce5dcc8a1c2e0bdd84", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d9072e63c3f2aefd48c890aaf7299208", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae6b42c55e33d48a1ed538075def178d", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b1b9c36ff3a341c994bbfad533c6410e", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b84fd94ac7f83d2b78017c3aefbf7fb2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893524c054ece733f7b32ce16a6f9a803", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98626ba0f2167d4b3ec806bea5610b975b", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d75c557ea388d9b9866d3d279b8c3e2", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987df63e87fd5b0e91155af1cd4eb9235d", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4196739571cb4c266b6cd584e66d7f9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a712f53c88bf32c1f146bd0b5aa1d1f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988beb35e103e75e76b45f858579592f08", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/SwiftImageGallerySaverPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eafe6a9063d1317605a6a85e03e38b45", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826ae20a7e8a6393c49abc8028e79e922", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822304b17ac6a2fdb2600a3f89cf67ccd", "name": "image_gallery_saver", "path": "image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c5ed068cb36d04ff96d471a41e140e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899199716f31fdb3ca5e7f689af9180bc", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b14df5cf1deb363daa33a4b726898ce", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d923ae476312b3b09cf42f676497cbe", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e25770ae108481e9c1ef63cc32335cd0", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870b4d2e1609753b98ff075d788e221cf", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885f88ad41eeb6bb4f23d5af568a8497a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d0fdc001a481f14feccf85054099d69", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987410a4a978edd686883b77ae0e689d9c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894bac380dceabc5ee9b571323d43893f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886cca2e2d28cd81b7b9b708893d7799b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed60613948e86d550bad111d35b0b468", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c903a5951be9b532120cecb900a85502", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/image_gallery_saver.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986451df249ea5e3412f7f51649c7629c6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b8e28126ffc0cc197370e60c1f4761f7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984f5aa8569001842e951934591ebd4605", "path": "image_gallery_saver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98402b80ad239b35bc17ce7af2eb7a47f5", "path": "image_gallery_saver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980018dbcc50aaa4b0b4364b1d1e5d89c4", "path": "image_gallery_saver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae0ca825c3bc4ba937395e4a87ea8a61", "path": "image_gallery_saver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f52b6c6c10ed6ac9210917a296388a1d", "path": "image_gallery_saver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b6649d8eec688668b3f696b56be65506", "path": "image_gallery_saver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819b5635f8239a3d52bfacfe697d677d7", "path": "image_gallery_saver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb02ec99e1d33c89a728f15d787bc28a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881601f55c18fbe67a225b64587994727", "name": "image_gallery_saver", "path": "../.symlinks/plugins/image_gallery_saver/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869a2f4c072dadf7c435d216a77385614", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b3f1d7c7ddaab3b54b65b0365db7c2b", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c013e29ff2475b30c03671e5edd59a4", "path": "../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987741fdde4513cf7f553868b4da41912f", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830b73b605644b18adb94e7ccbc9cca57", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98847b7d067ba373490ae3355bd68b97b8", "path": "../../../../../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989e16a33e8d742f8ab957748604852cb4", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9a5305a3a39f16cd9adff7a6ecdcc68", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edae7622534468523afe40915fc71734", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0b713bb3e0d68b5969f6a501c66d84d", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b8bba89022f2ee94ef79ad41be5b674", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983556d3da21a1dbe0f2023d5154507ee8", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcc532759c0a45cfee2d5d932b41695f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aae822792aab6b150b44415b7805e94f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cd97511f9204cb5474974152404cd1d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea8497173c4e9e0bc6c23ce1ff1f97f7", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984957d460316656561fa107d9f227f195", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a009aaf0922f1a217662513dd978409", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdd078f223fa1442fd9043f70b08c035", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a0c2db2569c665a4551aa4b394be048", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849283559d8c63ecf6e0c4bdf9fe7201f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be9eff2120d96db985dee373022ac192", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a35d3562af98a6011df9c305c3fc9ca8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891dfffc9e949c5574729eff2485e1eda", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0eb74e89f98e80ecdfa7b36fbb81c43", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986db1c712454ba439d42fa286ef722a73", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98291549881a06e21f50168ce2c83ea815", "name": "..", "path": "../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bf67717ab6d1a4574638455160d63f74", "path": "../../../../../../../../fvm/versions/3.29.0/packages/integration_test/ios/integration_test.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986554d84398ca93ad17ae8d54c5e50163", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985182de813b29b80c0d1a6b82d79fccfc", "path": "integration_test.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983dc5e1ac9b801e02ee317bbbbfcd75a0", "path": "integration_test-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981404b48ea298def1bec57e7dbb07279b", "path": "integration_test-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879f2f12645ab73a55b0565e29c297bab", "path": "integration_test-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb1ecd8e3e38e94a29909bdb98aeee01", "path": "integration_test-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986b519bfb714195f6469d5d9b178e3735", "path": "integration_test.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9818b7d5a6819fc4f2cc499ee7c39786eb", "path": "integration_test.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987d117877e9e045793fa580ec03b0fe45", "name": "Support Files", "path": "../../../../Pods/Target Support Files/integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ce5250c8e0c2b97610886a6358106e1", "name": "integration_test", "path": "../.symlinks/plugins/integration_test/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98028b4a69f8945c9833b478652cd2da08", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859f43878192be3d83f0c5f422500c630", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98861f68d512af5313e24d1a7f2554a8c3", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da9a33b48a63cff740f18ccd23176231", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc6761383d50a7db8b0270c93a1909c7", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837694ab77da098d043d39e92d089982b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809d897085dcee2c29a144148a88b90e6", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98601b436dd8cda549d17e61f837bbeddd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989346d6895e78d2240e1d954551b0e6ba", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811c0c3bfa0ef8e6c7923cdcf13e0534b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889ef8b109b886064b9e5a6cf40bebc6d", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d0eb2a18843ced263b40f1972dc5239", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873f6137f9b2041ccd3b9681900f213ce", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bb10dd20d1ea45880fdf299cdec896f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da9b616bdc755674de9231440a199418", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b384fc437863906f3e00bb772da06fb4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ec6f9224bd081fda4f26c1cbb3d267c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e770b02e2b9b252313709264d3dbc42", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb16d7f61e5a05ef8320a2f25ba9e20e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982698947a7f0c849cd2ce700f5fbabf77", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c87a3818b292ed433505a5fa5d13641", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee0c1d86d84b568358c8637bb950b935", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98158538b98248d491f855d304be66fb05", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6be0b4486f04c08d5724412d1807708", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986aa57225ffbee99a083e214ca963a7a7", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d27c4efd34003f32d0b388551e41b4b", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aca3328db431e536df8c7244476a9712", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843687a6cb920b16aef689d62279ec79e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855317e3533a3511eb51df146ffee8738", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a7198e90c4b575de18a4af07945cebd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837ccfe2b8db0312c385defac730b59ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2f86313a3733da8dff4ec45cf8ac38c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3786dff10bdeacd8f821646e9f229c0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcfd170ec611acb313eb3cdcde27554", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e967e386dc1d218d1a556b40d564b2d3", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983aaa931873b7802d70a25e2a2405f9ca", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984db9dd60dae626203bd1a9022e777419", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986a876db0e8889041c552cc2b8d66ca1d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981ec74373bb4a2201666b54e80294a70d", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d25475c5fbb40f45228771e618283c82", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9837a3c00cc2c7c7f30a5b2cdfebb8708b", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a544787393a362f7e0997960e3b3a1c", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987343f40989e75d6feef1a6dc3e3bebb2", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b664b199353a1331c1ec9e07079a27c9", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9870458463ecf36100bfb2f0990aae4976", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a73e6d1e02bac56b0a679e45a0c45c0", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d805e4452973b333c6206c4fb85be2c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b0b861031b77ff5b61c02f9f1628b24", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9861ec6301ce8d35fc5d2e68527e64f72c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b367ed055c1326595aff5e5a006f045c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981edc526ba2611fedfda80d3ae0b364da", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a34521618cff2c63a23b96729e6ae31", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b53e9e894ce9dd5663c4cea1a638e8", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865c26fb113ec197aea90f26102d838b1", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc5014ae37c16bc9a11f9e0f6be16ad0", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe2f94be752b844f5f61e1ab20890ea7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835a163a59f38d1d2e67244099e365e3c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981294172d3157bea13b20b63a3e8d5f39", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988153e5dbe668ecdd79b25002d5c2fbb9", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfca8c87258c0e146cb80fca74be0ec8", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98529a67393a0f3db06f9414abdc5b43ec", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abdeffb01feb7e7b1613add7b1284cc2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2fe6fbe3af3fba6bda3bbd96201e217", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b12a949984389b6f32321303a09a4b44", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3ee98f93af2e2e9a0b2eb8847b35f7d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986233d1b9beac69d5c8ac69c28908dd52", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPCADisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a153e3f3a6eb07904f1225b703d12a0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPEventBridge.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98103d007aff46955542d71119abd612d0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98babdd38ef45b9a0d544056cb82c28f85", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPNativeVideoViewFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824ccc9bb1f577f6394f4830057efcd58", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPTextureBasedVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c90794bb6cf3fdc78c2f07c6dbc2056a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98083dbc94d2e794767d8906789a46370c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc0df94c7200a81188a7f40abd5cc93b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPViewProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f7e6b6ad9df3859dfd83235b22ef76d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98671ada3999ed4529978bae3b9c0a7e78", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b61edea4af8e66e4506a53f5bf8f5020", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ffcdc41dcfe7f026748c381619cdecb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dde73b8c70a954ad2dda2bb7dfab8ba2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPEventBridge.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809e41ae0fee909712e95222ff0a56d63", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f43713a31322ffb6002a360931b61d9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b641a75ad593a6bdb01b3ec5deb7cc7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoViewFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0b9d752a3d103d21ec4687979b0e477", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838892dd4a957142c57106ac9b64d86e0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa17823e56da5858ef44ad82bddf8c12", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoEventListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b6a582d9cd4c135fa4dadd0c5a6a95a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838c98b0ee7edb2e94fd19e487a399049", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee6903c4200c383a929551b283f06dc2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9bcece912aa69bfa97737d5cda302e5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3569063605481bb298e80394571f5ea", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPViewProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b3a4fe4bcc95a43731c23c7199223ac", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893f2402f56932f0cd6a02ee67ba11e8b", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f2673ec070896c6263dadaf9845bd2b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cf213262584fd08535a53cc807842fa", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d7cadd203c4920eaf1e78bf6a3f2e7a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoView.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987e0ad0c689e714c5689e9c4ca3134969", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856e8f3b9a956a6f975e14e6c9adbec10", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f33be7db59199ca7ad3ce3bb58c6bae", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985669f48830b30862d2ee967556186157", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982df16b4c3eb03c8052c3d5d1b4edad6c", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989912c24d941596563f29c7e531dbc1b4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e17cb213c05d0c95bd1c3bc469eafdb5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a8c4ab88f428ff697b1abb90740ced0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac72f8c7f5386b87e654be1acc6bc2bf", "name": "camera", "path": "camera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e28cdcfca0b37c49529ba423bbf9cfd0", "name": "manish", "path": "manish", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c58be410da841c4de0b6cd49e6813ed5", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844a85ff134bc30421a707f23786331c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd5da20d40cdd9413ea5085d0fda42b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866b346c9a57b554813b6f053ea649352", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98186f39d5a6afafddff9d0dd5247352ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbcf385bef12d67174a795139a687aca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc2d2bd2511c6d2af7a87ae852dc13a2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98822a94ad6b7f94e8c906b9a4df920cde", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9825d7ced225ae709d069326f4095bb1f9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98023b7087545f38c0bd511823d9e21a4e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9855fd087514842b73fd9ac5ca8f74eb64", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bdfea07aa169728c6e74ce12e8730265", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980d7e612f61f45aa6cae19649a227d3c4", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983501d26a15bf026805f4555a2713c886", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9838cf1fc9a09f984c22166652e221668f", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f028d6da50aa58ac2eea49f7a4fae7a3", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980397e1dc22873eaa7f50b6906a1c23a1", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9853d28fb37cad9bec09e386899e3ceb55", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984f58d66f50a4bd69f727fcf05b4f3857", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98348b317b6242ba6fb189117f4cb11fd9", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff8996ac88ce1a639d23f630f9fbb20c", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddad5e954c4839307dbb571b6aafda44", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983953e1a581f4e5498bf4d0ec845524be", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc7d34944f58a053e3e511b07e5e1943", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093e0d274405de132c9336fa40762070", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e983e98b0b779900965f69857c4e896cd30", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983e3485d5de14c283a1380298e18a4526", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98810f0c8e3d9cb80091b9909448cd6626", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb913630fcec08bc27ac2263f59bdea4", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857bc931e356d7b7843fda47492893ed5", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98d15413e70e37fef51b4d07973f5f0bba", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987d99e57ff5a2fbc3c868ddd022b1a2a0", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a9b3ef9ba473b1f1f86ce82470c7d25", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986e1523d54ce0c59eb376d15073c00a62", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa6d3f4a0bb22c1d653f7652a809829a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ac3979f5c1325345624fa4eb4525c7db", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f0ddcbd73ecfe1dd17d3452c7fbe2d4", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d38343279834dca6061bda437330c834", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Documents/manish/camera/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/manish/camera/ios/Pods", "targets": ["TARGET@v11_hash=223896b7b95f80b638b64393687376ad", "TARGET@v11_hash=8d44c30cc1543b37f30a80f40494a17d", "TARGET@v11_hash=7ff3a6d84aee1e49c4c00b1ae8e2ef73", "TARGET@v11_hash=32c0385b53070db7bc2e8d0319b7e7d0", "TARGET@v11_hash=31b6d6cb0c0b29662da7e4a725c16590", "TARGET@v11_hash=c1b8144666d31de23ae8cbf981b7650d", "TARGET@v11_hash=985d729b119a06640a3fd7c17ff9b775", "TARGET@v11_hash=244ece740c2389b92f3f0982d884037a", "TARGET@v11_hash=d9c36e75df1322904c464cbe89fbf7b5", "TARGET@v11_hash=6fd3d61fb5b849e75bd78efaf151f039"]}