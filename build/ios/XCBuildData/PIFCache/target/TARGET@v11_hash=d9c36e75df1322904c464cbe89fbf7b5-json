{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9853d28fb37cad9bec09e386899e3ceb55", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845864c20b50d7bf61c87c9928c17b1a1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f58d66f50a4bd69f727fcf05b4f3857", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c4b231252ac0d65a94af0d73ff89138", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f58d66f50a4bd69f727fcf05b4f3857", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab52d681ee1a5ef337e0a85f4cecdb56", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98671ada3999ed4529978bae3b9c0a7e78", "guid": "bfdfe7dc352907fc980b868725387e98701bcb0ff3b4b7cd5783d01534a7e2f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b61edea4af8e66e4506a53f5bf8f5020", "guid": "bfdfe7dc352907fc980b868725387e9876fe15da742f91d0ea06278d805343e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffcdc41dcfe7f026748c381619cdecb", "guid": "bfdfe7dc352907fc980b868725387e98fe6197ec5b014509968c9552fc4fb0f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde73b8c70a954ad2dda2bb7dfab8ba2", "guid": "bfdfe7dc352907fc980b868725387e9835be3b4146a5f028f1173f33c594a8c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e41ae0fee909712e95222ff0a56d63", "guid": "bfdfe7dc352907fc980b868725387e981a9aa3334a7388a641dddd35800c75db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f43713a31322ffb6002a360931b61d9", "guid": "bfdfe7dc352907fc980b868725387e98088f1996b0b00ee5cda6826b0671f5a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b641a75ad593a6bdb01b3ec5deb7cc7", "guid": "bfdfe7dc352907fc980b868725387e98e24d54319c82089b5f538353cda2979f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b9d752a3d103d21ec4687979b0e477", "guid": "bfdfe7dc352907fc980b868725387e98f2a200e381836fea1c7ccce569d9f83a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838892dd4a957142c57106ac9b64d86e0", "guid": "bfdfe7dc352907fc980b868725387e98c867975b26220456f0433cd1a43a9acb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa17823e56da5858ef44ad82bddf8c12", "guid": "bfdfe7dc352907fc980b868725387e98c7b90dd8bb52650399306e0bcafc0410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6a582d9cd4c135fa4dadd0c5a6a95a", "guid": "bfdfe7dc352907fc980b868725387e98b6d326f161cc3580e4931b2b94fcfdee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c98b0ee7edb2e94fd19e487a399049", "guid": "bfdfe7dc352907fc980b868725387e98a141b0132580102f6f8ecf616a601fcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee6903c4200c383a929551b283f06dc2", "guid": "bfdfe7dc352907fc980b868725387e9826d8d35e847a4c4856a2ba8a2922a186", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9bcece912aa69bfa97737d5cda302e5", "guid": "bfdfe7dc352907fc980b868725387e988201ad518b67dc86e9802b1c7b201a57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3569063605481bb298e80394571f5ea", "guid": "bfdfe7dc352907fc980b868725387e98389bfea1f74215f6cc3b07867e6d1fec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3a4fe4bcc95a43731c23c7199223ac", "guid": "bfdfe7dc352907fc980b868725387e985abac5efb2be1a3c3edd5dd5674ec9a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980397e1dc22873eaa7f50b6906a1c23a1", "guid": "bfdfe7dc352907fc980b868725387e98513854531e9d646a66bbb9568f9866a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98201de2e5de5f3b25ab3372cbaf3d06dd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b12a949984389b6f32321303a09a4b44", "guid": "bfdfe7dc352907fc980b868725387e98ff2c79da7560602958e1bebe0e968106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ee98f93af2e2e9a0b2eb8847b35f7d", "guid": "bfdfe7dc352907fc980b868725387e9821c19d220f17af6268b06b9e4bc74232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986233d1b9beac69d5c8ac69c28908dd52", "guid": "bfdfe7dc352907fc980b868725387e98df42d32b403deb61f5af3b55c43a3285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a153e3f3a6eb07904f1225b703d12a0", "guid": "bfdfe7dc352907fc980b868725387e98f9696f6f1874d4bc8c83014612018e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103d007aff46955542d71119abd612d0", "guid": "bfdfe7dc352907fc980b868725387e98a015c83e5d6c2aadea1ba31db2c1cc29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7cadd203c4920eaf1e78bf6a3f2e7a", "guid": "bfdfe7dc352907fc980b868725387e980fdb06675be0abfaa5e9f910b7a08cb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babdd38ef45b9a0d544056cb82c28f85", "guid": "bfdfe7dc352907fc980b868725387e98c585b4157c2092ddc0a36d83cadcbd39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ccc9bb1f577f6394f4830057efcd58", "guid": "bfdfe7dc352907fc980b868725387e9882290f915a4c3c370abefcf494af7a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90794bb6cf3fdc78c2f07c6dbc2056a", "guid": "bfdfe7dc352907fc980b868725387e9852f0a9bb095395bebc1419d5fe0486ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083dbc94d2e794767d8906789a46370c", "guid": "bfdfe7dc352907fc980b868725387e987e656749503f283b2576cc655b9e44ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc0df94c7200a81188a7f40abd5cc93b", "guid": "bfdfe7dc352907fc980b868725387e98d74af000b20c71f0c745716f15c88b4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f7e6b6ad9df3859dfd83235b22ef76d", "guid": "bfdfe7dc352907fc980b868725387e982e6c65458086329656fb8a71603e084b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983501d26a15bf026805f4555a2713c886", "guid": "bfdfe7dc352907fc980b868725387e982bdf1a3d25ac1aca2eb0b8a9ed93baf6"}], "guid": "bfdfe7dc352907fc980b868725387e982ad3667d1f3573c96d2a2e5ae7fa4bcb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "guid": "bfdfe7dc352907fc980b868725387e98abc643bd218bd44c774ad67fece38daf"}], "guid": "bfdfe7dc352907fc980b868725387e981787b72bb159c812f5ae815acc704875", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9882d28e4c77380c1eac2a6c8d30e66314", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7265512b31d58a201289f7f8dd4455", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}