{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98604150b1c79337fe49a8d5628c190f9e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9825a4fb8242ef8a94bbdcd828c87eb13f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862c2aed8ae45fcc1e6caf036727b6c6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98414cd678aa6ff810b7bd6dd28f5f7e64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862c2aed8ae45fcc1e6caf036727b6c6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef3f94148fe7a046d728d110af328fa8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdd24166e882df96b3e735a24466b3c9", "guid": "bfdfe7dc352907fc980b868725387e986e228729c552fdaf4f3abd94a0f59224", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e8cca56dba2af75f862871e698fd796", "guid": "bfdfe7dc352907fc980b868725387e98cf036e804673f99e037e55d8a59f179b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726906f32e7f6680385744b0243054c5", "guid": "bfdfe7dc352907fc980b868725387e98add2c506dad34f81760c6219553bea80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a523f1fbf75197eac0e317440d71909", "guid": "bfdfe7dc352907fc980b868725387e98d356bb37994acca10e842176ab4490c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934c2beb3e2b7ed571ff0ca28546b0cf", "guid": "bfdfe7dc352907fc980b868725387e985c02dce7403034179e0dabe09734d96e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31fac731c5723f88a0739cc42b80b1c", "guid": "bfdfe7dc352907fc980b868725387e987064865d9b092d5d70b83cb23945fde3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98761e7ca130e92737e000f901b4c0ce43", "guid": "bfdfe7dc352907fc980b868725387e9816b671c1aa4a8b2ee19e45628ad04437", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ede442239f67abfe8d7a80eafd332d", "guid": "bfdfe7dc352907fc980b868725387e98c3bfe7b6fa6f2c19028676fcab3a3e25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5c1124b2a1710941abb282869a959f", "guid": "bfdfe7dc352907fc980b868725387e981a7a254ad9a8cf28715e8f12f32e1042", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f05b494c1c583a3825aadf81024a40", "guid": "bfdfe7dc352907fc980b868725387e982baf89577cc1a95ee1b1fdcda9ec965c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13364127859f4d2943188178cb06d87", "guid": "bfdfe7dc352907fc980b868725387e9880dfdedd20aaedc3aaf54e6d642b2b2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e258911e76bb412b013d0912757f47a", "guid": "bfdfe7dc352907fc980b868725387e98122544dd4862e07bbc96d0f8fb79d205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d10ad5d0c87c73e42b8d3756aabceaa7", "guid": "bfdfe7dc352907fc980b868725387e98c6f3dbfe24da77f0e509e83aadd492df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82d76ed8ba04d0b8fd6ad094469b573", "guid": "bfdfe7dc352907fc980b868725387e98b16d0146d7c6bdcae6805e62b6844ff7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982936c3c7b79f6a16d7da3154478b3949", "guid": "bfdfe7dc352907fc980b868725387e98c55f8999387a872a4a0c69543298c78a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc98880ad3da01037512fe1a526fb49", "guid": "bfdfe7dc352907fc980b868725387e98a5bdf18cc97fa80fedcd2463aea792f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d896e7773f5bbda85d223c82a302856d", "guid": "bfdfe7dc352907fc980b868725387e980cc362660d4d9fcb097ef80057d63047", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804645c79b195603ab2443a68fd9b93cf", "guid": "bfdfe7dc352907fc980b868725387e9881045152de6e6dcc5cb7d03eff3eaae4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a94cc74bf14aea3187c470e82ed2887e", "guid": "bfdfe7dc352907fc980b868725387e985e16f4aec969b1962522a17a61fac53c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850160a6560f2e9fac99874504292d5cf", "guid": "bfdfe7dc352907fc980b868725387e9879072d91c6ff4b339513af96ca7bdb00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f7f4e1076b4e578e299a34074b685a", "guid": "bfdfe7dc352907fc980b868725387e98e5b1d4df8c837ff0cc3c51566437c029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b49a5596609c8832aed4d8a7a633aad1", "guid": "bfdfe7dc352907fc980b868725387e98fadf6262afd774dc82e3d035c8cb4917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed9f03025c3f8f294ba0b2adb00cb7c", "guid": "bfdfe7dc352907fc980b868725387e98d68d0f35d5236cc2ea866fdf70cdd945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8dc45dd92095611e69b372ce3c52ce", "guid": "bfdfe7dc352907fc980b868725387e986cb166cffa408478fff228f1b334350b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983286e47c8acee7d1b60820265be2bd12", "guid": "bfdfe7dc352907fc980b868725387e98ab1d349e9a4625bff76d031c878e3d6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b73cbc8145e4a5c0e63db7b3c97f039", "guid": "bfdfe7dc352907fc980b868725387e985d1ac6de6d415598ddb6bd23031a9d38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aeb3371d9b4bd59db38837377ad0875", "guid": "bfdfe7dc352907fc980b868725387e9868c90fce41e91ab226f8207daf71aaf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986584422537928c71a9f0aebd37cdb9f5", "guid": "bfdfe7dc352907fc980b868725387e98c2918530c6d729e34b04e4641f7c5a0d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987426a7dafac1d2394f10c16127971def", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987aefd5fb314167bca743cf6ab61c05bd", "guid": "bfdfe7dc352907fc980b868725387e98e47d12451b324c60ec391a11285c43d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb5a189ab1a7621710d57d67403cd50", "guid": "bfdfe7dc352907fc980b868725387e981d4b62e7cda0d76579551741b4ebdd3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3399a5baa9d150220277269ff4f1e17", "guid": "bfdfe7dc352907fc980b868725387e980fe1a2a4d33951b8ed98234e2b2bea0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3c159ef47349c83946f47f7aa3ac8a", "guid": "bfdfe7dc352907fc980b868725387e98af4cabc0de388550d3e71883d03e3aaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464edf36a0bf1cf41c0baac5d0a4960b", "guid": "bfdfe7dc352907fc980b868725387e9859da38cf09cc3bc3a4124d4bd7828793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895c1578de735b6983e080389fb98ed98", "guid": "bfdfe7dc352907fc980b868725387e989ce2f42db30ca2549565178a3d9efa0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989afaa5efa17320c08ada625356d320d7", "guid": "bfdfe7dc352907fc980b868725387e9866b81dd61ea432ff6ad732b9238a009b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890755a3551145dd6aa15818e174ebb12", "guid": "bfdfe7dc352907fc980b868725387e9867c8ac81822b6a3cba283780c0604212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f17e54c3c127d8fbb00e60386d54320", "guid": "bfdfe7dc352907fc980b868725387e987fce2c3dd48f1a866fa142615e11144e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98393d2247e615e66e21f45ad4b6e92302", "guid": "bfdfe7dc352907fc980b868725387e98d7be78d131d449332efd9e3816d51e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820e779d295ec6d8fe4b3282a3de7f7ab", "guid": "bfdfe7dc352907fc980b868725387e9892393b1c23b94b949ef28fffc44145e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af381def737b7e1744ee79f718ce27e", "guid": "bfdfe7dc352907fc980b868725387e984a14ee517c7a87681cd0db47b491bf85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989884ffc7067a9551e7279bf008920c99", "guid": "bfdfe7dc352907fc980b868725387e989904b4b8bffb03e7c2287b67449c0dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881200ea14dfa24acb60d149f320fcd38", "guid": "bfdfe7dc352907fc980b868725387e98c6d20c9533f912d162993fa6133ada77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985768b440f78bbcb7bfa3c8baee8c75e9", "guid": "bfdfe7dc352907fc980b868725387e98f287f55ffaae487423756f74553b9dd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fce6a16a96d66839030c408756fcf37", "guid": "bfdfe7dc352907fc980b868725387e98d4238656733ca81dbd591a7d6f38f204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983363629f15c33c509cafefea82c66cb7", "guid": "bfdfe7dc352907fc980b868725387e98cdf01bea73ee28432527385cb3dfa5ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b758c7d5fa6902a5e1cf38272c72d5e", "guid": "bfdfe7dc352907fc980b868725387e98b3c3bc993e9f8cdca6c4df4c3e48efea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0ac3e9878e41630596e25be7195e93", "guid": "bfdfe7dc352907fc980b868725387e983a02ca25a806202865688273a7a5484f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f79542f6c86e3e629efeec59e6e8ad6", "guid": "bfdfe7dc352907fc980b868725387e981525a38a4aee73219ad3d388dd0bf402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8dbcb3080d5888c9d52a3479af9268", "guid": "bfdfe7dc352907fc980b868725387e9894744856380751c204450640a47a3d5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d219d5fc5072d31b049d17a1be50ac56", "guid": "bfdfe7dc352907fc980b868725387e98a26b5b9762317cfe2f53fb9326985296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d28b4dcf0c6a59945d2359d06ce0da8", "guid": "bfdfe7dc352907fc980b868725387e98fe34a10833b98342f39b663140be613b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297560b847dc893295d3d619b146cf81", "guid": "bfdfe7dc352907fc980b868725387e986fa474f68767c95812d63d4e43fbd1f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986af4a91fc9d4e0d6fb7183c68609fae0", "guid": "bfdfe7dc352907fc980b868725387e9841adf54abbcfad92ede32b2db73737c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880beac51fe1b56d35c4e53ec1a20ccf0", "guid": "bfdfe7dc352907fc980b868725387e9855adeb8e6bc554445cf99c74d05e93ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caafd1b414b4d701384bb57d2fc72ea8", "guid": "bfdfe7dc352907fc980b868725387e985f0a4dced51a78956c67b07cd8fd63f8"}], "guid": "bfdfe7dc352907fc980b868725387e9877363964029d8ad42a403062036a7c87", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "guid": "bfdfe7dc352907fc980b868725387e986833ee1301fc896ca7cc18dee79bf078"}], "guid": "bfdfe7dc352907fc980b868725387e981557d72eca0c38666dfd53933b835a06", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e2c4e89c618ae2feca2ecdbf2be263c7", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e982fd2d25df49aacaaca253cad31fd43c2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}