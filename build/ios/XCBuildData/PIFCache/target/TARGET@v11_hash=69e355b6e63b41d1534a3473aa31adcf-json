{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a930d5080e2acf463c66f374810ecf69", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845864c20b50d7bf61c87c9928c17b1a1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4921c4a26b0734d9e00edf090570bba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c4b231252ac0d65a94af0d73ff89138", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4921c4a26b0734d9e00edf090570bba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab52d681ee1a5ef337e0a85f4cecdb56", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f3f3b32e7f99ff2bb4b7157d0ebeac3a", "guid": "bfdfe7dc352907fc980b868725387e98701bcb0ff3b4b7cd5783d01534a7e2f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0343a9f29ce637389bb328e85c0d22d", "guid": "bfdfe7dc352907fc980b868725387e9876fe15da742f91d0ea06278d805343e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e8b65f05af06f7c5a709f49376bad6", "guid": "bfdfe7dc352907fc980b868725387e98fe6197ec5b014509968c9552fc4fb0f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae05776e64848920ac9627c65e64ef7c", "guid": "bfdfe7dc352907fc980b868725387e9835be3b4146a5f028f1173f33c594a8c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fecfea98023b699185a26874d3c43232", "guid": "bfdfe7dc352907fc980b868725387e981a9aa3334a7388a641dddd35800c75db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768f943f3f01e51511e85593f9e09b3d", "guid": "bfdfe7dc352907fc980b868725387e98088f1996b0b00ee5cda6826b0671f5a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a19c7f8be7ff988ef2a50565f3aebcc", "guid": "bfdfe7dc352907fc980b868725387e98e24d54319c82089b5f538353cda2979f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc897e7f1791a5354b9a36b6cbaf81a7", "guid": "bfdfe7dc352907fc980b868725387e98f2a200e381836fea1c7ccce569d9f83a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c9d594e50d2ca8e82c8562bba494e0", "guid": "bfdfe7dc352907fc980b868725387e98c867975b26220456f0433cd1a43a9acb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988782eccb42d92e14423649b041848b67", "guid": "bfdfe7dc352907fc980b868725387e98c7b90dd8bb52650399306e0bcafc0410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60fbf2cb642d09b280f7a163478be6d", "guid": "bfdfe7dc352907fc980b868725387e98b6d326f161cc3580e4931b2b94fcfdee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66bcbfe94b76057d8ff0853e282781a", "guid": "bfdfe7dc352907fc980b868725387e98a141b0132580102f6f8ecf616a601fcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5c32faa53d452c9e8fb47c1b620e83", "guid": "bfdfe7dc352907fc980b868725387e9826d8d35e847a4c4856a2ba8a2922a186", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fcd0488cddc66f4e77258fc266c1dc8", "guid": "bfdfe7dc352907fc980b868725387e988201ad518b67dc86e9802b1c7b201a57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960e08253413cfafce0c207b9d76fd47", "guid": "bfdfe7dc352907fc980b868725387e98389bfea1f74215f6cc3b07867e6d1fec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1edeb15e9ae54fd2203f07ae0147921", "guid": "bfdfe7dc352907fc980b868725387e985abac5efb2be1a3c3edd5dd5674ec9a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c12231b95abebab165d223a67fadf0", "guid": "bfdfe7dc352907fc980b868725387e98513854531e9d646a66bbb9568f9866a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98201de2e5de5f3b25ab3372cbaf3d06dd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988147d9d702275f3bee5b55366c34ec7f", "guid": "bfdfe7dc352907fc980b868725387e98ff2c79da7560602958e1bebe0e968106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b16a34477c017530097cb979b0c1f5", "guid": "bfdfe7dc352907fc980b868725387e9821c19d220f17af6268b06b9e4bc74232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a9b3ad7621fb5adccdabc51e3b2e61a", "guid": "bfdfe7dc352907fc980b868725387e98df42d32b403deb61f5af3b55c43a3285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e5060629cda9defc7eb25dc3ca4c2bd", "guid": "bfdfe7dc352907fc980b868725387e98f9696f6f1874d4bc8c83014612018e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e3ffc598f18b9b1dbf305f91634f4f", "guid": "bfdfe7dc352907fc980b868725387e98a015c83e5d6c2aadea1ba31db2c1cc29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8c87a249a58728d56759c73ffa08e0", "guid": "bfdfe7dc352907fc980b868725387e980fdb06675be0abfaa5e9f910b7a08cb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985312ed809ad027968c8b85c32715a8b7", "guid": "bfdfe7dc352907fc980b868725387e98c585b4157c2092ddc0a36d83cadcbd39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981488d135a53fe52a487827506a4a5ce2", "guid": "bfdfe7dc352907fc980b868725387e9882290f915a4c3c370abefcf494af7a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2861363028fab0e46084716910492f", "guid": "bfdfe7dc352907fc980b868725387e9852f0a9bb095395bebc1419d5fe0486ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836eb2360de7ef87b9393ba9987e20b55", "guid": "bfdfe7dc352907fc980b868725387e987e656749503f283b2576cc655b9e44ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e00af3efabb9da806a4e7cacd3604d", "guid": "bfdfe7dc352907fc980b868725387e98d74af000b20c71f0c745716f15c88b4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bea572271e5032d5223657b97591eb9", "guid": "bfdfe7dc352907fc980b868725387e982e6c65458086329656fb8a71603e084b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6ff1d814756e5732873230363243f9", "guid": "bfdfe7dc352907fc980b868725387e982bdf1a3d25ac1aca2eb0b8a9ed93baf6"}], "guid": "bfdfe7dc352907fc980b868725387e982ad3667d1f3573c96d2a2e5ae7fa4bcb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "guid": "bfdfe7dc352907fc980b868725387e98abc643bd218bd44c774ad67fece38daf"}], "guid": "bfdfe7dc352907fc980b868725387e981787b72bb159c812f5ae815acc704875", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9882d28e4c77380c1eac2a6c8d30e66314", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7265512b31d58a201289f7f8dd4455", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}