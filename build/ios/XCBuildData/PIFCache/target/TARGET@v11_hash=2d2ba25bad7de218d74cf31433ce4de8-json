{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2b1c8badb7d0fec0e7d6f01bcd9062b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9825a4fb8242ef8a94bbdcd828c87eb13f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc49f783b26359a6282578d12e95afee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98414cd678aa6ff810b7bd6dd28f5f7e64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc49f783b26359a6282578d12e95afee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef3f94148fe7a046d728d110af328fa8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98430f0bb5b17933104b0c99ed255b6158", "guid": "bfdfe7dc352907fc980b868725387e986e228729c552fdaf4f3abd94a0f59224", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f43ed9aaa0c87abad9d09aaaadba9d", "guid": "bfdfe7dc352907fc980b868725387e98cf036e804673f99e037e55d8a59f179b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d6342f91d6356c14ff77df02bb3c606", "guid": "bfdfe7dc352907fc980b868725387e98add2c506dad34f81760c6219553bea80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66da60507ed994ee3b648cae4234b99", "guid": "bfdfe7dc352907fc980b868725387e98d356bb37994acca10e842176ab4490c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f8cc4013338802002bfd96a3ad2364b", "guid": "bfdfe7dc352907fc980b868725387e985c02dce7403034179e0dabe09734d96e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98144aa26a12c48b712bfed3b465e9693e", "guid": "bfdfe7dc352907fc980b868725387e987064865d9b092d5d70b83cb23945fde3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850b014036d72b2b61832b1a1212b8683", "guid": "bfdfe7dc352907fc980b868725387e9816b671c1aa4a8b2ee19e45628ad04437", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e93fd5de19f645a85aceea56895bce", "guid": "bfdfe7dc352907fc980b868725387e98c3bfe7b6fa6f2c19028676fcab3a3e25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4aca9a22553e1e213a02ed1959a338b", "guid": "bfdfe7dc352907fc980b868725387e981a7a254ad9a8cf28715e8f12f32e1042", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a62c7a59164975398c37b7b5ef8c85", "guid": "bfdfe7dc352907fc980b868725387e982baf89577cc1a95ee1b1fdcda9ec965c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98233b5278d5e9d6506078581c35575d47", "guid": "bfdfe7dc352907fc980b868725387e9880dfdedd20aaedc3aaf54e6d642b2b2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a91fe4a2297389032c91c8fe3ee2f2d7", "guid": "bfdfe7dc352907fc980b868725387e98122544dd4862e07bbc96d0f8fb79d205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985baa657abf62c22f5495c4f082721cfa", "guid": "bfdfe7dc352907fc980b868725387e98c6f3dbfe24da77f0e509e83aadd492df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985483d3c4e6c7ee02d8b576c1232e9eeb", "guid": "bfdfe7dc352907fc980b868725387e98b16d0146d7c6bdcae6805e62b6844ff7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785df5d9b35b4ae921e826c96d89db0a", "guid": "bfdfe7dc352907fc980b868725387e98c55f8999387a872a4a0c69543298c78a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83c0394f7ea5cfae11c7e584ba134e2", "guid": "bfdfe7dc352907fc980b868725387e98a5bdf18cc97fa80fedcd2463aea792f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c92e10c58f65913d3491524aee5cda", "guid": "bfdfe7dc352907fc980b868725387e980cc362660d4d9fcb097ef80057d63047", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae5785dcee934accae9a042d9a64603d", "guid": "bfdfe7dc352907fc980b868725387e9881045152de6e6dcc5cb7d03eff3eaae4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a299f34a2417e10e4b50b91eecce492a", "guid": "bfdfe7dc352907fc980b868725387e985e16f4aec969b1962522a17a61fac53c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342930e3cb7ebf768a7a14ebe922aade", "guid": "bfdfe7dc352907fc980b868725387e9879072d91c6ff4b339513af96ca7bdb00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876149c2e44aba1b6767859d934d3c65c", "guid": "bfdfe7dc352907fc980b868725387e98e5b1d4df8c837ff0cc3c51566437c029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac3c905de1f760a4e3022a9cbd2ce90", "guid": "bfdfe7dc352907fc980b868725387e98fadf6262afd774dc82e3d035c8cb4917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863615b583a5e33af3f5abd7728087c50", "guid": "bfdfe7dc352907fc980b868725387e98d68d0f35d5236cc2ea866fdf70cdd945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df7f8422d46a095db2307bb6a6810c94", "guid": "bfdfe7dc352907fc980b868725387e986cb166cffa408478fff228f1b334350b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eadc799df3a1c199d8fde76fe39314f", "guid": "bfdfe7dc352907fc980b868725387e98ab1d349e9a4625bff76d031c878e3d6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aefc2099eb00a7e827305edcf727173a", "guid": "bfdfe7dc352907fc980b868725387e985d1ac6de6d415598ddb6bd23031a9d38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61ec6df667ba1982140650ba414497c", "guid": "bfdfe7dc352907fc980b868725387e9868c90fce41e91ab226f8207daf71aaf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be3cddb74be2e20a61c6fe1815ed5730", "guid": "bfdfe7dc352907fc980b868725387e98c2918530c6d729e34b04e4641f7c5a0d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987426a7dafac1d2394f10c16127971def", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988488935ba22d2afad4f7331a466284da", "guid": "bfdfe7dc352907fc980b868725387e98e47d12451b324c60ec391a11285c43d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc125dd58c73546c6e6d38b6f7d8080", "guid": "bfdfe7dc352907fc980b868725387e981d4b62e7cda0d76579551741b4ebdd3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e0acfd55913869db28bb7db1f642bd0", "guid": "bfdfe7dc352907fc980b868725387e980fe1a2a4d33951b8ed98234e2b2bea0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863b706bf48878bfc0fca25aef66c4cad", "guid": "bfdfe7dc352907fc980b868725387e98af4cabc0de388550d3e71883d03e3aaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df99e0a595640f1a4901452a83fe228", "guid": "bfdfe7dc352907fc980b868725387e9859da38cf09cc3bc3a4124d4bd7828793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d2dbbd251156dd26e90cb5a1e1d5ec", "guid": "bfdfe7dc352907fc980b868725387e989ce2f42db30ca2549565178a3d9efa0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc7efc499d17d2a4fac1c6bc94b79cb", "guid": "bfdfe7dc352907fc980b868725387e9866b81dd61ea432ff6ad732b9238a009b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d9412acead563714bf072014f317c41", "guid": "bfdfe7dc352907fc980b868725387e9867c8ac81822b6a3cba283780c0604212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2cbf0c4261f3ec93656e76af4d6a720", "guid": "bfdfe7dc352907fc980b868725387e987fce2c3dd48f1a866fa142615e11144e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98092d596b2dcb4540b60f595fd8deaf5e", "guid": "bfdfe7dc352907fc980b868725387e98d7be78d131d449332efd9e3816d51e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826930a24e06216e7781ea11a0be3ec53", "guid": "bfdfe7dc352907fc980b868725387e9892393b1c23b94b949ef28fffc44145e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e229e31c49d4d381d79db566048ec11", "guid": "bfdfe7dc352907fc980b868725387e984a14ee517c7a87681cd0db47b491bf85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041d3468c82bed1aa71db1e6b5ac8402", "guid": "bfdfe7dc352907fc980b868725387e989904b4b8bffb03e7c2287b67449c0dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671252c23fe32bf67d846decfeffef39", "guid": "bfdfe7dc352907fc980b868725387e98c6d20c9533f912d162993fa6133ada77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ebe851d2b30c60206043cda77dacad", "guid": "bfdfe7dc352907fc980b868725387e98f287f55ffaae487423756f74553b9dd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983539312bcbd01828adf4362954b153f5", "guid": "bfdfe7dc352907fc980b868725387e98d4238656733ca81dbd591a7d6f38f204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d06841edbcbb185320b9f77c1b636f", "guid": "bfdfe7dc352907fc980b868725387e98cdf01bea73ee28432527385cb3dfa5ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850fb769c35c550008640aa44b2a39951", "guid": "bfdfe7dc352907fc980b868725387e98b3c3bc993e9f8cdca6c4df4c3e48efea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a615a52cbb653c8508abcbf0a257a67", "guid": "bfdfe7dc352907fc980b868725387e983a02ca25a806202865688273a7a5484f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3527e86423bf8bab58993cdad55f60d", "guid": "bfdfe7dc352907fc980b868725387e981525a38a4aee73219ad3d388dd0bf402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868bb19416ce3f6589c4757f9ddc6c928", "guid": "bfdfe7dc352907fc980b868725387e9894744856380751c204450640a47a3d5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acb1436845b4bd4fda6897283e34b17c", "guid": "bfdfe7dc352907fc980b868725387e98a26b5b9762317cfe2f53fb9326985296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882437ffb86024eb47abe97939d0808a1", "guid": "bfdfe7dc352907fc980b868725387e98fe34a10833b98342f39b663140be613b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa23f52443865de02fc17f4a716b8ced", "guid": "bfdfe7dc352907fc980b868725387e986fa474f68767c95812d63d4e43fbd1f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919ed086a3eeb446a9b1bc5ad32c7ad0", "guid": "bfdfe7dc352907fc980b868725387e9841adf54abbcfad92ede32b2db73737c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aad32a0c95d7da6b7cae0df1299231a", "guid": "bfdfe7dc352907fc980b868725387e9855adeb8e6bc554445cf99c74d05e93ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98923a0f20b40adf73659f158fa8b2e1e4", "guid": "bfdfe7dc352907fc980b868725387e985f0a4dced51a78956c67b07cd8fd63f8"}], "guid": "bfdfe7dc352907fc980b868725387e9877363964029d8ad42a403062036a7c87", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98105e24997628e1bf12ed88a27cb68436", "guid": "bfdfe7dc352907fc980b868725387e986833ee1301fc896ca7cc18dee79bf078"}], "guid": "bfdfe7dc352907fc980b868725387e981557d72eca0c38666dfd53933b835a06", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e2c4e89c618ae2feca2ecdbf2be263c7", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e982fd2d25df49aacaaca253cad31fd43c2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}