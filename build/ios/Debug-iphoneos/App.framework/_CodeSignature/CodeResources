<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		21tVqbIV90TbglF4ZJhNBz8uj4w=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		vyGp6PvFo4RvsFtPoIWeCReyIC8=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		+D1xbIOooc3ypce1+jh+mmLy1J0=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		qFgQ9/9D9EeIT2bzkvq70giu5tw=
		</data>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>flutter_assets/isolate_snapshot_data</key>
		<data>
		J3OKveu3/udFiB8O6yi68rDVWOY=
		</data>
		<key>flutter_assets/kernel_blob.bin</key>
		<data>
		HNZSJ6u+NzosPn0l+3TY0V4WviY=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VoVnsu58hN7wrwazX0nyAopiB0Q=
		</data>
		<key>flutter_assets/vm_snapshot_data</key>
		<data>
		6f33nJbLQOxwLNQ7Pq7TZR9ENuo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			9WbMb8zGVzZcAZeszzp9b4D4Ugn/Zm/3dPTcvFJKqEI=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RBNvo1WzZ4oRRq0W9+hknpT7T8If536DEMBg9hyq/4o=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLHrKz0uGtYLjIsPkQCxzL9JL3+pf1vrtR6pfnOSbn0=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			ykJBeLwdNuYeIrXzVmhZ/1RVfNQWooOB0vZwPveKWmc=
			</data>
		</dict>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			ykVvWbyyNFkOsE/pzKdeOTPYsF+loL8qrTBq1TF84es=
			</data>
		</dict>
		<key>flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			XuV7pPwFweT0k9YvC5PU1yeReFAKjxT5dIO9JLQMHzQ=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			4PC1vOWKHlNOnW4dGwFfD4TPwBCllniPCsOy7+1X5co=
			</data>
		</dict>
		<key>flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			EkAQI7At/OAUaTSQUy3t6/4PpHWrvbPs+YdJHmzJ978=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
