<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		6y3kpwMQeb/v/1VcPI44vJXx/4g=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		ioOhcJw1Ybg+C+I4WJqxboH2aGQ=
		</data>
		<key>Assets.car</key>
		<data>
		QtKcPwyRJqNAFmLDZ/ltZNXVLzw=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		+T1tJhzxgfGx9PBUpzV9JUIJzK8=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		GkgZrqK4Ztc3+hlR8SxQS0SfuiY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		21tVqbIV90TbglF4ZJhNBz8uj4w=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		vyGp6PvFo4RvsFtPoIWeCReyIC8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		+D1xbIOooc3ypce1+jh+mmLy1J0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		qFgQ9/9D9EeIT2bzkvq70giu5tw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		J3OKveu3/udFiB8O6yi68rDVWOY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		5kk72FyqfJBCI+ihOhYLKXGJwnA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VoVnsu58hN7wrwazX0nyAopiB0Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		6f33nJbLQOxwLNQ7Pq7TZR9ENuo=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		5OuBhcVMzku1Lsc2PedsqHo2YQw=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		+8GUTjT65EWKWGCvzzqxpU7xZPE=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		/h16tUmYkItwIKOuPLUhjeZQLOc=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/camera_avfoundation.framework/Info.plist</key>
		<data>
		77x4ua74YX8Dlp4bfUL+Je0ybPU=
		</data>
		<key>Frameworks/camera_avfoundation.framework/_CodeSignature/CodeResources</key>
		<data>
		lVBIXalwMJi/r71eXvsbabdo1p8=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation</key>
		<data>
		HDLIpjVkE96WVIXriIYXl5bbisM=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		HrIlCd6wOWvEOkXDQiJvCxjve34=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/image_gallery_saver.framework/Info.plist</key>
		<data>
		flWx0mbiYQ61mLtaqwKCvIBObu4=
		</data>
		<key>Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources</key>
		<data>
		6kDhwYIAudRktXR/SQHIYm3vvwU=
		</data>
		<key>Frameworks/image_gallery_saver.framework/image_gallery_saver</key>
		<data>
		DuJy6G+BGjbhrcPFvw4VRDyp/eM=
		</data>
		<key>Frameworks/integration_test.framework/Info.plist</key>
		<data>
		rNDPXaqdGT3fmVpmJr0QQHBCjG0=
		</data>
		<key>Frameworks/integration_test.framework/_CodeSignature/CodeResources</key>
		<data>
		3jgIBpTfFIBBK+zf+Hnw+gpFrvE=
		</data>
		<key>Frameworks/integration_test.framework/integration_test</key>
		<data>
		fXPxNuPqfV1p+HxvYKr5Q8IxiU8=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		iMOEBSTlcgEu4p5nJhHx2HeqbKw=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		WFWQv9xHKH0sNwN/eK4s16fpNdQ=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		k5p88820Cp3AN+f3wfYRdYum++4=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		XTdZt5xGm7e7toyE/Zn6i3UopAA=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/Info.plist</key>
		<data>
		CELWqBvxPqtFbCRiOpLDrZ0nTo8=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources</key>
		<data>
		U999fEltAmB7V07+pnJUIhKE2J8=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation</key>
		<data>
		NcpLyaoH45YspmCl0Lme5YIBSh0=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		u91loM8m5VM251MFEJJoDgffEqw=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Info.plist</key>
		<data>
		9xyVuMW5vTu0Zx7P7pjf2PC11Qw=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		z2xPeBvzahFZZ3bVG8kLy4mIzZg=
		</data>
		<key>__preview.dylib</key>
		<data>
		EyPTVcaLJHmOopIqyleNFc44+rc=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		b3IsaiaaSAfa+gGq1o57Ksa4/nU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			zVXIFJRV7uliGISNysOyn4vQ/lnQdgY5xN4w7gXJGQk=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qyat40OEnOXl37cRIf0D1aIhqCW6l9+g7/vnGu1GkMA=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			0YIwewIM02vcPRmgUCsbbWovkBvg/GCcoOttdUG6UB0=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			s0Tu7npqrqC3g2S5HD0Xs9lKv8ePrKu1RTdsuBxXjak=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			sDVGICgM1IiTpRtzgMOK8kDbrh1j1OQHDNkMWozAnsY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			9WbMb8zGVzZcAZeszzp9b4D4Ugn/Zm/3dPTcvFJKqEI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RBNvo1WzZ4oRRq0W9+hknpT7T8If536DEMBg9hyq/4o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLHrKz0uGtYLjIsPkQCxzL9JL3+pf1vrtR6pfnOSbn0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			ykJBeLwdNuYeIrXzVmhZ/1RVfNQWooOB0vZwPveKWmc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			ykVvWbyyNFkOsE/pzKdeOTPYsF+loL8qrTBq1TF84es=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			cYqJsvqIAssWICo1Iwq2ggNWPuxB4M4I+KhT322tiJs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			4PC1vOWKHlNOnW4dGwFfD4TPwBCllniPCsOy7+1X5co=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			EkAQI7At/OAUaTSQUy3t6/4PpHWrvbPs+YdJHmzJ978=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			QsO1QMorD2KeNoTsI2585hXNeoRunGLq86bunjKN6l8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qvdqOJyjv94imOYTWNDnDQE5YS+PsQoEVDZ/eyOLBsM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Qs/vkLJuQ29Pp1KIPbf42CmNEU97zcwikXe/XR2CXRY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3dSDDIMkGvZhqYp1IE5LQ6Qk1taf4r+lqG/vA6Tf10Y=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			MwFTeNL4dixJtg/wZM0SwhGjLw33ZcnvXSw5ChFZUTg=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation</key>
		<dict>
			<key>hash2</key>
			<data>
			ma13RefCB1FsiHESbRzCPojwT+BqVCTKW0jiZTwgv2g=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			dSBERNazIv6DOtioIyemXFYMzi4wvpOy1aL+Tf/sNgc=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AmV0g8pLuzR3rhr3VVZeKaiOx2V0E+3AbywGKJdFdRg=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			QPMU4oZqKrKqCZACD794DMJ6BZ6/VbsaldBre8jNwpg=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/image_gallery_saver</key>
		<dict>
			<key>hash2</key>
			<data>
			tgJRg4pTJNK5pzd/AtGU2PDdEY1/rN+RJkauX4+u5nA=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Dg+vnz4pJCzJIdEzJ0iPIoDSjMVIBeNEk/YXTFsyHkQ=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			SuOWMnEqoWFTZtqVkXGeCtLJWE0BEPip3dVlaBMCdpI=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/integration_test</key>
		<dict>
			<key>hash2</key>
			<data>
			R2rbByMlfK+L8UcdciGCgILB1jGuYHHRy7N980guWGA=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CycVo4PP22NnTT1XNWM8e9LxIn96SIOM3wK+6wl/xAc=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			cDHNXIPmdElpmcf8Aci2/1ABtWqu/HkTZOThWjxuo5Q=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			wRGz9HVToEOlJnXc29ADjCoYDwNT1BAon7aeKIM2t4s=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HTE6Le6DqCRQczRI07h2JZ1o0Ws2eVl5Dq2iDZpZTTI=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yKHD4xGbF15rrQW17JKnkl3bWl6Cgdd4BEPRhcjOqHg=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			VeyrAzvbc3wu2MgbAJQSCFakhf8wwQZEU0w74BHuEYU=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation</key>
		<dict>
			<key>hash2</key>
			<data>
			5CTVzieff90J2zkEIkk5y+TL+PtwSR1lipFQ66hbeLM=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			L5IMUpSXXsShPEmdmf6CPKzhSh7XzKJTqT8rbEiSLP4=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			1RfX8+F58YcQDpjEcRfjFe72mxbCBzYnNXvnrVdCNKs=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			pUGV5uMar3ZBUgBRRT0TVCC/gAHGDo7mDRA5BdURAuI=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			2L2yBXpKKO4kGucUHeQiNR3BizONyKLvE6/YG6FmNKg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
