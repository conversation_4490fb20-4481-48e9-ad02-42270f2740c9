---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/camera/build/ios/Release-iphoneos/path_provider_foundation/path_provider_foundation.framework/path_provider_foundation'
relocations:
  - { offset: 0x5E35C, size: 0x8, addend: 0x0, symName: _path_provider_foundationVersionString, symObjAddr: 0x0, symBinAddr: 0x81C0, symSize: 0x0 }
  - { offset: 0x5E391, size: 0x8, addend: 0x0, symName: _path_provider_foundationVersionNumber, symObjAddr: 0x38, symBinAddr: 0x81F8, symSize: 0x0 }
  - { offset: 0x5E3F6, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19MessagesPigeonCodecC6sharedACvpZ', symObjAddr: 0xB0B0, symBinAddr: 0xDFF0, symSize: 0x0 }
  - { offset: 0x5E449, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCMa', symObjAddr: 0x3C, symBinAddr: 0x403C, symSize: 0x20 }
  - { offset: 0x5E45D, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation10nilOrValue33_7EDA05D020E1F315CC375C7C1D72F461LLyxSgypSglFSi_Tg5', symObjAddr: 0x6C, symBinAddr: 0x406C, symSize: 0xEC }
  - { offset: 0x5E6BB, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x56C, symBinAddr: 0x456C, symSize: 0x20 }
  - { offset: 0x5E6CF, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x754, symBinAddr: 0x4754, symSize: 0x20 }
  - { offset: 0x5E6E3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31MessagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x89C, symBinAddr: 0x489C, symSize: 0x20 }
  - { offset: 0x5E6F7, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19MessagesPigeonCodecC6shared_WZ', symObjAddr: 0x8BC, symBinAddr: 0x48BC, symSize: 0x70 }
  - { offset: 0x5E73C, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19MessagesPigeonCodecCfETo', symObjAddr: 0x9B4, symBinAddr: 0x49B4, symSize: 0x4 }
  - { offset: 0x5E767, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19MessagesPigeonCodecCMa', symObjAddr: 0x9B8, symBinAddr: 0x49B8, symSize: 0x20 }
  - { offset: 0x5E8E5, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegn_Iegng_yXlSgABIeyBy_IeyByy_TR', symObjAddr: 0xB34, symBinAddr: 0x4B34, symSize: 0xC8 }
  - { offset: 0x5E8FD, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0xBFC, symBinAddr: 0x4BFC, symSize: 0xD8 }
  - { offset: 0x5E915, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupCMa', symObjAddr: 0xE54, symBinAddr: 0x4E54, symSize: 0x20 }
  - { offset: 0x5E929, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0xE74, symBinAddr: 0x4E74, symSize: 0x40 }
  - { offset: 0x5E93D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0xEB4, symBinAddr: 0x4EB4, symSize: 0x40 }
  - { offset: 0x5E951, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0xEF4, symBinAddr: 0x4EF4, symSize: 0x3C }
  - { offset: 0x5E965, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0xF30, symBinAddr: 0x4F30, symSize: 0x24 }
  - { offset: 0x5E979, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xF54, symBinAddr: 0x4F54, symSize: 0x44 }
  - { offset: 0x5E98D, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0xFA8, symBinAddr: 0x4FA8, symSize: 0x10 }
  - { offset: 0x5E9A1, size: 0x8, addend: 0x0, symName: '_$sSo6NSNullCMa', symObjAddr: 0xFB8, symBinAddr: 0x4FB8, symSize: 0x44 }
  - { offset: 0x5E9F2, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation15PathProviderApi_pWOb', symObjAddr: 0x14D0, symBinAddr: 0x54D0, symSize: 0x18 }
  - { offset: 0x5EA06, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation15PathProviderApi_pWOc', symObjAddr: 0x14E8, symBinAddr: 0x54E8, symSize: 0x44 }
  - { offset: 0x5EA1A, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_TA', symObjAddr: 0x1570, symBinAddr: 0x5570, symSize: 0x8 }
  - { offset: 0x5EA2E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1578, symBinAddr: 0x5578, symSize: 0x10 }
  - { offset: 0x5EA42, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1588, symBinAddr: 0x5588, symSize: 0x8 }
  - { offset: 0x5EA56, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_TA', symObjAddr: 0x1590, symBinAddr: 0x5590, symSize: 0x8 }
  - { offset: 0x5EA6A, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x15BC, symBinAddr: 0x55BC, symSize: 0x8 }
  - { offset: 0x5EA7E, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x15C4, symBinAddr: 0x55C4, symSize: 0xC }
  - { offset: 0x5EA92, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x15D0, symBinAddr: 0x55D0, symSize: 0x4 }
  - { offset: 0x5EAA6, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwet', symObjAddr: 0x15D4, symBinAddr: 0x55D4, symSize: 0x90 }
  - { offset: 0x5EABA, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwst', symObjAddr: 0x1664, symBinAddr: 0x5664, symSize: 0xB0 }
  - { offset: 0x5EACE, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwug', symObjAddr: 0x1714, symBinAddr: 0x5714, symSize: 0x8 }
  - { offset: 0x5EAE2, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwup', symObjAddr: 0x171C, symBinAddr: 0x571C, symSize: 0x4 }
  - { offset: 0x5EAF6, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwui', symObjAddr: 0x1720, symBinAddr: 0x5720, symSize: 0x8 }
  - { offset: 0x5EB0A, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOMa', symObjAddr: 0x1728, symBinAddr: 0x5728, symSize: 0x10 }
  - { offset: 0x5EB1E, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASQWb', symObjAddr: 0x1738, symBinAddr: 0x5738, symSize: 0x4 }
  - { offset: 0x5EB32, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOACSQAAWl', symObjAddr: 0x173C, symBinAddr: 0x573C, symSize: 0x40 }
  - { offset: 0x5EB81, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCfD', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x3C }
  - { offset: 0x5EBC7, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x5C, symBinAddr: 0x405C, symSize: 0x4 }
  - { offset: 0x5EBE3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP5_codeSivgTW', symObjAddr: 0x60, symBinAddr: 0x4060, symSize: 0x4 }
  - { offset: 0x5EBFF, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x64, symBinAddr: 0x4064, symSize: 0x4 }
  - { offset: 0x5EC1B, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x4 }
  - { offset: 0x5EC4C, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x158, symBinAddr: 0x4158, symSize: 0x14 }
  - { offset: 0x5ECE9, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH9hashValueSivgTW', symObjAddr: 0x16C, symBinAddr: 0x416C, symSize: 0x44 }
  - { offset: 0x5ED83, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1B0, symBinAddr: 0x41B0, symSize: 0x28 }
  - { offset: 0x5EDC1, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1D8, symBinAddr: 0x41D8, symSize: 0x40 }
  - { offset: 0x5EF7E, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x218, symBinAddr: 0x4218, symSize: 0x28 }
  - { offset: 0x5EFA7, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x240, symBinAddr: 0x4240, symSize: 0xC }
  - { offset: 0x5EFBB, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC9readValue6ofTypeypSgs5UInt8V_tF', symObjAddr: 0x24C, symBinAddr: 0x424C, symSize: 0x190 }
  - { offset: 0x5F03B, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC9readValue6ofTypeypSgs5UInt8V_tFTo', symObjAddr: 0x3DC, symBinAddr: 0x43DC, symSize: 0xD0 }
  - { offset: 0x5F0BB, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC4dataAD10Foundation4DataV_tcfcTo', symObjAddr: 0x4AC, symBinAddr: 0x44AC, symSize: 0xA0 }
  - { offset: 0x5F104, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC10writeValueyyypF', symObjAddr: 0x58C, symBinAddr: 0x458C, symSize: 0xF8 }
  - { offset: 0x5F15A, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC10writeValueyyypFTo', symObjAddr: 0x684, symBinAddr: 0x4684, symSize: 0x68 }
  - { offset: 0x5F196, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25MessagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC4dataADSo13NSMutableDataC_tcfcTo', symObjAddr: 0x6EC, symBinAddr: 0x46EC, symSize: 0x48 }
  - { offset: 0x5F2E3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31MessagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC6reader4withSo015FlutterStandardG0C10Foundation4DataV_tFTo', symObjAddr: 0x774, symBinAddr: 0x4774, symSize: 0xD0 }
  - { offset: 0x5F352, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31MessagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC6writer4withSo015FlutterStandardH0CSo13NSMutableDataC_tFTo', symObjAddr: 0x844, symBinAddr: 0x4844, symSize: 0x38 }
  - { offset: 0x5F3F9, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_', symObjAddr: 0x9D8, symBinAddr: 0x49D8, symSize: 0x15C }
  - { offset: 0x5F586, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_', symObjAddr: 0xCD4, symBinAddr: 0x4CD4, symSize: 0x170 }
  - { offset: 0x5F701, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupCfD', symObjAddr: 0xE44, symBinAddr: 0x4E44, symSize: 0x10 }
  - { offset: 0x5F726, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0xF98, symBinAddr: 0x4F98, symSize: 0x10 }
  - { offset: 0x5F792, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZTf4nnnd_n', symObjAddr: 0xFFC, symBinAddr: 0x4FFC, symSize: 0x454 }
  - { offset: 0x5FB46, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0x5788, symSize: 0xB4 }
  - { offset: 0x5FC60, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCMa', symObjAddr: 0xD4, symBinAddr: 0x585C, symSize: 0x20 }
  - { offset: 0x5FD0C, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x4C0, symBinAddr: 0x5C04, symSize: 0x48 }
  - { offset: 0x5FE16, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0x5788, symSize: 0xB4 }
  - { offset: 0x5FE92, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfC', symObjAddr: 0xB4, symBinAddr: 0x583C, symSize: 0x20 }
  - { offset: 0x5FEB3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZTo', symObjAddr: 0xF4, symBinAddr: 0x587C, symSize: 0xCC }
  - { offset: 0x5FF2B, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfc', symObjAddr: 0x1C0, symBinAddr: 0x5948, symSize: 0x30 }
  - { offset: 0x5FF5E, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfcTo', symObjAddr: 0x1F0, symBinAddr: 0x5978, symSize: 0x3C }
  - { offset: 0x5FF93, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCfD', symObjAddr: 0x22C, symBinAddr: 0x59B4, symSize: 0x30 }
  - { offset: 0x5FFE9, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC012getDirectoryD04typeSSSgAA0H4TypeO_tFTf4nd_n', symObjAddr: 0x2A0, symBinAddr: 0x59E4, symSize: 0x90 }
  - { offset: 0x60100, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC012getContainerD018appGroupIdentifierSSSgSS_tFTf4nd_n', symObjAddr: 0x330, symBinAddr: 0x5A74, symSize: 0x190 }
...
