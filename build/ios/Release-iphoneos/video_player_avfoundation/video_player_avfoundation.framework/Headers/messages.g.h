// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v25.5.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import <Foundation/Foundation.h>

@protocol FlutterBinaryMessenger;
@protocol FlutterMessageCodec;
@class FlutterError;
@class FlutterStandardTypedData;

NS_ASSUME_NONNULL_BEGIN

@class FVPPlatformVideoViewCreationParams;
@class FVPCreationOptions;
@class FVPTexturePlayerIds;

/// Information passed to the platform view creation.
@interface FVPPlatformVideoViewCreationParams : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithPlayerId:(NSInteger)playerId;
@property(nonatomic, assign) NSInteger playerId;
@end

@interface FVPCreationOptions : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithUri:(NSString *)uri
                httpHeaders:(NSDictionary<NSString *, NSString *> *)httpHeaders;
@property(nonatomic, copy) NSString *uri;
@property(nonatomic, copy) NSDictionary<NSString *, NSString *> *httpHeaders;
@end

@interface FVPTexturePlayerIds : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithPlayerId:(NSInteger)playerId textureId:(NSInteger)textureId;
@property(nonatomic, assign) NSInteger playerId;
@property(nonatomic, assign) NSInteger textureId;
@end

/// The codec used by all APIs.
NSObject<FlutterMessageCodec> *FVPGetMessagesCodec(void);

@protocol FVPAVFoundationVideoPlayerApi
- (void)initialize:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)createPlatformViewPlayerWithOptions:(FVPCreationOptions *)params
                                                     error:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable FVPTexturePlayerIds *)
    createTexturePlayerWithOptions:(FVPCreationOptions *)creationOptions
                             error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setMixWithOthers:(BOOL)mixWithOthers error:(FlutterError *_Nullable *_Nonnull)error;
- (nullable NSString *)fileURLForAssetWithName:(NSString *)asset
                                       package:(nullable NSString *)package
                                         error:(FlutterError *_Nullable *_Nonnull)error;
@end

extern void SetUpFVPAVFoundationVideoPlayerApi(
    id<FlutterBinaryMessenger> binaryMessenger,
    NSObject<FVPAVFoundationVideoPlayerApi> *_Nullable api);

extern void SetUpFVPAVFoundationVideoPlayerApiWithSuffix(
    id<FlutterBinaryMessenger> binaryMessenger,
    NSObject<FVPAVFoundationVideoPlayerApi> *_Nullable api, NSString *messageChannelSuffix);

@protocol FVPVideoPlayerInstanceApi
- (void)setLooping:(BOOL)looping error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setVolume:(double)volume error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setPlaybackSpeed:(double)speed error:(FlutterError *_Nullable *_Nonnull)error;
- (void)playWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)position:(FlutterError *_Nullable *_Nonnull)error;
- (void)seekTo:(NSInteger)position completion:(void (^)(FlutterError *_Nullable))completion;
- (void)pauseWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)disposeWithError:(FlutterError *_Nullable *_Nonnull)error;
@end

extern void SetUpFVPVideoPlayerInstanceApi(id<FlutterBinaryMessenger> binaryMessenger,
                                           NSObject<FVPVideoPlayerInstanceApi> *_Nullable api);

extern void SetUpFVPVideoPlayerInstanceApiWithSuffix(
    id<FlutterBinaryMessenger> binaryMessenger, NSObject<FVPVideoPlayerInstanceApi> *_Nullable api,
    NSString *messageChannelSuffix);

NS_ASSUME_NONNULL_END
