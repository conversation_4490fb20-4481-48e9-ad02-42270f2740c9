---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/camera/build/ios/Release-iphoneos/video_player_avfoundation/video_player_avfoundation.framework/video_player_avfoundation'
relocations:
  - { offset: 0x7931C, size: 0x8, addend: 0x0, symName: _video_player_avfoundationVersionString, symObjAddr: 0x0, symBinAddr: 0xDAC0, symSize: 0x0 }
  - { offset: 0x79351, size: 0x8, addend: 0x0, symName: _video_player_avfoundationVersionNumber, symObjAddr: 0x38, symBinAddr: 0xDAF8, symSize: 0x0 }
  - { offset: 0x7938E, size: 0x8, addend: 0x0, symName: _FVPGetStandardizedTransformForTrack, symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x198 }
  - { offset: 0x7939C, size: 0x8, addend: 0x0, symName: _FVPGetStandardizedTransformForTrack, symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x198 }
  - { offset: 0x79421, size: 0x8, addend: 0x0, symName: '-[FVPDefaultAVFactory playerWithPlayerItem:]', symObjAddr: 0x0, symBinAddr: 0x4198, symSize: 0xC }
  - { offset: 0x79440, size: 0x8, addend: 0x0, symName: '-[FVPDefaultAVFactory playerWithPlayerItem:]', symObjAddr: 0x0, symBinAddr: 0x4198, symSize: 0xC }
  - { offset: 0x79481, size: 0x8, addend: 0x0, symName: '-[FVPDefaultAVFactory videoOutputWithPixelBufferAttributes:]', symObjAddr: 0xC, symBinAddr: 0x41A4, symSize: 0x4C }
  - { offset: 0x7952A, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget initWithCallback:]', symObjAddr: 0x0, symBinAddr: 0x41F0, symSize: 0x78 }
  - { offset: 0x79622, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget initWithCallback:]', symObjAddr: 0x0, symBinAddr: 0x41F0, symSize: 0x78 }
  - { offset: 0x79669, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget onDisplayLink:]', symObjAddr: 0x78, symBinAddr: 0x4268, symSize: 0x34 }
  - { offset: 0x796B4, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget callback]', symObjAddr: 0xAC, symBinAddr: 0x429C, symSize: 0x8 }
  - { offset: 0x796EB, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget setCallback:]', symObjAddr: 0xB4, symBinAddr: 0x42A4, symSize: 0x8 }
  - { offset: 0x7972A, size: 0x8, addend: 0x0, symName: '-[FVPDisplayLinkTarget .cxx_destruct]', symObjAddr: 0xBC, symBinAddr: 0x42AC, symSize: 0xC }
  - { offset: 0x7975D, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink initWithRegistrar:callback:]', symObjAddr: 0xC8, symBinAddr: 0x42B8, symSize: 0x104 }
  - { offset: 0x797B0, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink dealloc]', symObjAddr: 0x1CC, symBinAddr: 0x43BC, symSize: 0x48 }
  - { offset: 0x797E3, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink running]', symObjAddr: 0x214, symBinAddr: 0x4404, symSize: 0x3C }
  - { offset: 0x7981A, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink setRunning:]', symObjAddr: 0x250, symBinAddr: 0x4440, symSize: 0x38 }
  - { offset: 0x7985D, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink duration]', symObjAddr: 0x288, symBinAddr: 0x4478, symSize: 0x44 }
  - { offset: 0x79894, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink displayLink]', symObjAddr: 0x2CC, symBinAddr: 0x44BC, symSize: 0x8 }
  - { offset: 0x798CB, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink setDisplayLink:]', symObjAddr: 0x2D4, symBinAddr: 0x44C4, symSize: 0xC }
  - { offset: 0x7990C, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink target]', symObjAddr: 0x2E0, symBinAddr: 0x44D0, symSize: 0x8 }
  - { offset: 0x79943, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink setTarget:]', symObjAddr: 0x2E8, symBinAddr: 0x44D8, symSize: 0xC }
  - { offset: 0x79984, size: 0x8, addend: 0x0, symName: '-[FVPCADisplayLink .cxx_destruct]', symObjAddr: 0x2F4, symBinAddr: 0x44E4, symSize: 0x30 }
  - { offset: 0x79A53, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge initWithMessenger:channelName:]', symObjAddr: 0x0, symBinAddr: 0x4514, symSize: 0xD8 }
  - { offset: 0x79B01, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge initWithMessenger:channelName:]', symObjAddr: 0x0, symBinAddr: 0x4514, symSize: 0xD8 }
  - { offset: 0x79B58, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge onListenWithArguments:eventSink:]', symObjAddr: 0xD8, symBinAddr: 0x45EC, symSize: 0x150 }
  - { offset: 0x79BD6, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge onCancelWithArguments:]', symObjAddr: 0x228, symBinAddr: 0x473C, symSize: 0x34 }
  - { offset: 0x79C19, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidInitializeWithDuration:size:]', symObjAddr: 0x25C, symBinAddr: 0x4770, symSize: 0x14C }
  - { offset: 0x79C6C, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidErrorWithMessage:]', symObjAddr: 0x3A8, symBinAddr: 0x48BC, symSize: 0x54 }
  - { offset: 0x79CAF, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidComplete]', symObjAddr: 0x3FC, symBinAddr: 0x4910, symSize: 0x9C }
  - { offset: 0x79CE2, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidStartBuffering]', symObjAddr: 0x498, symBinAddr: 0x49AC, symSize: 0x9C }
  - { offset: 0x79D15, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidEndBuffering]', symObjAddr: 0x534, symBinAddr: 0x4A48, symSize: 0x9C }
  - { offset: 0x79D48, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidUpdateBufferRegions:]', symObjAddr: 0x5D0, symBinAddr: 0x4AE4, symSize: 0xC8 }
  - { offset: 0x79D8B, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerDidSetPlaying:]', symObjAddr: 0x698, symBinAddr: 0x4BAC, symSize: 0xD4 }
  - { offset: 0x79DCE, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge videoPlayerWasDisposed]', symObjAddr: 0x76C, symBinAddr: 0x4C80, symSize: 0x34 }
  - { offset: 0x79E01, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge sendOrQueue:]', symObjAddr: 0x7A0, symBinAddr: 0x4CB4, symSize: 0x94 }
  - { offset: 0x79E58, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge eventChannel]', symObjAddr: 0x834, symBinAddr: 0x4D48, symSize: 0x8 }
  - { offset: 0x79E8F, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge setEventChannel:]', symObjAddr: 0x83C, symBinAddr: 0x4D50, symSize: 0xC }
  - { offset: 0x79ED0, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge eventSink]', symObjAddr: 0x848, symBinAddr: 0x4D5C, symSize: 0x8 }
  - { offset: 0x79F07, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge setEventSink:]', symObjAddr: 0x850, symBinAddr: 0x4D64, symSize: 0x8 }
  - { offset: 0x79F46, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge queuedEvents]', symObjAddr: 0x858, symBinAddr: 0x4D6C, symSize: 0x8 }
  - { offset: 0x79F7D, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge setQueuedEvents:]', symObjAddr: 0x860, symBinAddr: 0x4D74, symSize: 0xC }
  - { offset: 0x79FBE, size: 0x8, addend: 0x0, symName: '-[FVPEventBridge .cxx_destruct]', symObjAddr: 0x86C, symBinAddr: 0x4D80, symSize: 0x3C }
  - { offset: 0x7A071, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater initWithRegistry:]', symObjAddr: 0x0, symBinAddr: 0x4DBC, symSize: 0x40 }
  - { offset: 0x7A10B, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater initWithRegistry:]', symObjAddr: 0x0, symBinAddr: 0x4DBC, symSize: 0x40 }
  - { offset: 0x7A152, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater displayLinkFired]', symObjAddr: 0x40, symBinAddr: 0x4DFC, symSize: 0x44 }
  - { offset: 0x7A185, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater textureIdentifier]', symObjAddr: 0x84, symBinAddr: 0x4E40, symSize: 0x8 }
  - { offset: 0x7A1BC, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater setTextureIdentifier:]', symObjAddr: 0x8C, symBinAddr: 0x4E48, symSize: 0x8 }
  - { offset: 0x7A1F9, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater registry]', symObjAddr: 0x94, symBinAddr: 0x4E50, symSize: 0x18 }
  - { offset: 0x7A230, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater displayLink]', symObjAddr: 0xAC, symBinAddr: 0x4E68, symSize: 0x8 }
  - { offset: 0x7A267, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater setDisplayLink:]', symObjAddr: 0xB4, symBinAddr: 0x4E70, symSize: 0xC }
  - { offset: 0x7A2A8, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater frameDuration]', symObjAddr: 0xC0, symBinAddr: 0x4E7C, symSize: 0x8 }
  - { offset: 0x7A2DD, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater setFrameDuration:]', symObjAddr: 0xC8, symBinAddr: 0x4E84, symSize: 0x8 }
  - { offset: 0x7A31B, size: 0x8, addend: 0x0, symName: '-[FVPFrameUpdater .cxx_destruct]', symObjAddr: 0xD0, symBinAddr: 0x4E8C, symSize: 0x2C }
  - { offset: 0x7A3A7, size: 0x8, addend: 0x0, symName: '+[FVPPlayerView layerClass]', symObjAddr: 0x0, symBinAddr: 0x4EB8, symSize: 0xC }
  - { offset: 0x7A3F6, size: 0x8, addend: 0x0, symName: '+[FVPPlayerView layerClass]', symObjAddr: 0x0, symBinAddr: 0x4EB8, symSize: 0xC }
  - { offset: 0x7A429, size: 0x8, addend: 0x0, symName: '-[FVPPlayerView setPlayer:]', symObjAddr: 0xC, symBinAddr: 0x4EC4, symSize: 0x50 }
  - { offset: 0x7A46C, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoView initWithPlayer:]', symObjAddr: 0x5C, symBinAddr: 0x4F14, symSize: 0x8C }
  - { offset: 0x7A4B3, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoView view]', symObjAddr: 0xE8, symBinAddr: 0x4FA0, symSize: 0x4 }
  - { offset: 0x7A4E8, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoView playerView]', symObjAddr: 0xEC, symBinAddr: 0x4FA4, symSize: 0x8 }
  - { offset: 0x7A51F, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoView setPlayerView:]', symObjAddr: 0xF4, symBinAddr: 0x4FAC, symSize: 0xC }
  - { offset: 0x7A560, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoView .cxx_destruct]', symObjAddr: 0x100, symBinAddr: 0x4FB8, symSize: 0xC }
  - { offset: 0x7A60E, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory initWithMessenger:playerByIdentifierProvider:]', symObjAddr: 0x0, symBinAddr: 0x4FC4, symSize: 0xA8 }
  - { offset: 0x7A7B8, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory initWithMessenger:playerByIdentifierProvider:]', symObjAddr: 0x0, symBinAddr: 0x4FC4, symSize: 0xA8 }
  - { offset: 0x7A80F, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory createWithFrame:viewIdentifier:arguments:]', symObjAddr: 0xA8, symBinAddr: 0x506C, symSize: 0xD0 }
  - { offset: 0x7A8A2, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory createArgsCodec]', symObjAddr: 0x178, symBinAddr: 0x513C, symSize: 0x4 }
  - { offset: 0x7A8D8, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory createArgsCodec]', symObjAddr: 0x178, symBinAddr: 0x513C, symSize: 0x4 }
  - { offset: 0x7A8EF, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory messenger]', symObjAddr: 0x17C, symBinAddr: 0x5140, symSize: 0x8 }
  - { offset: 0x7A926, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory setMessenger:]', symObjAddr: 0x184, symBinAddr: 0x5148, symSize: 0xC }
  - { offset: 0x7A967, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory playerByIdProvider]', symObjAddr: 0x190, symBinAddr: 0x5154, symSize: 0x8 }
  - { offset: 0x7A99E, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory setPlayerByIdProvider:]', symObjAddr: 0x198, symBinAddr: 0x515C, symSize: 0x8 }
  - { offset: 0x7A9DD, size: 0x8, addend: 0x0, symName: '-[FVPNativeVideoViewFactory .cxx_destruct]', symObjAddr: 0x1A0, symBinAddr: 0x5164, symSize: 0x30 }
  - { offset: 0x7AA99, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer initWithPlayerItem:frameUpdater:displayLink:avFactory:viewProvider:]', symObjAddr: 0x0, symBinAddr: 0x5194, symSize: 0x1B0 }
  - { offset: 0x7AAA7, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer copyPixelBuffer]', symObjAddr: 0x570, symBinAddr: 0x5704, symSize: 0x318 }
  - { offset: 0x7AECC, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer initWithPlayerItem:frameUpdater:displayLink:avFactory:viewProvider:]', symObjAddr: 0x0, symBinAddr: 0x5194, symSize: 0x1B0 }
  - { offset: 0x7AF53, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer dealloc]', symObjAddr: 0x1B0, symBinAddr: 0x5344, symSize: 0x50 }
  - { offset: 0x7AFA2, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setTextureIdentifier:]', symObjAddr: 0x200, symBinAddr: 0x5394, symSize: 0x4C }
  - { offset: 0x7AFE5, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer expectFrame]', symObjAddr: 0x24C, symBinAddr: 0x53E0, symSize: 0x34 }
  - { offset: 0x7B018, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer updatePlayingState]', symObjAddr: 0x280, symBinAddr: 0x5414, symSize: 0x70 }
  - { offset: 0x7B04B, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer seekTo:completion:]', symObjAddr: 0x2F0, symBinAddr: 0x5484, symSize: 0xF8 }
  - { offset: 0x7B0AD, size: 0x8, addend: 0x0, symName: '___48-[FVPTextureBasedVideoPlayer seekTo:completion:]_block_invoke', symObjAddr: 0x3E8, symBinAddr: 0x557C, symSize: 0xB8 }
  - { offset: 0x7B165, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x4A0, symBinAddr: 0x5634, symSize: 0x34 }
  - { offset: 0x7B18E, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x4D4, symBinAddr: 0x5668, symSize: 0x28 }
  - { offset: 0x7B1AD, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer disposeWithError:]', symObjAddr: 0x4FC, symBinAddr: 0x5690, symSize: 0x74 }
  - { offset: 0x7B236, size: 0x8, addend: 0x0, symName: '___45-[FVPTextureBasedVideoPlayer copyPixelBuffer]_block_invoke', symObjAddr: 0x888, symBinAddr: 0x5A1C, symSize: 0x80 }
  - { offset: 0x7B275, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x908, symBinAddr: 0x5A9C, symSize: 0x8 }
  - { offset: 0x7B29C, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x910, symBinAddr: 0x5AA4, symSize: 0x8 }
  - { offset: 0x7B2BB, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer onTextureUnregistered:]', symObjAddr: 0x918, symBinAddr: 0x5AAC, symSize: 0x58 }
  - { offset: 0x7B310, size: 0x8, addend: 0x0, symName: '___52-[FVPTextureBasedVideoPlayer onTextureUnregistered:]_block_invoke', symObjAddr: 0x970, symBinAddr: 0x5B04, symSize: 0x40 }
  - { offset: 0x7B36E, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer frameUpdater]', symObjAddr: 0x9B0, symBinAddr: 0x5B44, symSize: 0x10 }
  - { offset: 0x7B3A5, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setFrameUpdater:]', symObjAddr: 0x9C0, symBinAddr: 0x5B54, symSize: 0x14 }
  - { offset: 0x7B3E6, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer displayLink]', symObjAddr: 0x9D4, symBinAddr: 0x5B68, symSize: 0x10 }
  - { offset: 0x7B41D, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setDisplayLink:]', symObjAddr: 0x9E4, symBinAddr: 0x5B78, symSize: 0x14 }
  - { offset: 0x7B45E, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer latestPixelBuffer]', symObjAddr: 0x9F8, symBinAddr: 0x5B8C, symSize: 0x10 }
  - { offset: 0x7B495, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setLatestPixelBuffer:]', symObjAddr: 0xA08, symBinAddr: 0x5B9C, symSize: 0x10 }
  - { offset: 0x7B4D2, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer targetTime]', symObjAddr: 0xA18, symBinAddr: 0x5BAC, symSize: 0x10 }
  - { offset: 0x7B507, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setTargetTime:]', symObjAddr: 0xA28, symBinAddr: 0x5BBC, symSize: 0x10 }
  - { offset: 0x7B545, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer selfRefresh]', symObjAddr: 0xA38, symBinAddr: 0x5BCC, symSize: 0x10 }
  - { offset: 0x7B57C, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setSelfRefresh:]', symObjAddr: 0xA48, symBinAddr: 0x5BDC, symSize: 0x10 }
  - { offset: 0x7B5B7, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer startTime]', symObjAddr: 0xA58, symBinAddr: 0x5BEC, symSize: 0x10 }
  - { offset: 0x7B5EC, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setStartTime:]', symObjAddr: 0xA68, symBinAddr: 0x5BFC, symSize: 0x10 }
  - { offset: 0x7B62A, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer framesCount]', symObjAddr: 0xA78, symBinAddr: 0x5C0C, symSize: 0x10 }
  - { offset: 0x7B661, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setFramesCount:]', symObjAddr: 0xA88, symBinAddr: 0x5C1C, symSize: 0x10 }
  - { offset: 0x7B69E, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer latestDuration]', symObjAddr: 0xA98, symBinAddr: 0x5C2C, symSize: 0x10 }
  - { offset: 0x7B6D3, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setLatestDuration:]', symObjAddr: 0xAA8, symBinAddr: 0x5C3C, symSize: 0x10 }
  - { offset: 0x7B711, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer waitingForFrame]', symObjAddr: 0xAB8, symBinAddr: 0x5C4C, symSize: 0x10 }
  - { offset: 0x7B748, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer setWaitingForFrame:]', symObjAddr: 0xAC8, symBinAddr: 0x5C5C, symSize: 0x10 }
  - { offset: 0x7B783, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer playerLayer]', symObjAddr: 0xAD8, symBinAddr: 0x5C6C, symSize: 0x10 }
  - { offset: 0x7B7BA, size: 0x8, addend: 0x0, symName: '-[FVPTextureBasedVideoPlayer .cxx_destruct]', symObjAddr: 0xAE8, symBinAddr: 0x5C7C, symSize: 0x54 }
  - { offset: 0x7BA72, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer initWithPlayerItem:avFactory:viewProvider:]', symObjAddr: 0x0, symBinAddr: 0x5CD0, symSize: 0x2C0 }
  - { offset: 0x7BA8C, size: 0x8, addend: 0x0, symName: _TIME_UNSET, symObjAddr: 0x2D28, symBinAddr: 0xDB28, symSize: 0x0 }
  - { offset: 0x7BAAE, size: 0x8, addend: 0x0, symName: _timeRangeContext, symObjAddr: 0x2D30, symBinAddr: 0x19488, symSize: 0x0 }
  - { offset: 0x7BAC5, size: 0x8, addend: 0x0, symName: _statusContext, symObjAddr: 0x2D38, symBinAddr: 0x19490, symSize: 0x0 }
  - { offset: 0x7BADB, size: 0x8, addend: 0x0, symName: _presentationSizeContext, symObjAddr: 0x2D40, symBinAddr: 0x19498, symSize: 0x0 }
  - { offset: 0x7BAF1, size: 0x8, addend: 0x0, symName: _durationContext, symObjAddr: 0x2D48, symBinAddr: 0x194A0, symSize: 0x0 }
  - { offset: 0x7BB07, size: 0x8, addend: 0x0, symName: _playbackLikelyToKeepUpContext, symObjAddr: 0x2D50, symBinAddr: 0x194A8, symSize: 0x0 }
  - { offset: 0x7BB1D, size: 0x8, addend: 0x0, symName: _rateContext, symObjAddr: 0x2D58, symBinAddr: 0x194B0, symSize: 0x0 }
  - { offset: 0x7BD48, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer initWithPlayerItem:avFactory:viewProvider:]', symObjAddr: 0x0, symBinAddr: 0x5CD0, symSize: 0x2C0 }
  - { offset: 0x7BDDF, size: 0x8, addend: 0x0, symName: '___60-[FVPVideoPlayer initWithPlayerItem:avFactory:viewProvider:]_block_invoke', symObjAddr: 0x2C0, symBinAddr: 0x5F90, symSize: 0x198 }
  - { offset: 0x7BE8C, size: 0x8, addend: 0x0, symName: '___60-[FVPVideoPlayer initWithPlayerItem:avFactory:viewProvider:]_block_invoke_2', symObjAddr: 0x458, symBinAddr: 0x6128, symSize: 0xD4 }
  - { offset: 0x7BF6F, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s, symObjAddr: 0x52C, symBinAddr: 0x61FC, symSize: 0x38 }
  - { offset: 0x7BF98, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s, symObjAddr: 0x564, symBinAddr: 0x6234, symSize: 0x38 }
  - { offset: 0x7BFB7, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s, symObjAddr: 0x59C, symBinAddr: 0x626C, symSize: 0x30 }
  - { offset: 0x7BFE0, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x5CC, symBinAddr: 0x629C, symSize: 0x30 }
  - { offset: 0x7BFFF, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer dealloc]', symObjAddr: 0x5FC, symBinAddr: 0x62CC, symSize: 0xC8 }
  - { offset: 0x7C08F, size: 0x8, addend: 0x0, symName: _FVPGetPlayerItemObservations, symObjAddr: 0x7F0, symBinAddr: 0x64C0, symSize: 0x18C }
  - { offset: 0x7C0A9, size: 0x8, addend: 0x0, symName: _FVPRemoveKeyValueObservers, symObjAddr: 0x6C4, symBinAddr: 0x6394, symSize: 0x12C }
  - { offset: 0x7C10F, size: 0x8, addend: 0x0, symName: _FVPGetPlayerObservations, symObjAddr: 0x97C, symBinAddr: 0x664C, symSize: 0xAC }
  - { offset: 0x7C129, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer disposeWithError:]', symObjAddr: 0xA28, symBinAddr: 0x66F8, symSize: 0x15C }
  - { offset: 0x7C1D8, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setEventListener:]', symObjAddr: 0xB84, symBinAddr: 0x6854, symSize: 0x128 }
  - { offset: 0x7C297, size: 0x8, addend: 0x0, symName: _FVPRegisterKeyValueObservers, symObjAddr: 0xCAC, symBinAddr: 0x697C, symSize: 0x164 }
  - { offset: 0x7C2FB, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer itemDidPlayToEndTime:]', symObjAddr: 0xE10, symBinAddr: 0x6AE0, symSize: 0x94 }
  - { offset: 0x7C3CA, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer getVideoCompositionWithTransform:withAsset:withVideoTrack:]', symObjAddr: 0xEA4, symBinAddr: 0x6B74, symSize: 0x314 }
  - { offset: 0x7C5D9, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer observeValueForKeyPath:ofObject:change:context:]', symObjAddr: 0x11B8, symBinAddr: 0x6E88, symSize: 0x3BC }
  - { offset: 0x7C720, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer reportStatusForPlayerItem:]', symObjAddr: 0x1574, symBinAddr: 0x7244, symSize: 0x60 }
  - { offset: 0x7C765, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer updatePlayingState]', symObjAddr: 0x15D4, symBinAddr: 0x72A4, symSize: 0x38 }
  - { offset: 0x7C799, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer updateRate]', symObjAddr: 0x160C, symBinAddr: 0x72DC, symSize: 0x100 }
  - { offset: 0x7C7EB, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer sendFailedToLoadVideoEvent]', symObjAddr: 0x170C, symBinAddr: 0x73DC, symSize: 0x310 }
  - { offset: 0x7C8E4, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x1A1C, symBinAddr: 0x76EC, symSize: 0x10 }
  - { offset: 0x7C909, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x1A2C, symBinAddr: 0x76FC, symSize: 0x8 }
  - { offset: 0x7C928, size: 0x8, addend: 0x0, symName: '___44-[FVPVideoPlayer sendFailedToLoadVideoEvent]_block_invoke', symObjAddr: 0x1A34, symBinAddr: 0x7704, symSize: 0x1C }
  - { offset: 0x7C975, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32r, symObjAddr: 0x1A50, symBinAddr: 0x7720, symSize: 0x10 }
  - { offset: 0x7C99E, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32r, symObjAddr: 0x1A60, symBinAddr: 0x7730, symSize: 0xC }
  - { offset: 0x7C9BD, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer reportInitializedIfReadyToPlay]', symObjAddr: 0x1A6C, symBinAddr: 0x773C, symSize: 0x284 }
  - { offset: 0x7CAA8, size: 0x8, addend: 0x0, symName: '___48-[FVPVideoPlayer reportInitializedIfReadyToPlay]_block_invoke', symObjAddr: 0x1CF0, symBinAddr: 0x79C0, symSize: 0x88 }
  - { offset: 0x7CB0C, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x1D78, symBinAddr: 0x7A48, symSize: 0x28 }
  - { offset: 0x7CB35, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer playWithError:]', symObjAddr: 0x1DC8, symBinAddr: 0x7A70, symSize: 0xC }
  - { offset: 0x7CB74, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer pauseWithError:]', symObjAddr: 0x1DD4, symBinAddr: 0x7A7C, symSize: 0x8 }
  - { offset: 0x7CBB3, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer position:]', symObjAddr: 0x1DDC, symBinAddr: 0x7A84, symSize: 0x88 }
  - { offset: 0x7CC11, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer seekTo:completion:]', symObjAddr: 0x1E64, symBinAddr: 0x7B0C, symSize: 0x17C }
  - { offset: 0x7CCE5, size: 0x8, addend: 0x0, symName: '___36-[FVPVideoPlayer seekTo:completion:]_block_invoke', symObjAddr: 0x1FE0, symBinAddr: 0x7C88, symSize: 0x70 }
  - { offset: 0x7CD59, size: 0x8, addend: 0x0, symName: '___36-[FVPVideoPlayer seekTo:completion:]_block_invoke_2', symObjAddr: 0x2050, symBinAddr: 0x7CF8, symSize: 0x10 }
  - { offset: 0x7CDAA, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0x2060, symBinAddr: 0x7D08, symSize: 0x10 }
  - { offset: 0x7CDD3, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setLooping:error:]', symObjAddr: 0x2078, symBinAddr: 0x7D18, symSize: 0x8 }
  - { offset: 0x7CE1F, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setVolume:error:]', symObjAddr: 0x2080, symBinAddr: 0x7D20, symSize: 0x24 }
  - { offset: 0x7CE71, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setPlaybackSpeed:error:]', symObjAddr: 0x20A4, symBinAddr: 0x7D44, symSize: 0x44 }
  - { offset: 0x7CEC3, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer duration]', symObjAddr: 0x20E8, symBinAddr: 0x7D88, symSize: 0xB0 }
  - { offset: 0x7CF14, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer player]', symObjAddr: 0x2198, symBinAddr: 0x7E38, symSize: 0x8 }
  - { offset: 0x7CF4B, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer disposed]', symObjAddr: 0x21A0, symBinAddr: 0x7E40, symSize: 0x8 }
  - { offset: 0x7CF82, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer isLooping]', symObjAddr: 0x21A8, symBinAddr: 0x7E48, symSize: 0x8 }
  - { offset: 0x7CFB9, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setIsLooping:]', symObjAddr: 0x21B0, symBinAddr: 0x7E50, symSize: 0x8 }
  - { offset: 0x7CFF4, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer position]', symObjAddr: 0x21B8, symBinAddr: 0x7E58, symSize: 0x8 }
  - { offset: 0x7D02B, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer eventListener]', symObjAddr: 0x21C0, symBinAddr: 0x7E60, symSize: 0x8 }
  - { offset: 0x7D062, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer onDisposed]', symObjAddr: 0x21C8, symBinAddr: 0x7E68, symSize: 0x8 }
  - { offset: 0x7D099, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setOnDisposed:]', symObjAddr: 0x21D0, symBinAddr: 0x7E70, symSize: 0x8 }
  - { offset: 0x7D0D8, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer videoOutput]', symObjAddr: 0x21D8, symBinAddr: 0x7E78, symSize: 0x8 }
  - { offset: 0x7D10F, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer viewProvider]', symObjAddr: 0x21E0, symBinAddr: 0x7E80, symSize: 0x8 }
  - { offset: 0x7D146, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer preferredTransform]', symObjAddr: 0x21E8, symBinAddr: 0x7E88, symSize: 0x18 }
  - { offset: 0x7D17B, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer setPreferredTransform:]', symObjAddr: 0x2200, symBinAddr: 0x7EA0, symSize: 0x18 }
  - { offset: 0x7D1B9, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer targetPlaybackSpeed]', symObjAddr: 0x2218, symBinAddr: 0x7EB8, symSize: 0x8 }
  - { offset: 0x7D1F0, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer isPlaying]', symObjAddr: 0x2220, symBinAddr: 0x7EC0, symSize: 0x8 }
  - { offset: 0x7D227, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer isInitialized]', symObjAddr: 0x2228, symBinAddr: 0x7EC8, symSize: 0x8 }
  - { offset: 0x7D25E, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayer .cxx_destruct]', symObjAddr: 0x2230, symBinAddr: 0x7ED0, symSize: 0x60 }
  - { offset: 0x7D746, size: 0x8, addend: 0x0, symName: '-[FVPDefaultDisplayLinkFactory displayLinkWithRegistrar:callback:]', symObjAddr: 0x0, symBinAddr: 0x7F30, symSize: 0x70 }
  - { offset: 0x7D84D, size: 0x8, addend: 0x0, symName: '-[FVPDefaultDisplayLinkFactory displayLinkWithRegistrar:callback:]', symObjAddr: 0x0, symBinAddr: 0x7F30, symSize: 0x70 }
  - { offset: 0x7D8A0, size: 0x8, addend: 0x0, symName: '+[FVPVideoPlayerPlugin registerWithRegistrar:]', symObjAddr: 0x70, symBinAddr: 0x7FA0, symSize: 0x134 }
  - { offset: 0x7D92F, size: 0x8, addend: 0x0, symName: '___46+[FVPVideoPlayerPlugin registerWithRegistrar:]_block_invoke', symObjAddr: 0x1A4, symBinAddr: 0x80D4, symSize: 0x10 }
  - { offset: 0x7D97E, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin initWithRegistrar:]', symObjAddr: 0x1C4, symBinAddr: 0x80E4, symSize: 0xBC }
  - { offset: 0x7D9C5, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin initWithAVFactory:displayLinkFactory:viewProvider:registrar:]', symObjAddr: 0x280, symBinAddr: 0x81A0, symSize: 0x198 }
  - { offset: 0x7DA3C, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin detachFromEngineForRegistrar:]', symObjAddr: 0x418, symBinAddr: 0x8338, symSize: 0x1C8 }
  - { offset: 0x7DABB, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin configurePlayer:withExtraDisposeHandler:]', symObjAddr: 0x5E0, symBinAddr: 0x8500, symSize: 0x274 }
  - { offset: 0x7DBA3, size: 0x8, addend: 0x0, symName: '___64-[FVPVideoPlayerPlugin configurePlayer:withExtraDisposeHandler:]_block_invoke', symObjAddr: 0x854, symBinAddr: 0x8774, symSize: 0x9C }
  - { offset: 0x7DC3F, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b56w, symObjAddr: 0x8F0, symBinAddr: 0x8810, symSize: 0x48 }
  - { offset: 0x7DC68, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56w, symObjAddr: 0x938, symBinAddr: 0x8858, symSize: 0x38 }
  - { offset: 0x7DC87, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin initialize:]', symObjAddr: 0x970, symBinAddr: 0x8890, symSize: 0x1A4 }
  - { offset: 0x7DD18, size: 0x8, addend: 0x0, symName: _upgradeAudioSessionCategory, symObjAddr: 0xB14, symBinAddr: 0x8A34, symSize: 0x248 }
  - { offset: 0x7DDAB, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin createPlatformViewPlayerWithOptions:error:]', symObjAddr: 0xD5C, symBinAddr: 0x8C7C, symSize: 0x198 }
  - { offset: 0x7DE44, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin createTexturePlayerWithOptions:error:]', symObjAddr: 0xEF4, symBinAddr: 0x8E14, symSize: 0x368 }
  - { offset: 0x7DF2D, size: 0x8, addend: 0x0, symName: '___61-[FVPVideoPlayerPlugin createTexturePlayerWithOptions:error:]_block_invoke', symObjAddr: 0x125C, symBinAddr: 0x917C, symSize: 0x8 }
  - { offset: 0x7DF68, size: 0x8, addend: 0x0, symName: '___61-[FVPVideoPlayerPlugin createTexturePlayerWithOptions:error:]_block_invoke.25', symObjAddr: 0x1264, symBinAddr: 0x9184, symSize: 0x6C }
  - { offset: 0x7DFB3, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32w, symObjAddr: 0x12D0, symBinAddr: 0x91F0, symSize: 0xC }
  - { offset: 0x7DFDC, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32w, symObjAddr: 0x12DC, symBinAddr: 0x91FC, symSize: 0x8 }
  - { offset: 0x7DFFB, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin setMixWithOthers:error:]', symObjAddr: 0x12E4, symBinAddr: 0x9204, symSize: 0x74 }
  - { offset: 0x7E054, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin fileURLForAssetWithName:package:error:]', symObjAddr: 0x1358, symBinAddr: 0x9278, symSize: 0x12C }
  - { offset: 0x7E0D7, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin playerItemWithCreationOptions:]', symObjAddr: 0x1484, symBinAddr: 0x93A4, symSize: 0x168 }
  - { offset: 0x7E14F, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin registrar]', symObjAddr: 0x15EC, symBinAddr: 0x950C, symSize: 0x8 }
  - { offset: 0x7E186, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin displayLinkFactory]', symObjAddr: 0x15F4, symBinAddr: 0x9514, symSize: 0x8 }
  - { offset: 0x7E1BD, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin setDisplayLinkFactory:]', symObjAddr: 0x15FC, symBinAddr: 0x951C, symSize: 0xC }
  - { offset: 0x7E1FE, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin avFactory]', symObjAddr: 0x1608, symBinAddr: 0x9528, symSize: 0x8 }
  - { offset: 0x7E235, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin setAvFactory:]', symObjAddr: 0x1610, symBinAddr: 0x9530, symSize: 0xC }
  - { offset: 0x7E276, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin viewProvider]', symObjAddr: 0x161C, symBinAddr: 0x953C, symSize: 0x8 }
  - { offset: 0x7E2AD, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin setViewProvider:]', symObjAddr: 0x1624, symBinAddr: 0x9544, symSize: 0xC }
  - { offset: 0x7E2EE, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin nextPlayerIdentifier]', symObjAddr: 0x1630, symBinAddr: 0x9550, symSize: 0x8 }
  - { offset: 0x7E325, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin setNextPlayerIdentifier:]', symObjAddr: 0x1638, symBinAddr: 0x9558, symSize: 0x8 }
  - { offset: 0x7E362, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin playersByIdentifier]', symObjAddr: 0x1640, symBinAddr: 0x9560, symSize: 0x8 }
  - { offset: 0x7E399, size: 0x8, addend: 0x0, symName: '-[FVPVideoPlayerPlugin .cxx_destruct]', symObjAddr: 0x1648, symBinAddr: 0x9568, symSize: 0x54 }
  - { offset: 0x7E894, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider initWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x95BC, symSize: 0x78 }
  - { offset: 0x7E8D2, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider initWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x95BC, symSize: 0x78 }
  - { offset: 0x7E919, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider view]', symObjAddr: 0x78, symBinAddr: 0x9634, symSize: 0x88 }
  - { offset: 0x7E95C, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider registrar]', symObjAddr: 0x100, symBinAddr: 0x96BC, symSize: 0x8 }
  - { offset: 0x7E993, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider setRegistrar:]', symObjAddr: 0x108, symBinAddr: 0x96C4, symSize: 0xC }
  - { offset: 0x7E9D4, size: 0x8, addend: 0x0, symName: '-[FVPDefaultViewProvider .cxx_destruct]', symObjAddr: 0x114, symBinAddr: 0x96D0, symSize: 0xC }
  - { offset: 0x7EA95, size: 0x8, addend: 0x0, symName: '+[FVPPlatformVideoViewCreationParams makeWithPlayerId:]', symObjAddr: 0x0, symBinAddr: 0x96DC, symSize: 0x3C }
  - { offset: 0x7EAA3, size: 0x8, addend: 0x0, symName: _FVPGetMessagesCodec, symObjAddr: 0x954, symBinAddr: 0xA030, symSize: 0x30 }
  - { offset: 0x7EAC9, size: 0x8, addend: 0x0, symName: _FVPGetMessagesCodec.sSharedObject, symObjAddr: 0xB7A0, symBinAddr: 0x19758, symSize: 0x0 }
  - { offset: 0x7EADF, size: 0x8, addend: 0x0, symName: _FVPGetMessagesCodec.sPred, symObjAddr: 0xB7A8, symBinAddr: 0x19760, symSize: 0x0 }
  - { offset: 0x7EC02, size: 0x8, addend: 0x0, symName: '+[FVPPlatformVideoViewCreationParams makeWithPlayerId:]', symObjAddr: 0x0, symBinAddr: 0x96DC, symSize: 0x3C }
  - { offset: 0x7EC55, size: 0x8, addend: 0x0, symName: '+[FVPPlatformVideoViewCreationParams fromList:]', symObjAddr: 0x3C, symBinAddr: 0x9718, symSize: 0x84 }
  - { offset: 0x7ECC4, size: 0x8, addend: 0x0, symName: _GetNullableObjectAtIndex, symObjAddr: 0xC0, symBinAddr: 0x979C, symSize: 0x70 }
  - { offset: 0x7ED0F, size: 0x8, addend: 0x0, symName: '+[FVPPlatformVideoViewCreationParams nullableFromList:]', symObjAddr: 0x130, symBinAddr: 0x980C, symSize: 0x30 }
  - { offset: 0x7ED52, size: 0x8, addend: 0x0, symName: '-[FVPPlatformVideoViewCreationParams toList]', symObjAddr: 0x160, symBinAddr: 0x983C, symSize: 0xA0 }
  - { offset: 0x7ED89, size: 0x8, addend: 0x0, symName: '-[FVPPlatformVideoViewCreationParams playerId]', symObjAddr: 0x200, symBinAddr: 0x98DC, symSize: 0x8 }
  - { offset: 0x7EDC0, size: 0x8, addend: 0x0, symName: '-[FVPPlatformVideoViewCreationParams setPlayerId:]', symObjAddr: 0x208, symBinAddr: 0x98E4, symSize: 0x8 }
  - { offset: 0x7EDFD, size: 0x8, addend: 0x0, symName: '+[FVPCreationOptions makeWithUri:httpHeaders:]', symObjAddr: 0x210, symBinAddr: 0x98EC, symSize: 0x7C }
  - { offset: 0x7EE60, size: 0x8, addend: 0x0, symName: '+[FVPCreationOptions fromList:]', symObjAddr: 0x28C, symBinAddr: 0x9968, symSize: 0xA8 }
  - { offset: 0x7EEEB, size: 0x8, addend: 0x0, symName: '+[FVPCreationOptions nullableFromList:]', symObjAddr: 0x334, symBinAddr: 0x9A10, symSize: 0x30 }
  - { offset: 0x7EF2E, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions toList]', symObjAddr: 0x364, symBinAddr: 0x9A40, symSize: 0x118 }
  - { offset: 0x7EF65, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions uri]', symObjAddr: 0x47C, symBinAddr: 0x9B58, symSize: 0x8 }
  - { offset: 0x7EF9C, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions setUri:]', symObjAddr: 0x484, symBinAddr: 0x9B60, symSize: 0x8 }
  - { offset: 0x7EFDB, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions httpHeaders]', symObjAddr: 0x48C, symBinAddr: 0x9B68, symSize: 0x8 }
  - { offset: 0x7F012, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions setHttpHeaders:]', symObjAddr: 0x494, symBinAddr: 0x9B70, symSize: 0x8 }
  - { offset: 0x7F051, size: 0x8, addend: 0x0, symName: '-[FVPCreationOptions .cxx_destruct]', symObjAddr: 0x49C, symBinAddr: 0x9B78, symSize: 0x30 }
  - { offset: 0x7F084, size: 0x8, addend: 0x0, symName: '+[FVPTexturePlayerIds makeWithPlayerId:textureId:]', symObjAddr: 0x4CC, symBinAddr: 0x9BA8, symSize: 0x54 }
  - { offset: 0x7F0E7, size: 0x8, addend: 0x0, symName: '+[FVPTexturePlayerIds fromList:]', symObjAddr: 0x520, symBinAddr: 0x9BFC, symSize: 0xB4 }
  - { offset: 0x7F172, size: 0x8, addend: 0x0, symName: '+[FVPTexturePlayerIds nullableFromList:]', symObjAddr: 0x5D4, symBinAddr: 0x9CB0, symSize: 0x30 }
  - { offset: 0x7F1B5, size: 0x8, addend: 0x0, symName: '-[FVPTexturePlayerIds toList]', symObjAddr: 0x604, symBinAddr: 0x9CE0, symSize: 0xDC }
  - { offset: 0x7F1EC, size: 0x8, addend: 0x0, symName: '-[FVPTexturePlayerIds playerId]', symObjAddr: 0x6E0, symBinAddr: 0x9DBC, symSize: 0x8 }
  - { offset: 0x7F223, size: 0x8, addend: 0x0, symName: '-[FVPTexturePlayerIds setPlayerId:]', symObjAddr: 0x6E8, symBinAddr: 0x9DC4, symSize: 0x8 }
  - { offset: 0x7F260, size: 0x8, addend: 0x0, symName: '-[FVPTexturePlayerIds textureId]', symObjAddr: 0x6F0, symBinAddr: 0x9DCC, symSize: 0x8 }
  - { offset: 0x7F297, size: 0x8, addend: 0x0, symName: '-[FVPTexturePlayerIds setTextureId:]', symObjAddr: 0x6F8, symBinAddr: 0x9DD4, symSize: 0x8 }
  - { offset: 0x7F2D4, size: 0x8, addend: 0x0, symName: '-[FVPMessagesPigeonCodecReader readValueOfType:]', symObjAddr: 0x700, symBinAddr: 0x9DDC, symSize: 0xB8 }
  - { offset: 0x7F31B, size: 0x8, addend: 0x0, symName: '-[FVPMessagesPigeonCodecWriter writeValue:]', symObjAddr: 0x7B8, symBinAddr: 0x9E94, symSize: 0x104 }
  - { offset: 0x7F35E, size: 0x8, addend: 0x0, symName: '-[FVPMessagesPigeonCodecReaderWriter writerWithData:]', symObjAddr: 0x8BC, symBinAddr: 0x9F98, symSize: 0x4C }
  - { offset: 0x7F3A1, size: 0x8, addend: 0x0, symName: '-[FVPMessagesPigeonCodecReaderWriter readerWithData:]', symObjAddr: 0x908, symBinAddr: 0x9FE4, symSize: 0x4C }
  - { offset: 0x7F41F, size: 0x8, addend: 0x0, symName: _FVPGetMessagesCodec.cold.1, symObjAddr: 0x2240, symBinAddr: 0xB8FC, symSize: 0x14 }
  - { offset: 0x7F43B, size: 0x8, addend: 0x0, symName: _FVPGetMessagesCodec.cold.1, symObjAddr: 0x2240, symBinAddr: 0xB8FC, symSize: 0x14 }
  - { offset: 0x7F44D, size: 0x8, addend: 0x0, symName: ___FVPGetMessagesCodec_block_invoke, symObjAddr: 0x984, symBinAddr: 0xA060, symSize: 0x5C }
  - { offset: 0x7F493, size: 0x8, addend: 0x0, symName: _SetUpFVPAVFoundationVideoPlayerApi, symObjAddr: 0x9E0, symBinAddr: 0xA0BC, symSize: 0xC }
  - { offset: 0x7F4E5, size: 0x8, addend: 0x0, symName: _SetUpFVPAVFoundationVideoPlayerApiWithSuffix, symObjAddr: 0x9EC, symBinAddr: 0xA0C8, symSize: 0x4C0 }
  - { offset: 0x7F60F, size: 0x8, addend: 0x0, symName: ___SetUpFVPAVFoundationVideoPlayerApiWithSuffix_block_invoke, symObjAddr: 0xEAC, symBinAddr: 0xA588, symSize: 0x94 }
  - { offset: 0x7F6BC, size: 0x8, addend: 0x0, symName: _wrapResult, symObjAddr: 0xF40, symBinAddr: 0xA61C, symSize: 0x1EC }
  - { offset: 0x7F6F7, size: 0x8, addend: 0x0, symName: ___SetUpFVPAVFoundationVideoPlayerApiWithSuffix_block_invoke.71, symObjAddr: 0x113C, symBinAddr: 0xA808, symSize: 0xEC }
  - { offset: 0x7F7F1, size: 0x8, addend: 0x0, symName: ___SetUpFVPAVFoundationVideoPlayerApiWithSuffix_block_invoke_2, symObjAddr: 0x1228, symBinAddr: 0xA8F4, symSize: 0xEC }
  - { offset: 0x7F8EB, size: 0x8, addend: 0x0, symName: ___SetUpFVPAVFoundationVideoPlayerApiWithSuffix_block_invoke_3, symObjAddr: 0x1314, symBinAddr: 0xA9E0, symSize: 0xE0 }
  - { offset: 0x7F9DB, size: 0x8, addend: 0x0, symName: ___SetUpFVPAVFoundationVideoPlayerApiWithSuffix_block_invoke_4, symObjAddr: 0x13F4, symBinAddr: 0xAAC0, symSize: 0x118 }
  - { offset: 0x7FB0A, size: 0x8, addend: 0x0, symName: _SetUpFVPVideoPlayerInstanceApi, symObjAddr: 0x150C, symBinAddr: 0xABD8, symSize: 0xC }
  - { offset: 0x7FB5F, size: 0x8, addend: 0x0, symName: _SetUpFVPVideoPlayerInstanceApiWithSuffix, symObjAddr: 0x1518, symBinAddr: 0xABE4, symSize: 0x70C }
  - { offset: 0x7FD1A, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke, symObjAddr: 0x1C24, symBinAddr: 0xB2F0, symSize: 0xE0 }
  - { offset: 0x7FE0B, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_2, symObjAddr: 0x1D04, symBinAddr: 0xB3D0, symSize: 0xE0 }
  - { offset: 0x7FEFC, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_3, symObjAddr: 0x1DE4, symBinAddr: 0xB4B0, symSize: 0xE0 }
  - { offset: 0x7FFED, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_4, symObjAddr: 0x1EC4, symBinAddr: 0xB590, symSize: 0x94 }
  - { offset: 0x800A0, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_5, symObjAddr: 0x1F58, symBinAddr: 0xB624, symSize: 0xA8 }
  - { offset: 0x80165, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_6, symObjAddr: 0x2000, symBinAddr: 0xB6CC, symSize: 0xC4 }
  - { offset: 0x80212, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_7, symObjAddr: 0x20C4, symBinAddr: 0xB790, symSize: 0x44 }
  - { offset: 0x80299, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke.93, symObjAddr: 0x2118, symBinAddr: 0xB7D4, symSize: 0x94 }
  - { offset: 0x8034C, size: 0x8, addend: 0x0, symName: ___SetUpFVPVideoPlayerInstanceApiWithSuffix_block_invoke_2.96, symObjAddr: 0x21AC, symBinAddr: 0xB868, symSize: 0x94 }
...
