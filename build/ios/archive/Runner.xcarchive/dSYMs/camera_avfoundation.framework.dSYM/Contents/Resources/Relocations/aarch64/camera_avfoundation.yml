---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ctnlridqzlszcscjliwgbinamljf/Build/Intermediates.noindex/ArchiveIntermediates/Runner/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/camera_avfoundation.framework/camera_avfoundation'
relocations:
  - { offset: 0x991FF, size: 0x8, addend: 0x0, symName: _camera_avfoundationVersionString, symObjAddr: 0x0, symBinAddr: 0x25C00, symSize: 0x0 }
  - { offset: 0x99234, size: 0x8, addend: 0x0, symName: _camera_avfoundationVersionNumber, symObjAddr: 0x38, symBinAddr: 0x25C38, symSize: 0x0 }
  - { offset: 0x99271, size: 0x8, addend: 0x0, symName: _FCPGetAVCaptureFlashModeForPigeonFlashMode, symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x24 }
  - { offset: 0x992E9, size: 0x8, addend: 0x0, symName: _FCPGetAVCaptureFlashModeForPigeonFlashMode, symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x24 }
  - { offset: 0x99314, size: 0x8, addend: 0x0, symName: _FCPGetUIDeviceOrientationForPigeonDeviceOrientation, symObjAddr: 0x24, symBinAddr: 0x4024, symSize: 0x20 }
  - { offset: 0x9933F, size: 0x8, addend: 0x0, symName: _FCPGetPigeonDeviceOrientationForOrientation, symObjAddr: 0x44, symBinAddr: 0x4044, symSize: 0x24 }
  - { offset: 0x9936A, size: 0x8, addend: 0x0, symName: _FCPGetPixelFormatForPigeonFormat, symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x1C }
  - { offset: 0x993F9, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter initWithURL:fileType:error:]', symObjAddr: 0x0, symBinAddr: 0x4084, symSize: 0xB0 }
  - { offset: 0x99497, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter initWithURL:fileType:error:]', symObjAddr: 0x0, symBinAddr: 0x4084, symSize: 0xB0 }
  - { offset: 0x994FE, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter startWriting]', symObjAddr: 0xB0, symBinAddr: 0x4134, symSize: 0x8 }
  - { offset: 0x99535, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter finishWritingWithCompletionHandler:]', symObjAddr: 0xB8, symBinAddr: 0x413C, symSize: 0x8 }
  - { offset: 0x99576, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter status]', symObjAddr: 0xC0, symBinAddr: 0x4144, symSize: 0x8 }
  - { offset: 0x995AD, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter error]', symObjAddr: 0xC8, symBinAddr: 0x414C, symSize: 0x8 }
  - { offset: 0x995E4, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter startSessionAtSourceTime:]', symObjAddr: 0xD0, symBinAddr: 0x4154, symSize: 0x34 }
  - { offset: 0x99627, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter addInput:]', symObjAddr: 0x104, symBinAddr: 0x4188, symSize: 0x8 }
  - { offset: 0x99668, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter writer]', symObjAddr: 0x10C, symBinAddr: 0x4190, symSize: 0x8 }
  - { offset: 0x9969F, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter setWriter:]', symObjAddr: 0x114, symBinAddr: 0x4198, symSize: 0xC }
  - { offset: 0x996E0, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriter .cxx_destruct]', symObjAddr: 0x120, symBinAddr: 0x41A4, symSize: 0xC }
  - { offset: 0x99713, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput initWithInput:]', symObjAddr: 0x12C, symBinAddr: 0x41B0, symSize: 0x78 }
  - { offset: 0x9975A, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput appendSampleBuffer:]', symObjAddr: 0x1A4, symBinAddr: 0x4228, symSize: 0x8 }
  - { offset: 0x9979F, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput expectsMediaDataInRealTime]', symObjAddr: 0x1AC, symBinAddr: 0x4230, symSize: 0x8 }
  - { offset: 0x997D6, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput setExpectsMediaDataInRealTime:]', symObjAddr: 0x1B4, symBinAddr: 0x4238, symSize: 0x8 }
  - { offset: 0x9981C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput readyForMoreMediaData]', symObjAddr: 0x1BC, symBinAddr: 0x4240, symSize: 0x8 }
  - { offset: 0x99853, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput input]', symObjAddr: 0x1C4, symBinAddr: 0x4248, symSize: 0x8 }
  - { offset: 0x9988A, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput setInput:]', symObjAddr: 0x1CC, symBinAddr: 0x4250, symSize: 0xC }
  - { offset: 0x998CB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInput .cxx_destruct]', symObjAddr: 0x1D8, symBinAddr: 0x425C, symSize: 0xC }
  - { offset: 0x998FE, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInputPixelBufferAdaptor initWithAdaptor:]', symObjAddr: 0x1E4, symBinAddr: 0x4268, symSize: 0x78 }
  - { offset: 0x99945, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInputPixelBufferAdaptor appendPixelBuffer:withPresentationTime:]', symObjAddr: 0x25C, symBinAddr: 0x42E0, symSize: 0x34 }
  - { offset: 0x9999C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInputPixelBufferAdaptor adaptor]', symObjAddr: 0x290, symBinAddr: 0x4314, symSize: 0x8 }
  - { offset: 0x999D3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInputPixelBufferAdaptor setAdaptor:]', symObjAddr: 0x298, symBinAddr: 0x431C, symSize: 0xC }
  - { offset: 0x99A14, size: 0x8, addend: 0x0, symName: '-[FLTDefaultAssetWriterInputPixelBufferAdaptor .cxx_destruct]', symObjAddr: 0x2A4, symBinAddr: 0x4328, symSize: 0xC }
  - { offset: 0x99B6C, size: 0x8, addend: 0x0, symName: '-[FLTCam initWithConfiguration:error:]', symObjAddr: 0x0, symBinAddr: 0x4334, symSize: 0x538 }
  - { offset: 0x99B86, size: 0x8, addend: 0x0, symName: _errorMethod, symObjAddr: 0x11B0, symBinAddr: 0x342A0, symSize: 0x0 }
  - { offset: 0x9A45F, size: 0x8, addend: 0x0, symName: '-[FLTCam initWithConfiguration:error:]', symObjAddr: 0x0, symBinAddr: 0x4334, symSize: 0x538 }
  - { offset: 0x9A577, size: 0x8, addend: 0x0, symName: '-[FLTCam createConnection:]', symObjAddr: 0x538, symBinAddr: 0x486C, symSize: 0x1DC }
  - { offset: 0x9A5CE, size: 0x8, addend: 0x0, symName: '-[FLTCam updateOrientation]', symObjAddr: 0x714, symBinAddr: 0x4A48, symSize: 0x50 }
  - { offset: 0x9A611, size: 0x8, addend: 0x0, symName: '-[FLTCam updateOrientation:forCaptureOutput:]', symObjAddr: 0x764, symBinAddr: 0x4A98, symSize: 0x7C }
  - { offset: 0x9A674, size: 0x8, addend: 0x0, symName: '-[FLTCam getVideoOrientationForDeviceOrientation:]', symObjAddr: 0x7E0, symBinAddr: 0x4B14, symSize: 0x10 }
  - { offset: 0x9A6B5, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureSessionPreset:withError:]', symObjAddr: 0x7F0, symBinAddr: 0x4B24, symSize: 0x2E8 }
  - { offset: 0x9A757, size: 0x8, addend: 0x0, symName: '-[FLTCam highestResolutionFormatForCaptureDevice:]', symObjAddr: 0xAD8, symBinAddr: 0x4E0C, symSize: 0x1DC }
  - { offset: 0x9A8AF, size: 0x8, addend: 0x0, symName: '-[FLTCam captureDevice]', symObjAddr: 0xCB4, symBinAddr: 0x4FE8, symSize: 0x8 }
  - { offset: 0x9A8E6, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureDevice:]', symObjAddr: 0xCBC, symBinAddr: 0x4FF0, symSize: 0xC }
  - { offset: 0x9A927, size: 0x8, addend: 0x0, symName: '-[FLTCam previewSize]', symObjAddr: 0xCC8, symBinAddr: 0x4FFC, symSize: 0x8 }
  - { offset: 0x9A95C, size: 0x8, addend: 0x0, symName: '-[FLTCam isPreviewPaused]', symObjAddr: 0xCD0, symBinAddr: 0x5004, symSize: 0x8 }
  - { offset: 0x9A993, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsPreviewPaused:]', symObjAddr: 0xCD8, symBinAddr: 0x500C, symSize: 0x8 }
  - { offset: 0x9A9CE, size: 0x8, addend: 0x0, symName: '-[FLTCam onFrameAvailable]', symObjAddr: 0xCE0, symBinAddr: 0x5014, symSize: 0x8 }
  - { offset: 0x9AA05, size: 0x8, addend: 0x0, symName: '-[FLTCam setOnFrameAvailable:]', symObjAddr: 0xCE8, symBinAddr: 0x501C, symSize: 0x8 }
  - { offset: 0x9AA44, size: 0x8, addend: 0x0, symName: '-[FLTCam dartAPI]', symObjAddr: 0xCF0, symBinAddr: 0x5024, symSize: 0x8 }
  - { offset: 0x9AA7B, size: 0x8, addend: 0x0, symName: '-[FLTCam setDartAPI:]', symObjAddr: 0xCF8, symBinAddr: 0x502C, symSize: 0xC }
  - { offset: 0x9AABC, size: 0x8, addend: 0x0, symName: '-[FLTCam videoFormat]', symObjAddr: 0xD04, symBinAddr: 0x5038, symSize: 0x8 }
  - { offset: 0x9AAF3, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoFormat:]', symObjAddr: 0xD0C, symBinAddr: 0x5040, symSize: 0x8 }
  - { offset: 0x9AB30, size: 0x8, addend: 0x0, symName: '-[FLTCam fileFormat]', symObjAddr: 0xD14, symBinAddr: 0x5048, symSize: 0x8 }
  - { offset: 0x9AB67, size: 0x8, addend: 0x0, symName: '-[FLTCam setFileFormat:]', symObjAddr: 0xD1C, symBinAddr: 0x5050, symSize: 0x8 }
  - { offset: 0x9ABA4, size: 0x8, addend: 0x0, symName: '-[FLTCam imageStreamHandler]', symObjAddr: 0xD24, symBinAddr: 0x5058, symSize: 0x8 }
  - { offset: 0x9ABDB, size: 0x8, addend: 0x0, symName: '-[FLTCam setImageStreamHandler:]', symObjAddr: 0xD2C, symBinAddr: 0x5060, symSize: 0xC }
  - { offset: 0x9AC1C, size: 0x8, addend: 0x0, symName: '-[FLTCam streamingPendingFramesCount]', symObjAddr: 0xD38, symBinAddr: 0x506C, symSize: 0x8 }
  - { offset: 0x9AC53, size: 0x8, addend: 0x0, symName: '-[FLTCam setStreamingPendingFramesCount:]', symObjAddr: 0xD40, symBinAddr: 0x5074, symSize: 0x8 }
  - { offset: 0x9AC90, size: 0x8, addend: 0x0, symName: '-[FLTCam isFirstVideoSample]', symObjAddr: 0xD48, symBinAddr: 0x507C, symSize: 0x8 }
  - { offset: 0x9ACC7, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsFirstVideoSample:]', symObjAddr: 0xD50, symBinAddr: 0x5084, symSize: 0x8 }
  - { offset: 0x9AD02, size: 0x8, addend: 0x0, symName: '-[FLTCam isRecording]', symObjAddr: 0xD58, symBinAddr: 0x508C, symSize: 0x8 }
  - { offset: 0x9AD39, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsRecording:]', symObjAddr: 0xD60, symBinAddr: 0x5094, symSize: 0x8 }
  - { offset: 0x9AD74, size: 0x8, addend: 0x0, symName: '-[FLTCam isRecordingPaused]', symObjAddr: 0xD68, symBinAddr: 0x509C, symSize: 0x8 }
  - { offset: 0x9ADAB, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsRecordingPaused:]', symObjAddr: 0xD70, symBinAddr: 0x50A4, symSize: 0x8 }
  - { offset: 0x9ADE6, size: 0x8, addend: 0x0, symName: '-[FLTCam videoWriter]', symObjAddr: 0xD78, symBinAddr: 0x50AC, symSize: 0x8 }
  - { offset: 0x9AE1D, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoWriter:]', symObjAddr: 0xD80, symBinAddr: 0x50B4, symSize: 0xC }
  - { offset: 0x9AE5E, size: 0x8, addend: 0x0, symName: '-[FLTCam videoIsDisconnected]', symObjAddr: 0xD8C, symBinAddr: 0x50C0, symSize: 0x8 }
  - { offset: 0x9AE95, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoIsDisconnected:]', symObjAddr: 0xD94, symBinAddr: 0x50C8, symSize: 0x8 }
  - { offset: 0x9AED0, size: 0x8, addend: 0x0, symName: '-[FLTCam audioIsDisconnected]', symObjAddr: 0xD9C, symBinAddr: 0x50D0, symSize: 0x8 }
  - { offset: 0x9AF07, size: 0x8, addend: 0x0, symName: '-[FLTCam setAudioIsDisconnected:]', symObjAddr: 0xDA4, symBinAddr: 0x50D8, symSize: 0x8 }
  - { offset: 0x9AF42, size: 0x8, addend: 0x0, symName: '-[FLTCam videoTimeOffset]', symObjAddr: 0xDAC, symBinAddr: 0x50E0, symSize: 0x14 }
  - { offset: 0x9AF77, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoTimeOffset:]', symObjAddr: 0xDC0, symBinAddr: 0x50F4, symSize: 0x14 }
  - { offset: 0x9AFB5, size: 0x8, addend: 0x0, symName: '-[FLTCam audioTimeOffset]', symObjAddr: 0xDD4, symBinAddr: 0x5108, symSize: 0x18 }
  - { offset: 0x9AFEA, size: 0x8, addend: 0x0, symName: '-[FLTCam setAudioTimeOffset:]', symObjAddr: 0xDEC, symBinAddr: 0x5120, symSize: 0x18 }
  - { offset: 0x9B028, size: 0x8, addend: 0x0, symName: '-[FLTCam videoWriterInput]', symObjAddr: 0xE04, symBinAddr: 0x5138, symSize: 0x8 }
  - { offset: 0x9B05F, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoWriterInput:]', symObjAddr: 0xE0C, symBinAddr: 0x5140, symSize: 0xC }
  - { offset: 0x9B0A0, size: 0x8, addend: 0x0, symName: '-[FLTCam audioWriterInput]', symObjAddr: 0xE18, symBinAddr: 0x514C, symSize: 0x8 }
  - { offset: 0x9B0D7, size: 0x8, addend: 0x0, symName: '-[FLTCam setAudioWriterInput:]', symObjAddr: 0xE20, symBinAddr: 0x5154, symSize: 0xC }
  - { offset: 0x9B118, size: 0x8, addend: 0x0, symName: '-[FLTCam videoAdaptor]', symObjAddr: 0xE2C, symBinAddr: 0x5160, symSize: 0xC }
  - { offset: 0x9B14F, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoAdaptor:]', symObjAddr: 0xE38, symBinAddr: 0x516C, symSize: 0x8 }
  - { offset: 0x9B18E, size: 0x8, addend: 0x0, symName: '-[FLTCam videoCaptureSession]', symObjAddr: 0xE40, symBinAddr: 0x5174, symSize: 0x8 }
  - { offset: 0x9B1C5, size: 0x8, addend: 0x0, symName: '-[FLTCam audioCaptureSession]', symObjAddr: 0xE48, symBinAddr: 0x517C, symSize: 0x8 }
  - { offset: 0x9B1FC, size: 0x8, addend: 0x0, symName: '-[FLTCam deviceOrientationProvider]', symObjAddr: 0xE50, symBinAddr: 0x5184, symSize: 0x8 }
  - { offset: 0x9B233, size: 0x8, addend: 0x0, symName: '-[FLTCam lockedCaptureOrientation]', symObjAddr: 0xE58, symBinAddr: 0x518C, symSize: 0x8 }
  - { offset: 0x9B26A, size: 0x8, addend: 0x0, symName: '-[FLTCam setLockedCaptureOrientation:]', symObjAddr: 0xE60, symBinAddr: 0x5194, symSize: 0x8 }
  - { offset: 0x9B2A7, size: 0x8, addend: 0x0, symName: '-[FLTCam deviceOrientation]', symObjAddr: 0xE68, symBinAddr: 0x519C, symSize: 0x8 }
  - { offset: 0x9B2DE, size: 0x8, addend: 0x0, symName: '-[FLTCam setDeviceOrientation:]', symObjAddr: 0xE70, symBinAddr: 0x51A4, symSize: 0x8 }
  - { offset: 0x9B31B, size: 0x8, addend: 0x0, symName: '-[FLTCam flashMode]', symObjAddr: 0xE78, symBinAddr: 0x51AC, symSize: 0x8 }
  - { offset: 0x9B352, size: 0x8, addend: 0x0, symName: '-[FLTCam setFlashMode:]', symObjAddr: 0xE80, symBinAddr: 0x51B4, symSize: 0x8 }
  - { offset: 0x9B38F, size: 0x8, addend: 0x0, symName: '-[FLTCam motionManager]', symObjAddr: 0xE88, symBinAddr: 0x51BC, symSize: 0x8 }
  - { offset: 0x9B3C6, size: 0x8, addend: 0x0, symName: '-[FLTCam setMotionManager:]', symObjAddr: 0xE90, symBinAddr: 0x51C4, symSize: 0xC }
  - { offset: 0x9B407, size: 0x8, addend: 0x0, symName: '-[FLTCam videoRecordingPath]', symObjAddr: 0xE9C, symBinAddr: 0x51D0, symSize: 0x8 }
  - { offset: 0x9B43E, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoRecordingPath:]', symObjAddr: 0xEA4, symBinAddr: 0x51D8, symSize: 0xC }
  - { offset: 0x9B47F, size: 0x8, addend: 0x0, symName: '-[FLTCam captureDeviceFactory]', symObjAddr: 0xEB0, symBinAddr: 0x51E4, symSize: 0x8 }
  - { offset: 0x9B4B6, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureDeviceFactory:]', symObjAddr: 0xEB8, symBinAddr: 0x51EC, symSize: 0x8 }
  - { offset: 0x9B4F5, size: 0x8, addend: 0x0, symName: '-[FLTCam captureVideoInput]', symObjAddr: 0xEC0, symBinAddr: 0x51F4, symSize: 0x8 }
  - { offset: 0x9B52C, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureVideoInput:]', symObjAddr: 0xEC8, symBinAddr: 0x51FC, symSize: 0xC }
  - { offset: 0x9B56D, size: 0x8, addend: 0x0, symName: '-[FLTCam captureDeviceInputFactory]', symObjAddr: 0xED4, symBinAddr: 0x5208, symSize: 0x8 }
  - { offset: 0x9B5A4, size: 0x8, addend: 0x0, symName: '-[FLTCam captureSessionQueue]', symObjAddr: 0xEDC, symBinAddr: 0x5210, symSize: 0x8 }
  - { offset: 0x9B5DB, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureSessionQueue:]', symObjAddr: 0xEE4, symBinAddr: 0x5218, symSize: 0xC }
  - { offset: 0x9B61C, size: 0x8, addend: 0x0, symName: '-[FLTCam assetWriterFactory]', symObjAddr: 0xEF0, symBinAddr: 0x5224, symSize: 0x8 }
  - { offset: 0x9B653, size: 0x8, addend: 0x0, symName: '-[FLTCam setAssetWriterFactory:]', symObjAddr: 0xEF8, symBinAddr: 0x522C, symSize: 0x8 }
  - { offset: 0x9B692, size: 0x8, addend: 0x0, symName: '-[FLTCam mediaSettingsAVWrapper]', symObjAddr: 0xF00, symBinAddr: 0x5234, symSize: 0x8 }
  - { offset: 0x9B6C9, size: 0x8, addend: 0x0, symName: '-[FLTCam mediaSettings]', symObjAddr: 0xF08, symBinAddr: 0x523C, symSize: 0x8 }
  - { offset: 0x9B700, size: 0x8, addend: 0x0, symName: '-[FLTCam inputPixelBufferAdaptorFactory]', symObjAddr: 0xF10, symBinAddr: 0x5244, symSize: 0x8 }
  - { offset: 0x9B737, size: 0x8, addend: 0x0, symName: '-[FLTCam setInputPixelBufferAdaptorFactory:]', symObjAddr: 0xF18, symBinAddr: 0x524C, symSize: 0x8 }
  - { offset: 0x9B776, size: 0x8, addend: 0x0, symName: '-[FLTCam isAudioSetup]', symObjAddr: 0xF20, symBinAddr: 0x5254, symSize: 0x8 }
  - { offset: 0x9B7AD, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsAudioSetup:]', symObjAddr: 0xF28, symBinAddr: 0x525C, symSize: 0x8 }
  - { offset: 0x9B7E8, size: 0x8, addend: 0x0, symName: '-[FLTCam audioCaptureDeviceFactory]', symObjAddr: 0xF30, symBinAddr: 0x5264, symSize: 0x8 }
  - { offset: 0x9B81F, size: 0x8, addend: 0x0, symName: '-[FLTCam setAudioCaptureDeviceFactory:]', symObjAddr: 0xF38, symBinAddr: 0x526C, symSize: 0x8 }
  - { offset: 0x9B85E, size: 0x8, addend: 0x0, symName: '-[FLTCam textureId]', symObjAddr: 0xF40, symBinAddr: 0x5274, symSize: 0x8 }
  - { offset: 0x9B895, size: 0x8, addend: 0x0, symName: '-[FLTCam captureSize]', symObjAddr: 0xF48, symBinAddr: 0x527C, symSize: 0x8 }
  - { offset: 0x9B8CA, size: 0x8, addend: 0x0, symName: '-[FLTCam assetWriterPixelBufferAdaptor]', symObjAddr: 0xF50, symBinAddr: 0x5284, symSize: 0x8 }
  - { offset: 0x9B901, size: 0x8, addend: 0x0, symName: '-[FLTCam setAssetWriterPixelBufferAdaptor:]', symObjAddr: 0xF58, symBinAddr: 0x528C, symSize: 0xC }
  - { offset: 0x9B942, size: 0x8, addend: 0x0, symName: '-[FLTCam videoOutput]', symObjAddr: 0xF64, symBinAddr: 0x5298, symSize: 0x8 }
  - { offset: 0x9B979, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoOutput:]', symObjAddr: 0xF6C, symBinAddr: 0x52A0, symSize: 0xC }
  - { offset: 0x9B9BA, size: 0x8, addend: 0x0, symName: '-[FLTCam videoDimensionsForFormat]', symObjAddr: 0xF78, symBinAddr: 0x52AC, symSize: 0x8 }
  - { offset: 0x9B9F1, size: 0x8, addend: 0x0, symName: '-[FLTCam setVideoDimensionsForFormat:]', symObjAddr: 0xF80, symBinAddr: 0x52B4, symSize: 0x8 }
  - { offset: 0x9BA30, size: 0x8, addend: 0x0, symName: '-[FLTCam captureVideoOutput]', symObjAddr: 0xF88, symBinAddr: 0x52BC, symSize: 0x8 }
  - { offset: 0x9BA67, size: 0x8, addend: 0x0, symName: '-[FLTCam setCaptureVideoOutput:]', symObjAddr: 0xF90, symBinAddr: 0x52C4, symSize: 0xC }
  - { offset: 0x9BAA8, size: 0x8, addend: 0x0, symName: '-[FLTCam capturePhotoOutput]', symObjAddr: 0xF9C, symBinAddr: 0x52D0, symSize: 0x8 }
  - { offset: 0x9BADF, size: 0x8, addend: 0x0, symName: '-[FLTCam setCapturePhotoOutput:]', symObjAddr: 0xFA4, symBinAddr: 0x52D8, symSize: 0xC }
  - { offset: 0x9BB20, size: 0x8, addend: 0x0, symName: '-[FLTCam isStreamingImages]', symObjAddr: 0xFB0, symBinAddr: 0x52E4, symSize: 0x8 }
  - { offset: 0x9BB57, size: 0x8, addend: 0x0, symName: '-[FLTCam setIsStreamingImages:]', symObjAddr: 0xFB8, symBinAddr: 0x52EC, symSize: 0x8 }
  - { offset: 0x9BB92, size: 0x8, addend: 0x0, symName: '-[FLTCam inProgressSavePhotoDelegates]', symObjAddr: 0xFC0, symBinAddr: 0x52F4, symSize: 0x8 }
  - { offset: 0x9BBC9, size: 0x8, addend: 0x0, symName: '-[FLTCam .cxx_destruct]', symObjAddr: 0xFC8, symBinAddr: 0x52FC, symSize: 0x168 }
  - { offset: 0x9BD42, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration initWithMediaSettings:mediaSettingsWrapper:captureDeviceFactory:audioCaptureDeviceFactory:captureSessionFactory:captureSessionQueue:captureDeviceInputFactory:initialCameraName:]', symObjAddr: 0x0, symBinAddr: 0x5464, symSize: 0x260 }
  - { offset: 0x9C1A8, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration initWithMediaSettings:mediaSettingsWrapper:captureDeviceFactory:audioCaptureDeviceFactory:captureSessionFactory:captureSessionQueue:captureDeviceInputFactory:initialCameraName:]', symObjAddr: 0x0, symBinAddr: 0x5464, symSize: 0x260 }
  - { offset: 0x9C287, size: 0x8, addend: 0x0, symName: '___199-[FLTCamConfiguration initWithMediaSettings:mediaSettingsWrapper:captureDeviceFactory:audioCaptureDeviceFactory:captureSessionFactory:captureSessionQueue:captureDeviceInputFactory:initialCameraName:]_block_invoke', symObjAddr: 0x260, symBinAddr: 0x56C4, symSize: 0x18 }
  - { offset: 0x9C2E3, size: 0x8, addend: 0x0, symName: '___199-[FLTCamConfiguration initWithMediaSettings:mediaSettingsWrapper:captureDeviceFactory:audioCaptureDeviceFactory:captureSessionFactory:captureSessionQueue:captureDeviceInputFactory:initialCameraName:]_block_invoke_2', symObjAddr: 0x278, symBinAddr: 0x56DC, symSize: 0x78 }
  - { offset: 0x9C33E, size: 0x8, addend: 0x0, symName: '___199-[FLTCamConfiguration initWithMediaSettings:mediaSettingsWrapper:captureDeviceFactory:audioCaptureDeviceFactory:captureSessionFactory:captureSessionQueue:captureDeviceInputFactory:initialCameraName:]_block_invoke_3', symObjAddr: 0x2F0, symBinAddr: 0x5754, symSize: 0xC4 }
  - { offset: 0x9C389, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration deviceOrientationProvider]', symObjAddr: 0x3B4, symBinAddr: 0x5818, symSize: 0x8 }
  - { offset: 0x9C3C0, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setDeviceOrientationProvider:]', symObjAddr: 0x3BC, symBinAddr: 0x5820, symSize: 0xC }
  - { offset: 0x9C401, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration captureSessionQueue]', symObjAddr: 0x3C8, symBinAddr: 0x582C, symSize: 0x8 }
  - { offset: 0x9C438, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setCaptureSessionQueue:]', symObjAddr: 0x3D0, symBinAddr: 0x5834, symSize: 0xC }
  - { offset: 0x9C479, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration mediaSettings]', symObjAddr: 0x3DC, symBinAddr: 0x5840, symSize: 0x8 }
  - { offset: 0x9C4B0, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setMediaSettings:]', symObjAddr: 0x3E4, symBinAddr: 0x5848, symSize: 0xC }
  - { offset: 0x9C4F1, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration mediaSettingsWrapper]', symObjAddr: 0x3F0, symBinAddr: 0x5854, symSize: 0x8 }
  - { offset: 0x9C528, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setMediaSettingsWrapper:]', symObjAddr: 0x3F8, symBinAddr: 0x585C, symSize: 0xC }
  - { offset: 0x9C569, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration captureDeviceFactory]', symObjAddr: 0x404, symBinAddr: 0x5868, symSize: 0x8 }
  - { offset: 0x9C5A0, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setCaptureDeviceFactory:]', symObjAddr: 0x40C, symBinAddr: 0x5870, symSize: 0x8 }
  - { offset: 0x9C5DF, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration audioCaptureDeviceFactory]', symObjAddr: 0x414, symBinAddr: 0x5878, symSize: 0x8 }
  - { offset: 0x9C616, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setAudioCaptureDeviceFactory:]', symObjAddr: 0x41C, symBinAddr: 0x5880, symSize: 0x8 }
  - { offset: 0x9C655, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration videoDimensionsForFormat]', symObjAddr: 0x424, symBinAddr: 0x5888, symSize: 0x8 }
  - { offset: 0x9C68C, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setVideoDimensionsForFormat:]', symObjAddr: 0x42C, symBinAddr: 0x5890, symSize: 0x8 }
  - { offset: 0x9C6CB, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration orientation]', symObjAddr: 0x434, symBinAddr: 0x5898, symSize: 0x8 }
  - { offset: 0x9C702, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setOrientation:]', symObjAddr: 0x43C, symBinAddr: 0x58A0, symSize: 0x8 }
  - { offset: 0x9C73F, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration videoCaptureSession]', symObjAddr: 0x444, symBinAddr: 0x58A8, symSize: 0x8 }
  - { offset: 0x9C776, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setVideoCaptureSession:]', symObjAddr: 0x44C, symBinAddr: 0x58B0, symSize: 0xC }
  - { offset: 0x9C7B7, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration audioCaptureSession]', symObjAddr: 0x458, symBinAddr: 0x58BC, symSize: 0x8 }
  - { offset: 0x9C7EE, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setAudioCaptureSession:]', symObjAddr: 0x460, symBinAddr: 0x58C4, symSize: 0xC }
  - { offset: 0x9C82F, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration captureDeviceInputFactory]', symObjAddr: 0x46C, symBinAddr: 0x58D0, symSize: 0x8 }
  - { offset: 0x9C866, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setCaptureDeviceInputFactory:]', symObjAddr: 0x474, symBinAddr: 0x58D8, symSize: 0xC }
  - { offset: 0x9C8A7, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration assetWriterFactory]', symObjAddr: 0x480, symBinAddr: 0x58E4, symSize: 0x8 }
  - { offset: 0x9C8DE, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setAssetWriterFactory:]', symObjAddr: 0x488, symBinAddr: 0x58EC, symSize: 0x8 }
  - { offset: 0x9C91D, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration inputPixelBufferAdaptorFactory]', symObjAddr: 0x490, symBinAddr: 0x58F4, symSize: 0x8 }
  - { offset: 0x9C954, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setInputPixelBufferAdaptorFactory:]', symObjAddr: 0x498, symBinAddr: 0x58FC, symSize: 0x8 }
  - { offset: 0x9C993, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration initialCameraName]', symObjAddr: 0x4A0, symBinAddr: 0x5904, symSize: 0x8 }
  - { offset: 0x9C9CA, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration setInitialCameraName:]', symObjAddr: 0x4A8, symBinAddr: 0x590C, symSize: 0x8 }
  - { offset: 0x9CA09, size: 0x8, addend: 0x0, symName: '-[FLTCamConfiguration .cxx_destruct]', symObjAddr: 0x4B0, symBinAddr: 0x5914, symSize: 0xB4 }
  - { offset: 0x9CC36, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCameraDeviceDiscoverer discoverySessionWithDeviceTypes:mediaType:position:]', symObjAddr: 0x0, symBinAddr: 0x59C8, symSize: 0x184 }
  - { offset: 0x9CC55, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCameraDeviceDiscoverer discoverySessionWithDeviceTypes:mediaType:position:]', symObjAddr: 0x0, symBinAddr: 0x59C8, symSize: 0x184 }
  - { offset: 0x9CD95, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager initWithPermissionService:]', symObjAddr: 0x0, symBinAddr: 0x5B4C, symSize: 0x90 }
  - { offset: 0x9CDF7, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager initWithPermissionService:]', symObjAddr: 0x0, symBinAddr: 0x5B4C, symSize: 0x90 }
  - { offset: 0x9CE3E, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager requestAudioPermissionWithCompletionHandler:]', symObjAddr: 0x90, symBinAddr: 0x5BDC, symSize: 0xC }
  - { offset: 0x9CE7F, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager requestCameraPermissionWithCompletionHandler:]', symObjAddr: 0x9C, symBinAddr: 0x5BE8, symSize: 0xC }
  - { offset: 0x9CEC0, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager requestPermissionForAudio:handler:]', symObjAddr: 0xA8, symBinAddr: 0x5BF4, symSize: 0x19C }
  - { offset: 0x9CF86, size: 0x8, addend: 0x0, symName: '___64-[FLTCameraPermissionManager requestPermissionForAudio:handler:]_block_invoke', symObjAddr: 0x244, symBinAddr: 0x5D90, symSize: 0x98 }
  - { offset: 0x9D027, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0x2DC, symBinAddr: 0x5E28, symSize: 0x10 }
  - { offset: 0x9D050, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x2EC, symBinAddr: 0x5E38, symSize: 0x8 }
  - { offset: 0x9D06F, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager permissionService]', symObjAddr: 0x2F4, symBinAddr: 0x5E40, symSize: 0x8 }
  - { offset: 0x9D0A6, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager setPermissionService:]', symObjAddr: 0x2FC, symBinAddr: 0x5E48, symSize: 0xC }
  - { offset: 0x9D0E7, size: 0x8, addend: 0x0, symName: '-[FLTCameraPermissionManager .cxx_destruct]', symObjAddr: 0x308, symBinAddr: 0x5E54, symSize: 0xC }
  - { offset: 0x9D2C6, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper lockDevice:error:]', symObjAddr: 0x0, symBinAddr: 0x5E60, symSize: 0xC }
  - { offset: 0x9D2E5, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper lockDevice:error:]', symObjAddr: 0x0, symBinAddr: 0x5E60, symSize: 0xC }
  - { offset: 0x9D338, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper unlockDevice:]', symObjAddr: 0xC, symBinAddr: 0x5E6C, symSize: 0x8 }
  - { offset: 0x9D377, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper beginConfigurationForSession:]', symObjAddr: 0x14, symBinAddr: 0x5E74, symSize: 0x8 }
  - { offset: 0x9D3B6, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper commitConfigurationForSession:]', symObjAddr: 0x1C, symBinAddr: 0x5E7C, symSize: 0x8 }
  - { offset: 0x9D3F5, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper setMinFrameDuration:onDevice:]', symObjAddr: 0x24, symBinAddr: 0x5E84, symSize: 0x34 }
  - { offset: 0x9D444, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper setMaxFrameDuration:onDevice:]', symObjAddr: 0x58, symBinAddr: 0x5EB8, symSize: 0x34 }
  - { offset: 0x9D493, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper assetWriterAudioInputWithOutputSettings:]', symObjAddr: 0x8C, symBinAddr: 0x5EEC, symSize: 0x8C }
  - { offset: 0x9D4D6, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper assetWriterVideoInputWithOutputSettings:]', symObjAddr: 0x118, symBinAddr: 0x5F78, symSize: 0x8C }
  - { offset: 0x9D519, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper addInput:toAssetWriter:]', symObjAddr: 0x1A4, symBinAddr: 0x6004, symSize: 0x54 }
  - { offset: 0x9D568, size: 0x8, addend: 0x0, symName: '-[FLTCamMediaSettingsAVWrapper recommendedVideoSettingsForAssetWriterWithFileType:forOutput:]', symObjAddr: 0x1F8, symBinAddr: 0x6058, symSize: 0x6C }
  - { offset: 0x9D62F, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection initWithConnection:]', symObjAddr: 0x0, symBinAddr: 0x60C4, symSize: 0x78 }
  - { offset: 0x9D66D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection initWithConnection:]', symObjAddr: 0x0, symBinAddr: 0x60C4, symSize: 0x78 }
  - { offset: 0x9D6B4, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection isVideoMirroringSupported]', symObjAddr: 0x78, symBinAddr: 0x613C, symSize: 0x3C }
  - { offset: 0x9D6EB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection isVideoOrientationSupported]', symObjAddr: 0xB4, symBinAddr: 0x6178, symSize: 0x3C }
  - { offset: 0x9D722, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection setVideoMirrored:]', symObjAddr: 0xF0, symBinAddr: 0x61B4, symSize: 0x38 }
  - { offset: 0x9D765, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection isVideoMirrored]', symObjAddr: 0x128, symBinAddr: 0x61EC, symSize: 0x3C }
  - { offset: 0x9D79C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection setVideoOrientation:]', symObjAddr: 0x164, symBinAddr: 0x6228, symSize: 0x38 }
  - { offset: 0x9D7DF, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection videoOrientation]', symObjAddr: 0x19C, symBinAddr: 0x6260, symSize: 0x3C }
  - { offset: 0x9D816, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection inputPorts]', symObjAddr: 0x1D8, symBinAddr: 0x629C, symSize: 0x44 }
  - { offset: 0x9D84D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection connection]', symObjAddr: 0x21C, symBinAddr: 0x62E0, symSize: 0x8 }
  - { offset: 0x9D884, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection setConnection:]', symObjAddr: 0x224, symBinAddr: 0x62E8, symSize: 0xC }
  - { offset: 0x9D8C5, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureConnection .cxx_destruct]', symObjAddr: 0x230, symBinAddr: 0x62F4, symSize: 0xC }
  - { offset: 0x9D988, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice initWithDevice:]', symObjAddr: 0x0, symBinAddr: 0x6300, symSize: 0x78 }
  - { offset: 0x9DA07, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice initWithDevice:]', symObjAddr: 0x0, symBinAddr: 0x6300, symSize: 0x78 }
  - { offset: 0x9DA4E, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice uniqueID]', symObjAddr: 0x78, symBinAddr: 0x6378, symSize: 0x44 }
  - { offset: 0x9DA85, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice position]', symObjAddr: 0xBC, symBinAddr: 0x63BC, symSize: 0x3C }
  - { offset: 0x9DABC, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice activeFormat]', symObjAddr: 0xF8, symBinAddr: 0x63F8, symSize: 0x7C }
  - { offset: 0x9DAF3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice formats]', symObjAddr: 0x174, symBinAddr: 0x6474, symSize: 0x19C }
  - { offset: 0x9DB51, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setActiveFormat:]', symObjAddr: 0x310, symBinAddr: 0x6610, symSize: 0x58 }
  - { offset: 0x9DB94, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice hasFlash]', symObjAddr: 0x368, symBinAddr: 0x6668, symSize: 0x3C }
  - { offset: 0x9DBCB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice hasTorch]', symObjAddr: 0x3A4, symBinAddr: 0x66A4, symSize: 0x3C }
  - { offset: 0x9DC02, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isTorchAvailable]', symObjAddr: 0x3E0, symBinAddr: 0x66E0, symSize: 0x3C }
  - { offset: 0x9DC39, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice torchMode]', symObjAddr: 0x41C, symBinAddr: 0x671C, symSize: 0x3C }
  - { offset: 0x9DC70, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setTorchMode:]', symObjAddr: 0x458, symBinAddr: 0x6758, symSize: 0x38 }
  - { offset: 0x9DCB3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isFlashModeSupported:]', symObjAddr: 0x490, symBinAddr: 0x6790, symSize: 0x44 }
  - { offset: 0x9DCFA, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isFocusPointOfInterestSupported]', symObjAddr: 0x4D4, symBinAddr: 0x67D4, symSize: 0x3C }
  - { offset: 0x9DD31, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isFocusModeSupported:]', symObjAddr: 0x510, symBinAddr: 0x6810, symSize: 0x44 }
  - { offset: 0x9DD78, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setFocusMode:]', symObjAddr: 0x554, symBinAddr: 0x6854, symSize: 0x38 }
  - { offset: 0x9DDBB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setFocusPointOfInterest:]', symObjAddr: 0x58C, symBinAddr: 0x688C, symSize: 0x48 }
  - { offset: 0x9DDFA, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isExposurePointOfInterestSupported]', symObjAddr: 0x5D4, symBinAddr: 0x68D4, symSize: 0x3C }
  - { offset: 0x9DE31, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setExposureMode:]', symObjAddr: 0x610, symBinAddr: 0x6910, symSize: 0x38 }
  - { offset: 0x9DE74, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setExposurePointOfInterest:]', symObjAddr: 0x648, symBinAddr: 0x6948, symSize: 0x48 }
  - { offset: 0x9DEB3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice minExposureTargetBias]', symObjAddr: 0x690, symBinAddr: 0x6990, symSize: 0x44 }
  - { offset: 0x9DEEA, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice maxExposureTargetBias]', symObjAddr: 0x6D4, symBinAddr: 0x69D4, symSize: 0x44 }
  - { offset: 0x9DF21, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setExposureTargetBias:completionHandler:]', symObjAddr: 0x718, symBinAddr: 0x6A18, symSize: 0x60 }
  - { offset: 0x9DF74, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice isExposureModeSupported:]', symObjAddr: 0x778, symBinAddr: 0x6A78, symSize: 0x44 }
  - { offset: 0x9DFBB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice maxAvailableVideoZoomFactor]', symObjAddr: 0x7BC, symBinAddr: 0x6ABC, symSize: 0x44 }
  - { offset: 0x9DFF2, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice minAvailableVideoZoomFactor]', symObjAddr: 0x800, symBinAddr: 0x6B00, symSize: 0x44 }
  - { offset: 0x9E029, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice videoZoomFactor]', symObjAddr: 0x844, symBinAddr: 0x6B44, symSize: 0x44 }
  - { offset: 0x9E060, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setVideoZoomFactor:]', symObjAddr: 0x888, symBinAddr: 0x6B88, symSize: 0x40 }
  - { offset: 0x9E0A3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice lensAperture]', symObjAddr: 0x8C8, symBinAddr: 0x6BC8, symSize: 0x44 }
  - { offset: 0x9E0DA, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice exposureDuration]', symObjAddr: 0x90C, symBinAddr: 0x6C0C, symSize: 0x4C }
  - { offset: 0x9E111, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice ISO]', symObjAddr: 0x958, symBinAddr: 0x6C58, symSize: 0x44 }
  - { offset: 0x9E148, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice lockForConfiguration:]', symObjAddr: 0x99C, symBinAddr: 0x6C9C, symSize: 0x44 }
  - { offset: 0x9E18F, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice unlockForConfiguration]', symObjAddr: 0x9E0, symBinAddr: 0x6CE0, symSize: 0x30 }
  - { offset: 0x9E1C2, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice activeVideoMinFrameDuration]', symObjAddr: 0xA10, symBinAddr: 0x6D10, symSize: 0x4C }
  - { offset: 0x9E1F9, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setActiveVideoMinFrameDuration:]', symObjAddr: 0xA5C, symBinAddr: 0x6D5C, symSize: 0x50 }
  - { offset: 0x9E23C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice activeVideoMaxFrameDuration]', symObjAddr: 0xAAC, symBinAddr: 0x6DAC, symSize: 0x4C }
  - { offset: 0x9E273, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setActiveVideoMaxFrameDuration:]', symObjAddr: 0xAF8, symBinAddr: 0x6DF8, symSize: 0x50 }
  - { offset: 0x9E2B6, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice device]', symObjAddr: 0xB48, symBinAddr: 0x6E48, symSize: 0x8 }
  - { offset: 0x9E2ED, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice setDevice:]', symObjAddr: 0xB50, symBinAddr: 0x6E50, symSize: 0xC }
  - { offset: 0x9E32E, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDevice .cxx_destruct]', symObjAddr: 0xB5C, symBinAddr: 0x6E5C, symSize: 0xC }
  - { offset: 0x9E361, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureInput initWithInput:]', symObjAddr: 0xB68, symBinAddr: 0x6E68, symSize: 0x78 }
  - { offset: 0x9E3A8, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureInput input]', symObjAddr: 0xBE0, symBinAddr: 0x6EE0, symSize: 0x8 }
  - { offset: 0x9E3DF, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureInput ports]', symObjAddr: 0xBE8, symBinAddr: 0x6EE8, symSize: 0x44 }
  - { offset: 0x9E416, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureInput setInput:]', symObjAddr: 0xC2C, symBinAddr: 0x6F2C, symSize: 0xC }
  - { offset: 0x9E457, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureInput .cxx_destruct]', symObjAddr: 0xC38, symBinAddr: 0x6F38, symSize: 0xC }
  - { offset: 0x9E48A, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceInputFactory deviceInputWithDevice:error:]', symObjAddr: 0xC44, symBinAddr: 0x6F44, symSize: 0xB0 }
  - { offset: 0x9E644, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange initWithRange:]', symObjAddr: 0x0, symBinAddr: 0x6FF4, symSize: 0x78 }
  - { offset: 0x9E6B2, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange initWithRange:]', symObjAddr: 0x0, symBinAddr: 0x6FF4, symSize: 0x78 }
  - { offset: 0x9E6F9, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange minFrameRate]', symObjAddr: 0x78, symBinAddr: 0x706C, symSize: 0x1C }
  - { offset: 0x9E730, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange maxFrameRate]', symObjAddr: 0x94, symBinAddr: 0x7088, symSize: 0x1C }
  - { offset: 0x9E767, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange range]', symObjAddr: 0xB0, symBinAddr: 0x70A4, symSize: 0x8 }
  - { offset: 0x9E79E, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange setRange:]', symObjAddr: 0xB8, symBinAddr: 0x70AC, symSize: 0xC }
  - { offset: 0x9E7DF, size: 0x8, addend: 0x0, symName: '-[FLTDefaultFrameRateRange .cxx_destruct]', symObjAddr: 0xC4, symBinAddr: 0x70B8, symSize: 0xC }
  - { offset: 0x9E812, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat initWithFormat:]', symObjAddr: 0xD0, symBinAddr: 0x70C4, symSize: 0x78 }
  - { offset: 0x9E859, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat formatDescription]', symObjAddr: 0x148, symBinAddr: 0x713C, symSize: 0x8 }
  - { offset: 0x9E890, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat videoSupportedFrameRateRanges]', symObjAddr: 0x150, symBinAddr: 0x7144, symSize: 0x16C }
  - { offset: 0x9E905, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat format]', symObjAddr: 0x2BC, symBinAddr: 0x72B0, symSize: 0x8 }
  - { offset: 0x9E93C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat setFormat:]', symObjAddr: 0x2C4, symBinAddr: 0x72B8, symSize: 0xC }
  - { offset: 0x9E97D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureDeviceFormat .cxx_destruct]', symObjAddr: 0x2D0, symBinAddr: 0x72C4, symSize: 0xC }
  - { offset: 0x9EA62, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput initWithPhotoOutput:]', symObjAddr: 0x0, symBinAddr: 0x72D0, symSize: 0x78 }
  - { offset: 0x9EAA0, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput initWithPhotoOutput:]', symObjAddr: 0x0, symBinAddr: 0x72D0, symSize: 0x78 }
  - { offset: 0x9EAE7, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput availablePhotoCodecTypes]', symObjAddr: 0x78, symBinAddr: 0x7348, symSize: 0x44 }
  - { offset: 0x9EB1E, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput highResolutionCaptureEnabled]', symObjAddr: 0xBC, symBinAddr: 0x738C, symSize: 0x3C }
  - { offset: 0x9EB55, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput setHighResolutionCaptureEnabled:]', symObjAddr: 0xF8, symBinAddr: 0x73C8, symSize: 0x38 }
  - { offset: 0x9EB98, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput capturePhotoWithSettings:delegate:]', symObjAddr: 0x130, symBinAddr: 0x7400, symSize: 0x74 }
  - { offset: 0x9EBEB, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput connectionWithMediaType:]', symObjAddr: 0x1A4, symBinAddr: 0x7474, symSize: 0x98 }
  - { offset: 0x9EC32, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput supportedFlashModes]', symObjAddr: 0x23C, symBinAddr: 0x750C, symSize: 0x44 }
  - { offset: 0x9EC69, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput avOutput]', symObjAddr: 0x280, symBinAddr: 0x7550, symSize: 0x8 }
  - { offset: 0x9ECA0, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput setAvOutput:]', symObjAddr: 0x288, symBinAddr: 0x7558, symSize: 0xC }
  - { offset: 0x9ECE1, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCapturePhotoOutput .cxx_destruct]', symObjAddr: 0x294, symBinAddr: 0x7564, symSize: 0xC }
  - { offset: 0x9EDB5, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession initWithCaptureSession:]', symObjAddr: 0x0, symBinAddr: 0x7570, symSize: 0x78 }
  - { offset: 0x9EDF3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession initWithCaptureSession:]', symObjAddr: 0x0, symBinAddr: 0x7570, symSize: 0x78 }
  - { offset: 0x9EE3A, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession beginConfiguration]', symObjAddr: 0x78, symBinAddr: 0x75E8, symSize: 0x8 }
  - { offset: 0x9EE6D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession commitConfiguration]', symObjAddr: 0x80, symBinAddr: 0x75F0, symSize: 0x8 }
  - { offset: 0x9EEA0, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession startRunning]', symObjAddr: 0x88, symBinAddr: 0x75F8, symSize: 0x8 }
  - { offset: 0x9EED3, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession stopRunning]', symObjAddr: 0x90, symBinAddr: 0x7600, symSize: 0x8 }
  - { offset: 0x9EF06, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession automaticallyConfiguresApplicationAudioSession]', symObjAddr: 0x98, symBinAddr: 0x7608, symSize: 0x8 }
  - { offset: 0x9EF3D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession setAutomaticallyConfiguresApplicationAudioSession:]', symObjAddr: 0xA0, symBinAddr: 0x7610, symSize: 0x8 }
  - { offset: 0x9EF83, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession canSetSessionPreset:]', symObjAddr: 0xA8, symBinAddr: 0x7618, symSize: 0x8 }
  - { offset: 0x9EFC8, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession addInputWithNoConnections:]', symObjAddr: 0xB0, symBinAddr: 0x7620, symSize: 0x40 }
  - { offset: 0x9F00B, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession addOutputWithNoConnections:]', symObjAddr: 0xF0, symBinAddr: 0x7660, symSize: 0x8 }
  - { offset: 0x9F04C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession addConnection:]', symObjAddr: 0xF8, symBinAddr: 0x7668, symSize: 0x8 }
  - { offset: 0x9F08D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession addOutput:]', symObjAddr: 0x100, symBinAddr: 0x7670, symSize: 0x8 }
  - { offset: 0x9F0CE, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession removeInput:]', symObjAddr: 0x108, symBinAddr: 0x7678, symSize: 0x40 }
  - { offset: 0x9F111, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession removeOutput:]', symObjAddr: 0x148, symBinAddr: 0x76B8, symSize: 0x8 }
  - { offset: 0x9F152, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession setSessionPreset:]', symObjAddr: 0x150, symBinAddr: 0x76C0, symSize: 0x8 }
  - { offset: 0x9F193, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession sessionPreset]', symObjAddr: 0x158, symBinAddr: 0x76C8, symSize: 0x8 }
  - { offset: 0x9F1CA, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession inputs]', symObjAddr: 0x160, symBinAddr: 0x76D0, symSize: 0x8 }
  - { offset: 0x9F201, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession outputs]', symObjAddr: 0x168, symBinAddr: 0x76D8, symSize: 0x8 }
  - { offset: 0x9F238, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession canAddInput:]', symObjAddr: 0x170, symBinAddr: 0x76E0, symSize: 0x4C }
  - { offset: 0x9F27F, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession canAddOutput:]', symObjAddr: 0x1BC, symBinAddr: 0x772C, symSize: 0x8 }
  - { offset: 0x9F2C4, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession canAddConnection:]', symObjAddr: 0x1C4, symBinAddr: 0x7734, symSize: 0x8 }
  - { offset: 0x9F309, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession addInput:]', symObjAddr: 0x1CC, symBinAddr: 0x773C, symSize: 0x40 }
  - { offset: 0x9F34C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession captureSession]', symObjAddr: 0x20C, symBinAddr: 0x777C, symSize: 0x8 }
  - { offset: 0x9F383, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession setCaptureSession:]', symObjAddr: 0x214, symBinAddr: 0x7784, symSize: 0xC }
  - { offset: 0x9F3C4, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureSession .cxx_destruct]', symObjAddr: 0x220, symBinAddr: 0x7790, symSize: 0xC }
  - { offset: 0x9F4B2, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput initWithCaptureVideoOutput:]', symObjAddr: 0x0, symBinAddr: 0x779C, symSize: 0x78 }
  - { offset: 0x9F4F0, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput initWithCaptureVideoOutput:]', symObjAddr: 0x0, symBinAddr: 0x779C, symSize: 0x78 }
  - { offset: 0x9F537, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput alwaysDiscardsLateVideoFrames]', symObjAddr: 0x78, symBinAddr: 0x7814, symSize: 0x3C }
  - { offset: 0x9F56E, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput setAlwaysDiscardsLateVideoFrames:]', symObjAddr: 0xB4, symBinAddr: 0x7850, symSize: 0x38 }
  - { offset: 0x9F5B1, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput videoSettings]', symObjAddr: 0xEC, symBinAddr: 0x7888, symSize: 0x44 }
  - { offset: 0x9F5E8, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput setVideoSettings:]', symObjAddr: 0x130, symBinAddr: 0x78CC, symSize: 0x50 }
  - { offset: 0x9F62B, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput connectionWithMediaType:]', symObjAddr: 0x180, symBinAddr: 0x791C, symSize: 0x98 }
  - { offset: 0x9F672, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput setSampleBufferDelegate:queue:]', symObjAddr: 0x218, symBinAddr: 0x79B4, symSize: 0x74 }
  - { offset: 0x9F6C5, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput avOutput]', symObjAddr: 0x28C, symBinAddr: 0x7A28, symSize: 0x8 }
  - { offset: 0x9F6FC, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput setAvOutput:]', symObjAddr: 0x294, symBinAddr: 0x7A30, symSize: 0xC }
  - { offset: 0x9F73D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultCaptureVideoDataOutput .cxx_destruct]', symObjAddr: 0x2A0, symBinAddr: 0x7A3C, symSize: 0xC }
  - { offset: 0x9F80C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultDeviceOrientationProvider orientation]', symObjAddr: 0x0, symBinAddr: 0x7A48, symSize: 0x44 }
  - { offset: 0x9F82B, size: 0x8, addend: 0x0, symName: '-[FLTDefaultDeviceOrientationProvider orientation]', symObjAddr: 0x0, symBinAddr: 0x7A48, symSize: 0x44 }
  - { offset: 0x9F8B0, size: 0x8, addend: 0x0, symName: _FLTBestFrameRateForFormat, symObjAddr: 0x0, symBinAddr: 0x7A8C, symSize: 0x150 }
  - { offset: 0x9F8F6, size: 0x8, addend: 0x0, symName: _FLTBestFrameRateForFormat, symObjAddr: 0x0, symBinAddr: 0x7A8C, symSize: 0x150 }
  - { offset: 0x9F9F4, size: 0x8, addend: 0x0, symName: _FLTSelectBestFormatForRequestedFrameRate, symObjAddr: 0x150, symBinAddr: 0x7BDC, symSize: 0x2E4 }
  - { offset: 0x9FCF6, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler initWithCaptureSessionQueue:]', symObjAddr: 0x0, symBinAddr: 0x7EC0, symSize: 0x60 }
  - { offset: 0x9FD81, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler initWithCaptureSessionQueue:]', symObjAddr: 0x0, symBinAddr: 0x7EC0, symSize: 0x60 }
  - { offset: 0x9FDC8, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler onCancelWithArguments:]', symObjAddr: 0x60, symBinAddr: 0x7F20, symSize: 0xD8 }
  - { offset: 0x9FE55, size: 0x8, addend: 0x0, symName: '___47-[FLTImageStreamHandler onCancelWithArguments:]_block_invoke', symObjAddr: 0x138, symBinAddr: 0x7FF8, symSize: 0x30 }
  - { offset: 0x9FE90, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32w, symObjAddr: 0x168, symBinAddr: 0x8028, symSize: 0xC }
  - { offset: 0x9FEB9, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32w, symObjAddr: 0x174, symBinAddr: 0x8034, symSize: 0x8 }
  - { offset: 0x9FED8, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler onListenWithArguments:eventSink:]', symObjAddr: 0x17C, symBinAddr: 0x803C, symSize: 0x108 }
  - { offset: 0x9FF5C, size: 0x8, addend: 0x0, symName: '___57-[FLTImageStreamHandler onListenWithArguments:eventSink:]_block_invoke', symObjAddr: 0x284, symBinAddr: 0x8144, symSize: 0x34 }
  - { offset: 0x9FFA7, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b40w, symObjAddr: 0x2B8, symBinAddr: 0x8178, symSize: 0x38 }
  - { offset: 0x9FFD0, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40w, symObjAddr: 0x2F0, symBinAddr: 0x81B0, symSize: 0x28 }
  - { offset: 0x9FFEF, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler captureSessionQueue]', symObjAddr: 0x318, symBinAddr: 0x81D8, symSize: 0x8 }
  - { offset: 0xA0026, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler setCaptureSessionQueue:]', symObjAddr: 0x320, symBinAddr: 0x81E0, symSize: 0xC }
  - { offset: 0xA0067, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler eventSink]', symObjAddr: 0x32C, symBinAddr: 0x81EC, symSize: 0xC }
  - { offset: 0xA009E, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler setEventSink:]', symObjAddr: 0x338, symBinAddr: 0x81F8, symSize: 0x8 }
  - { offset: 0xA00DD, size: 0x8, addend: 0x0, symName: '-[FLTImageStreamHandler .cxx_destruct]', symObjAddr: 0x340, symBinAddr: 0x8200, symSize: 0x30 }
  - { offset: 0xA026D, size: 0x8, addend: 0x0, symName: '-[FLTDefaultPermissionService authorizationStatusForMediaType:]', symObjAddr: 0x0, symBinAddr: 0x8230, symSize: 0xC }
  - { offset: 0xA028C, size: 0x8, addend: 0x0, symName: '-[FLTDefaultPermissionService authorizationStatusForMediaType:]', symObjAddr: 0x0, symBinAddr: 0x8230, symSize: 0xC }
  - { offset: 0xA02CD, size: 0x8, addend: 0x0, symName: '-[FLTDefaultPermissionService requestAccessForMediaType:completionHandler:]', symObjAddr: 0xC, symBinAddr: 0x823C, symSize: 0xC }
  - { offset: 0xA03EE, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate initWithPath:ioQueue:completionHandler:]', symObjAddr: 0x0, symBinAddr: 0x8248, symSize: 0xF0 }
  - { offset: 0xA050A, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate initWithPath:ioQueue:completionHandler:]', symObjAddr: 0x0, symBinAddr: 0x8248, symSize: 0xF0 }
  - { offset: 0xA0571, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate handlePhotoCaptureResultWithError:photoDataProvider:]', symObjAddr: 0xF0, symBinAddr: 0x8338, symSize: 0x13C }
  - { offset: 0xA0624, size: 0x8, addend: 0x0, symName: '___76-[FLTSavePhotoDelegate handlePhotoCaptureResultWithError:photoDataProvider:]_block_invoke', symObjAddr: 0x22C, symBinAddr: 0x8474, symSize: 0x124 }
  - { offset: 0xA0710, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b48w, symObjAddr: 0x350, symBinAddr: 0x8598, symSize: 0x40 }
  - { offset: 0xA0739, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48w, symObjAddr: 0x390, symBinAddr: 0x85D8, symSize: 0x30 }
  - { offset: 0xA0758, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate captureOutput:didFinishProcessingPhoto:error:]', symObjAddr: 0x3C0, symBinAddr: 0x8608, symSize: 0x90 }
  - { offset: 0xA07B7, size: 0x8, addend: 0x0, symName: '___69-[FLTSavePhotoDelegate captureOutput:didFinishProcessingPhoto:error:]_block_invoke', symObjAddr: 0x450, symBinAddr: 0x8698, symSize: 0x8 }
  - { offset: 0xA07F6, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x458, symBinAddr: 0x86A0, symSize: 0x8 }
  - { offset: 0xA081D, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate filePath]', symObjAddr: 0x468, symBinAddr: 0x86A8, symSize: 0x4 }
  - { offset: 0xA0852, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate path]', symObjAddr: 0x46C, symBinAddr: 0x86AC, symSize: 0x8 }
  - { offset: 0xA0889, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate ioQueue]', symObjAddr: 0x474, symBinAddr: 0x86B4, symSize: 0x8 }
  - { offset: 0xA08C0, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate completionHandler]', symObjAddr: 0x47C, symBinAddr: 0x86BC, symSize: 0x8 }
  - { offset: 0xA08F7, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate setFilePath:]', symObjAddr: 0x484, symBinAddr: 0x86C4, symSize: 0xC }
  - { offset: 0xA0938, size: 0x8, addend: 0x0, symName: '-[FLTSavePhotoDelegate .cxx_destruct]', symObjAddr: 0x490, symBinAddr: 0x86D0, symSize: 0x48 }
  - { offset: 0xA0B95, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel initWithEventChannel:]', symObjAddr: 0x0, symBinAddr: 0x8718, symSize: 0x78 }
  - { offset: 0xA0BF7, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel initWithEventChannel:]', symObjAddr: 0x0, symBinAddr: 0x8718, symSize: 0x78 }
  - { offset: 0xA0C3E, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel setStreamHandler:completion:]', symObjAddr: 0x78, symBinAddr: 0x8790, symSize: 0xB4 }
  - { offset: 0xA0D25, size: 0x8, addend: 0x0, symName: '___57-[FLTThreadSafeEventChannel setStreamHandler:completion:]_block_invoke', symObjAddr: 0x12C, symBinAddr: 0x8844, symSize: 0x48 }
  - { offset: 0xA0D90, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b, symObjAddr: 0x174, symBinAddr: 0x888C, symSize: 0x3C }
  - { offset: 0xA0DB9, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x1B0, symBinAddr: 0x88C8, symSize: 0x30 }
  - { offset: 0xA0DD8, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel channel]', symObjAddr: 0x1E0, symBinAddr: 0x88F8, symSize: 0x8 }
  - { offset: 0xA0E0F, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel setChannel:]', symObjAddr: 0x1E8, symBinAddr: 0x8900, symSize: 0xC }
  - { offset: 0xA0E50, size: 0x8, addend: 0x0, symName: '-[FLTThreadSafeEventChannel .cxx_destruct]', symObjAddr: 0x1F4, symBinAddr: 0x890C, symSize: 0xC }
  - { offset: 0xA0FA6, size: 0x8, addend: 0x0, symName: '-[FLTDefaultWritableData initWithData:]', symObjAddr: 0x0, symBinAddr: 0x8918, symSize: 0x78 }
  - { offset: 0xA0FE4, size: 0x8, addend: 0x0, symName: '-[FLTDefaultWritableData initWithData:]', symObjAddr: 0x0, symBinAddr: 0x8918, symSize: 0x78 }
  - { offset: 0xA102B, size: 0x8, addend: 0x0, symName: '-[FLTDefaultWritableData writeToFile:options:error:]', symObjAddr: 0x78, symBinAddr: 0x8990, symSize: 0x74 }
  - { offset: 0xA1092, size: 0x8, addend: 0x0, symName: '-[FLTDefaultWritableData data]', symObjAddr: 0xEC, symBinAddr: 0x8A04, symSize: 0x8 }
  - { offset: 0xA10C9, size: 0x8, addend: 0x0, symName: '-[FLTDefaultWritableData .cxx_destruct]', symObjAddr: 0xF4, symBinAddr: 0x8A0C, symSize: 0xC }
  - { offset: 0xA1196, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraLensDirectionBox initWithValue:]', symObjAddr: 0x0, symBinAddr: 0x8A18, symSize: 0x48 }
  - { offset: 0xA11A4, size: 0x8, addend: 0x0, symName: _FCPGetMessagesCodec, symObjAddr: 0x188C, symBinAddr: 0xA2A4, symSize: 0x30 }
  - { offset: 0xA11CC, size: 0x8, addend: 0x0, symName: _FCPGetMessagesCodec.sSharedObject, symObjAddr: 0x208A8, symBinAddr: 0x44160, symSize: 0x0 }
  - { offset: 0xA11E3, size: 0x8, addend: 0x0, symName: _FCPGetMessagesCodec.sPred, symObjAddr: 0x208B0, symBinAddr: 0x44168, symSize: 0x0 }
  - { offset: 0xA17AC, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraLensDirectionBox initWithValue:]', symObjAddr: 0x0, symBinAddr: 0x8A18, symSize: 0x48 }
  - { offset: 0xA17F3, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraLensDirectionBox value]', symObjAddr: 0x48, symBinAddr: 0x8A60, symSize: 0x8 }
  - { offset: 0xA182A, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraLensDirectionBox setValue:]', symObjAddr: 0x50, symBinAddr: 0x8A68, symSize: 0x8 }
  - { offset: 0xA1867, size: 0x8, addend: 0x0, symName: '-[FCPPlatformDeviceOrientationBox initWithValue:]', symObjAddr: 0x58, symBinAddr: 0x8A70, symSize: 0x48 }
  - { offset: 0xA18AE, size: 0x8, addend: 0x0, symName: '-[FCPPlatformDeviceOrientationBox value]', symObjAddr: 0xA0, symBinAddr: 0x8AB8, symSize: 0x8 }
  - { offset: 0xA18E5, size: 0x8, addend: 0x0, symName: '-[FCPPlatformDeviceOrientationBox setValue:]', symObjAddr: 0xA8, symBinAddr: 0x8AC0, symSize: 0x8 }
  - { offset: 0xA1922, size: 0x8, addend: 0x0, symName: '-[FCPPlatformExposureModeBox initWithValue:]', symObjAddr: 0xB0, symBinAddr: 0x8AC8, symSize: 0x48 }
  - { offset: 0xA1969, size: 0x8, addend: 0x0, symName: '-[FCPPlatformExposureModeBox value]', symObjAddr: 0xF8, symBinAddr: 0x8B10, symSize: 0x8 }
  - { offset: 0xA19A0, size: 0x8, addend: 0x0, symName: '-[FCPPlatformExposureModeBox setValue:]', symObjAddr: 0x100, symBinAddr: 0x8B18, symSize: 0x8 }
  - { offset: 0xA19DD, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFlashModeBox initWithValue:]', symObjAddr: 0x108, symBinAddr: 0x8B20, symSize: 0x48 }
  - { offset: 0xA1A24, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFlashModeBox value]', symObjAddr: 0x150, symBinAddr: 0x8B68, symSize: 0x8 }
  - { offset: 0xA1A5B, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFlashModeBox setValue:]', symObjAddr: 0x158, symBinAddr: 0x8B70, symSize: 0x8 }
  - { offset: 0xA1A98, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFocusModeBox initWithValue:]', symObjAddr: 0x160, symBinAddr: 0x8B78, symSize: 0x48 }
  - { offset: 0xA1ADF, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFocusModeBox value]', symObjAddr: 0x1A8, symBinAddr: 0x8BC0, symSize: 0x8 }
  - { offset: 0xA1B16, size: 0x8, addend: 0x0, symName: '-[FCPPlatformFocusModeBox setValue:]', symObjAddr: 0x1B0, symBinAddr: 0x8BC8, symSize: 0x8 }
  - { offset: 0xA1B53, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFileFormatBox initWithValue:]', symObjAddr: 0x1B8, symBinAddr: 0x8BD0, symSize: 0x48 }
  - { offset: 0xA1B9A, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFileFormatBox value]', symObjAddr: 0x200, symBinAddr: 0x8C18, symSize: 0x8 }
  - { offset: 0xA1BD1, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFileFormatBox setValue:]', symObjAddr: 0x208, symBinAddr: 0x8C20, symSize: 0x8 }
  - { offset: 0xA1C0E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFormatGroupBox initWithValue:]', symObjAddr: 0x210, symBinAddr: 0x8C28, symSize: 0x48 }
  - { offset: 0xA1C55, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFormatGroupBox value]', symObjAddr: 0x258, symBinAddr: 0x8C70, symSize: 0x8 }
  - { offset: 0xA1C8C, size: 0x8, addend: 0x0, symName: '-[FCPPlatformImageFormatGroupBox setValue:]', symObjAddr: 0x260, symBinAddr: 0x8C78, symSize: 0x8 }
  - { offset: 0xA1CC9, size: 0x8, addend: 0x0, symName: '-[FCPPlatformResolutionPresetBox initWithValue:]', symObjAddr: 0x268, symBinAddr: 0x8C80, symSize: 0x48 }
  - { offset: 0xA1D10, size: 0x8, addend: 0x0, symName: '-[FCPPlatformResolutionPresetBox value]', symObjAddr: 0x2B0, symBinAddr: 0x8CC8, symSize: 0x8 }
  - { offset: 0xA1D47, size: 0x8, addend: 0x0, symName: '-[FCPPlatformResolutionPresetBox setValue:]', symObjAddr: 0x2B8, symBinAddr: 0x8CD0, symSize: 0x8 }
  - { offset: 0xA1D84, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraDescription makeWithName:lensDirection:]', symObjAddr: 0x2C0, symBinAddr: 0x8CD8, symSize: 0x68 }
  - { offset: 0xA1DE7, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraDescription fromList:]', symObjAddr: 0x328, symBinAddr: 0x8D40, symSize: 0xB0 }
  - { offset: 0xA1E82, size: 0x8, addend: 0x0, symName: _GetNullableObjectAtIndex, symObjAddr: 0x3D8, symBinAddr: 0x8DF0, symSize: 0x70 }
  - { offset: 0xA1ECD, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraDescription nullableFromList:]', symObjAddr: 0x448, symBinAddr: 0x8E60, symSize: 0x30 }
  - { offset: 0xA1F10, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription toList]', symObjAddr: 0x478, symBinAddr: 0x8E90, symSize: 0xF8 }
  - { offset: 0xA1F47, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription name]', symObjAddr: 0x570, symBinAddr: 0x8F88, symSize: 0x8 }
  - { offset: 0xA1F7E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription setName:]', symObjAddr: 0x578, symBinAddr: 0x8F90, symSize: 0x8 }
  - { offset: 0xA1FBD, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription lensDirection]', symObjAddr: 0x580, symBinAddr: 0x8F98, symSize: 0x8 }
  - { offset: 0xA1FF4, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription setLensDirection:]', symObjAddr: 0x588, symBinAddr: 0x8FA0, symSize: 0x8 }
  - { offset: 0xA2031, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraDescription .cxx_destruct]', symObjAddr: 0x590, symBinAddr: 0x8FA8, symSize: 0xC }
  - { offset: 0xA2064, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraState makeWithPreviewSize:exposureMode:focusMode:exposurePointSupported:focusPointSupported:]', symObjAddr: 0x59C, symBinAddr: 0x8FB4, symSize: 0xA0 }
  - { offset: 0xA20F7, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraState fromList:]', symObjAddr: 0x63C, symBinAddr: 0x9054, symSize: 0x148 }
  - { offset: 0xA21F6, size: 0x8, addend: 0x0, symName: '+[FCPPlatformCameraState nullableFromList:]', symObjAddr: 0x784, symBinAddr: 0x919C, symSize: 0x30 }
  - { offset: 0xA2239, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState toList]', symObjAddr: 0x7B4, symBinAddr: 0x91CC, symSize: 0x1A0 }
  - { offset: 0xA2270, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState previewSize]', symObjAddr: 0x954, symBinAddr: 0x936C, symSize: 0x8 }
  - { offset: 0xA22A7, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState setPreviewSize:]', symObjAddr: 0x95C, symBinAddr: 0x9374, symSize: 0xC }
  - { offset: 0xA22E8, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState exposureMode]', symObjAddr: 0x968, symBinAddr: 0x9380, symSize: 0x8 }
  - { offset: 0xA231F, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState setExposureMode:]', symObjAddr: 0x970, symBinAddr: 0x9388, symSize: 0x8 }
  - { offset: 0xA235C, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState focusMode]', symObjAddr: 0x978, symBinAddr: 0x9390, symSize: 0x8 }
  - { offset: 0xA2393, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState setFocusMode:]', symObjAddr: 0x980, symBinAddr: 0x9398, symSize: 0x8 }
  - { offset: 0xA23D0, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState exposurePointSupported]', symObjAddr: 0x988, symBinAddr: 0x93A0, symSize: 0x8 }
  - { offset: 0xA2407, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState setExposurePointSupported:]', symObjAddr: 0x990, symBinAddr: 0x93A8, symSize: 0x8 }
  - { offset: 0xA2442, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState focusPointSupported]', symObjAddr: 0x998, symBinAddr: 0x93B0, symSize: 0x8 }
  - { offset: 0xA2479, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState setFocusPointSupported:]', symObjAddr: 0x9A0, symBinAddr: 0x93B8, symSize: 0x8 }
  - { offset: 0xA24B4, size: 0x8, addend: 0x0, symName: '-[FCPPlatformCameraState .cxx_destruct]', symObjAddr: 0x9A8, symBinAddr: 0x93C0, symSize: 0xC }
  - { offset: 0xA24E7, size: 0x8, addend: 0x0, symName: '+[FCPPlatformMediaSettings makeWithResolutionPreset:framesPerSecond:videoBitrate:audioBitrate:enableAudio:]', symObjAddr: 0x9B4, symBinAddr: 0x93CC, symSize: 0xC8 }
  - { offset: 0xA257A, size: 0x8, addend: 0x0, symName: '+[FCPPlatformMediaSettings fromList:]', symObjAddr: 0xA7C, symBinAddr: 0x9494, symSize: 0x138 }
  - { offset: 0xA2669, size: 0x8, addend: 0x0, symName: '+[FCPPlatformMediaSettings nullableFromList:]', symObjAddr: 0xBB4, symBinAddr: 0x95CC, symSize: 0x30 }
  - { offset: 0xA26AC, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings toList]', symObjAddr: 0xBE4, symBinAddr: 0x95FC, symSize: 0x1E0 }
  - { offset: 0xA26E3, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings resolutionPreset]', symObjAddr: 0xDC4, symBinAddr: 0x97DC, symSize: 0x8 }
  - { offset: 0xA271A, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings setResolutionPreset:]', symObjAddr: 0xDCC, symBinAddr: 0x97E4, symSize: 0x8 }
  - { offset: 0xA2757, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings framesPerSecond]', symObjAddr: 0xDD4, symBinAddr: 0x97EC, symSize: 0x8 }
  - { offset: 0xA278E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings setFramesPerSecond:]', symObjAddr: 0xDDC, symBinAddr: 0x97F4, symSize: 0xC }
  - { offset: 0xA27CF, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings videoBitrate]', symObjAddr: 0xDE8, symBinAddr: 0x9800, symSize: 0x8 }
  - { offset: 0xA2806, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings setVideoBitrate:]', symObjAddr: 0xDF0, symBinAddr: 0x9808, symSize: 0xC }
  - { offset: 0xA2847, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings audioBitrate]', symObjAddr: 0xDFC, symBinAddr: 0x9814, symSize: 0x8 }
  - { offset: 0xA287E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings setAudioBitrate:]', symObjAddr: 0xE04, symBinAddr: 0x981C, symSize: 0xC }
  - { offset: 0xA28BF, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings enableAudio]', symObjAddr: 0xE10, symBinAddr: 0x9828, symSize: 0x8 }
  - { offset: 0xA28F6, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings setEnableAudio:]', symObjAddr: 0xE18, symBinAddr: 0x9830, symSize: 0x8 }
  - { offset: 0xA2931, size: 0x8, addend: 0x0, symName: '-[FCPPlatformMediaSettings .cxx_destruct]', symObjAddr: 0xE20, symBinAddr: 0x9838, symSize: 0x3C }
  - { offset: 0xA2964, size: 0x8, addend: 0x0, symName: '+[FCPPlatformPoint makeWithX:y:]', symObjAddr: 0xE5C, symBinAddr: 0x9874, symSize: 0x54 }
  - { offset: 0xA29CB, size: 0x8, addend: 0x0, symName: '+[FCPPlatformPoint fromList:]', symObjAddr: 0xEB0, symBinAddr: 0x98C8, symSize: 0xAC }
  - { offset: 0xA2A59, size: 0x8, addend: 0x0, symName: '+[FCPPlatformPoint nullableFromList:]', symObjAddr: 0xF5C, symBinAddr: 0x9974, symSize: 0x30 }
  - { offset: 0xA2A9E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformPoint toList]', symObjAddr: 0xF8C, symBinAddr: 0x99A4, symSize: 0xD4 }
  - { offset: 0xA2AD6, size: 0x8, addend: 0x0, symName: '-[FCPPlatformPoint x]', symObjAddr: 0x1060, symBinAddr: 0x9A78, symSize: 0x8 }
  - { offset: 0xA2B0B, size: 0x8, addend: 0x0, symName: '-[FCPPlatformPoint setX:]', symObjAddr: 0x1068, symBinAddr: 0x9A80, symSize: 0x8 }
  - { offset: 0xA2B49, size: 0x8, addend: 0x0, symName: '-[FCPPlatformPoint y]', symObjAddr: 0x1070, symBinAddr: 0x9A88, symSize: 0x8 }
  - { offset: 0xA2B7E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformPoint setY:]', symObjAddr: 0x1078, symBinAddr: 0x9A90, symSize: 0x8 }
  - { offset: 0xA2BBC, size: 0x8, addend: 0x0, symName: '+[FCPPlatformSize makeWithWidth:height:]', symObjAddr: 0x1080, symBinAddr: 0x9A98, symSize: 0x54 }
  - { offset: 0xA2C23, size: 0x8, addend: 0x0, symName: '+[FCPPlatformSize fromList:]', symObjAddr: 0x10D4, symBinAddr: 0x9AEC, symSize: 0xAC }
  - { offset: 0xA2CB1, size: 0x8, addend: 0x0, symName: '+[FCPPlatformSize nullableFromList:]', symObjAddr: 0x1180, symBinAddr: 0x9B98, symSize: 0x30 }
  - { offset: 0xA2CF6, size: 0x8, addend: 0x0, symName: '-[FCPPlatformSize toList]', symObjAddr: 0x11B0, symBinAddr: 0x9BC8, symSize: 0xD4 }
  - { offset: 0xA2D2E, size: 0x8, addend: 0x0, symName: '-[FCPPlatformSize width]', symObjAddr: 0x1284, symBinAddr: 0x9C9C, symSize: 0x8 }
  - { offset: 0xA2D63, size: 0x8, addend: 0x0, symName: '-[FCPPlatformSize setWidth:]', symObjAddr: 0x128C, symBinAddr: 0x9CA4, symSize: 0x8 }
  - { offset: 0xA2DA1, size: 0x8, addend: 0x0, symName: '-[FCPPlatformSize height]', symObjAddr: 0x1294, symBinAddr: 0x9CAC, symSize: 0x8 }
  - { offset: 0xA2DD6, size: 0x8, addend: 0x0, symName: '-[FCPPlatformSize setHeight:]', symObjAddr: 0x129C, symBinAddr: 0x9CB4, symSize: 0x8 }
  - { offset: 0xA2E14, size: 0x8, addend: 0x0, symName: '-[FCPMessagesPigeonCodecReader readValueOfType:]', symObjAddr: 0x12A4, symBinAddr: 0x9CBC, symSize: 0x200 }
  - { offset: 0xA2F4D, size: 0x8, addend: 0x0, symName: '-[FCPMessagesPigeonCodecWriter writeValue:]', symObjAddr: 0x14A4, symBinAddr: 0x9EBC, symSize: 0x350 }
  - { offset: 0xA306A, size: 0x8, addend: 0x0, symName: '-[FCPMessagesPigeonCodecReaderWriter writerWithData:]', symObjAddr: 0x17F4, symBinAddr: 0xA20C, symSize: 0x4C }
  - { offset: 0xA30AF, size: 0x8, addend: 0x0, symName: '-[FCPMessagesPigeonCodecReaderWriter readerWithData:]', symObjAddr: 0x1840, symBinAddr: 0xA258, symSize: 0x4C }
  - { offset: 0xA3128, size: 0x8, addend: 0x0, symName: _FCPGetMessagesCodec.cold.1, symObjAddr: 0x5B60, symBinAddr: 0x22968, symSize: 0x14 }
  - { offset: 0xA3144, size: 0x8, addend: 0x0, symName: _FCPGetMessagesCodec.cold.1, symObjAddr: 0x5B60, symBinAddr: 0x22968, symSize: 0x14 }
  - { offset: 0xA3157, size: 0x8, addend: 0x0, symName: ___FCPGetMessagesCodec_block_invoke, symObjAddr: 0x18BC, symBinAddr: 0xA2D4, symSize: 0x5C }
  - { offset: 0xA31A0, size: 0x8, addend: 0x0, symName: _SetUpFCPCameraApi, symObjAddr: 0x1918, symBinAddr: 0xA330, symSize: 0xC }
  - { offset: 0xA31F5, size: 0x8, addend: 0x0, symName: _SetUpFCPCameraApiWithSuffix, symObjAddr: 0x1924, symBinAddr: 0xA33C, symSize: 0x181C }
  - { offset: 0xA37A4, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke, symObjAddr: 0x3140, symBinAddr: 0xBB58, symSize: 0x84 }
  - { offset: 0xA3800, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_2, symObjAddr: 0x31C4, symBinAddr: 0xBBDC, symSize: 0x48 }
  - { offset: 0xA389A, size: 0x8, addend: 0x0, symName: _wrapResult, symObjAddr: 0x320C, symBinAddr: 0xBC24, symSize: 0x1EC }
  - { offset: 0xA38D5, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke.181, symObjAddr: 0x3418, symBinAddr: 0xBE10, symSize: 0xF8 }
  - { offset: 0xA39A4, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_2.182, symObjAddr: 0x3510, symBinAddr: 0xBF08, symSize: 0x48 }
  - { offset: 0xA3A3E, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke.186, symObjAddr: 0x3558, symBinAddr: 0xBF50, symSize: 0x10C }
  - { offset: 0xA3B1E, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_2.187, symObjAddr: 0x3664, symBinAddr: 0xC05C, symSize: 0x44 }
  - { offset: 0xA3BA5, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke.191, symObjAddr: 0x36A8, symBinAddr: 0xC0A0, symSize: 0x84 }
  - { offset: 0xA3C01, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_2.192, symObjAddr: 0x372C, symBinAddr: 0xC124, symSize: 0x44 }
  - { offset: 0xA3C88, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_3, symObjAddr: 0x3770, symBinAddr: 0xC168, symSize: 0x84 }
  - { offset: 0xA3CE4, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_4, symObjAddr: 0x37F4, symBinAddr: 0xC1EC, symSize: 0x44 }
  - { offset: 0xA3D6B, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_5, symObjAddr: 0x3838, symBinAddr: 0xC230, symSize: 0x84 }
  - { offset: 0xA3DC7, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_6, symObjAddr: 0x38BC, symBinAddr: 0xC2B4, symSize: 0x44 }
  - { offset: 0xA3E4E, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_7, symObjAddr: 0x3900, symBinAddr: 0xC2F8, symSize: 0xC4 }
  - { offset: 0xA3EFB, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_8, symObjAddr: 0x39C4, symBinAddr: 0xC3BC, symSize: 0x44 }
  - { offset: 0xA3F82, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_9, symObjAddr: 0x3A08, symBinAddr: 0xC400, symSize: 0xC4 }
  - { offset: 0xA4040, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_10, symObjAddr: 0x3ACC, symBinAddr: 0xC4C4, symSize: 0x44 }
  - { offset: 0xA40C7, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_11, symObjAddr: 0x3B10, symBinAddr: 0xC508, symSize: 0x84 }
  - { offset: 0xA4123, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_12, symObjAddr: 0x3B94, symBinAddr: 0xC58C, symSize: 0x44 }
  - { offset: 0xA41AA, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_13, symObjAddr: 0x3BD8, symBinAddr: 0xC5D0, symSize: 0x84 }
  - { offset: 0xA4206, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_14, symObjAddr: 0x3C5C, symBinAddr: 0xC654, symSize: 0x48 }
  - { offset: 0xA42A0, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke.208, symObjAddr: 0x3CA4, symBinAddr: 0xC69C, symSize: 0x84 }
  - { offset: 0xA42FC, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_2.209, symObjAddr: 0x3D28, symBinAddr: 0xC720, symSize: 0x44 }
  - { offset: 0xA4383, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_3.212, symObjAddr: 0x3D6C, symBinAddr: 0xC764, symSize: 0xC4 }
  - { offset: 0xA4430, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_4.213, symObjAddr: 0x3E30, symBinAddr: 0xC828, symSize: 0x44 }
  - { offset: 0xA44B7, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_5.216, symObjAddr: 0x3E74, symBinAddr: 0xC86C, symSize: 0x84 }
  - { offset: 0xA4513, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_6.217, symObjAddr: 0x3EF8, symBinAddr: 0xC8F0, symSize: 0x48 }
  - { offset: 0xA45AD, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_7.220, symObjAddr: 0x3F40, symBinAddr: 0xC938, symSize: 0x84 }
  - { offset: 0xA4609, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_8.221, symObjAddr: 0x3FC4, symBinAddr: 0xC9BC, symSize: 0x44 }
  - { offset: 0xA4690, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_9.224, symObjAddr: 0x4008, symBinAddr: 0xCA00, symSize: 0x84 }
  - { offset: 0xA46EC, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_10.225, symObjAddr: 0x408C, symBinAddr: 0xCA84, symSize: 0x44 }
  - { offset: 0xA4773, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_11.228, symObjAddr: 0x40D0, symBinAddr: 0xCAC8, symSize: 0xC4 }
  - { offset: 0xA4831, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_12.229, symObjAddr: 0x4194, symBinAddr: 0xCB8C, symSize: 0x44 }
  - { offset: 0xA48B8, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_13.232, symObjAddr: 0x41D8, symBinAddr: 0xCBD0, symSize: 0xC4 }
  - { offset: 0xA4976, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_14.233, symObjAddr: 0x429C, symBinAddr: 0xCC94, symSize: 0x44 }
  - { offset: 0xA49FD, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_15, symObjAddr: 0x42E0, symBinAddr: 0xCCD8, symSize: 0xBC }
  - { offset: 0xA4AAA, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_16, symObjAddr: 0x439C, symBinAddr: 0xCD94, symSize: 0x44 }
  - { offset: 0xA4B31, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_17, symObjAddr: 0x43E0, symBinAddr: 0xCDD8, symSize: 0x84 }
  - { offset: 0xA4B8D, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_18, symObjAddr: 0x4464, symBinAddr: 0xCE5C, symSize: 0x48 }
  - { offset: 0xA4C27, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_19, symObjAddr: 0x44AC, symBinAddr: 0xCEA4, symSize: 0x84 }
  - { offset: 0xA4C83, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_20, symObjAddr: 0x4530, symBinAddr: 0xCF28, symSize: 0x48 }
  - { offset: 0xA4D1D, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_21, symObjAddr: 0x4578, symBinAddr: 0xCF70, symSize: 0xCC }
  - { offset: 0xA4DCA, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_22, symObjAddr: 0x4644, symBinAddr: 0xD03C, symSize: 0x44 }
  - { offset: 0xA4E51, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_23, symObjAddr: 0x4688, symBinAddr: 0xD080, symSize: 0xC4 }
  - { offset: 0xA4F0F, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_24, symObjAddr: 0x474C, symBinAddr: 0xD144, symSize: 0x44 }
  - { offset: 0xA4F96, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_25, symObjAddr: 0x4790, symBinAddr: 0xD188, symSize: 0xBC }
  - { offset: 0xA5043, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_26, symObjAddr: 0x484C, symBinAddr: 0xD244, symSize: 0x44 }
  - { offset: 0xA50CA, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_27, symObjAddr: 0x4890, symBinAddr: 0xD288, symSize: 0x84 }
  - { offset: 0xA5126, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_28, symObjAddr: 0x4914, symBinAddr: 0xD30C, symSize: 0x48 }
  - { offset: 0xA51C0, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_29, symObjAddr: 0x495C, symBinAddr: 0xD354, symSize: 0x84 }
  - { offset: 0xA521C, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_30, symObjAddr: 0x49E0, symBinAddr: 0xD3D8, symSize: 0x48 }
  - { offset: 0xA52B6, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_31, symObjAddr: 0x4A28, symBinAddr: 0xD420, symSize: 0xCC }
  - { offset: 0xA5363, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_32, symObjAddr: 0x4AF4, symBinAddr: 0xD4EC, symSize: 0x44 }
  - { offset: 0xA53EA, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_33, symObjAddr: 0x4B38, symBinAddr: 0xD530, symSize: 0x84 }
  - { offset: 0xA5446, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_34, symObjAddr: 0x4BBC, symBinAddr: 0xD5B4, symSize: 0x44 }
  - { offset: 0xA54CD, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_35, symObjAddr: 0x4C00, symBinAddr: 0xD5F8, symSize: 0x84 }
  - { offset: 0xA5529, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_36, symObjAddr: 0x4C84, symBinAddr: 0xD67C, symSize: 0x44 }
  - { offset: 0xA55B0, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_37, symObjAddr: 0x4CC8, symBinAddr: 0xD6C0, symSize: 0xBC }
  - { offset: 0xA565D, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_38, symObjAddr: 0x4D84, symBinAddr: 0xD77C, symSize: 0x44 }
  - { offset: 0xA56E4, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_39, symObjAddr: 0x4DC8, symBinAddr: 0xD7C0, symSize: 0xC4 }
  - { offset: 0xA57A2, size: 0x8, addend: 0x0, symName: ___SetUpFCPCameraApiWithSuffix_block_invoke_40, symObjAddr: 0x4E8C, symBinAddr: 0xD884, symSize: 0x44 }
  - { offset: 0xA5829, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi initWithBinaryMessenger:]', symObjAddr: 0x4ED0, symBinAddr: 0xD8C8, symSize: 0xC }
  - { offset: 0xA5870, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi initWithBinaryMessenger:messageChannelSuffix:]', symObjAddr: 0x4EDC, symBinAddr: 0xD8D4, symSize: 0xCC }
  - { offset: 0xA58CA, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi deviceOrientationChangedOrientation:completion:]', symObjAddr: 0x4FA8, symBinAddr: 0xD9A0, symSize: 0x1D0 }
  - { offset: 0xA5950, size: 0x8, addend: 0x0, symName: '___74-[FCPCameraGlobalEventApi deviceOrientationChangedOrientation:completion:]_block_invoke', symObjAddr: 0x5178, symBinAddr: 0xDB70, symSize: 0x134 }
  - { offset: 0xA5A0E, size: 0x8, addend: 0x0, symName: _createConnectionError, symObjAddr: 0x52AC, symBinAddr: 0xDCA4, symSize: 0x98 }
  - { offset: 0xA5A39, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x5344, symBinAddr: 0xDD3C, symSize: 0x34 }
  - { offset: 0xA5A62, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x5378, symBinAddr: 0xDD70, symSize: 0x28 }
  - { offset: 0xA5A81, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi binaryMessenger]', symObjAddr: 0x53A0, symBinAddr: 0xDD98, symSize: 0x8 }
  - { offset: 0xA5AB9, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi setBinaryMessenger:]', symObjAddr: 0x53A8, symBinAddr: 0xDDA0, symSize: 0xC }
  - { offset: 0xA5AFB, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi messageChannelSuffix]', symObjAddr: 0x53B4, symBinAddr: 0xDDAC, symSize: 0x8 }
  - { offset: 0xA5B33, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi setMessageChannelSuffix:]', symObjAddr: 0x53BC, symBinAddr: 0xDDB4, symSize: 0xC }
  - { offset: 0xA5B75, size: 0x8, addend: 0x0, symName: '-[FCPCameraGlobalEventApi .cxx_destruct]', symObjAddr: 0x53C8, symBinAddr: 0xDDC0, symSize: 0x30 }
  - { offset: 0xA5BA9, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi initWithBinaryMessenger:]', symObjAddr: 0x53F8, symBinAddr: 0xDDF0, symSize: 0xC }
  - { offset: 0xA5BF0, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi initWithBinaryMessenger:messageChannelSuffix:]', symObjAddr: 0x5404, symBinAddr: 0xDDFC, symSize: 0xCC }
  - { offset: 0xA5C4A, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi initializedWithState:completion:]', symObjAddr: 0x54D0, symBinAddr: 0xDEC8, symSize: 0x1E8 }
  - { offset: 0xA5CD0, size: 0x8, addend: 0x0, symName: '___53-[FCPCameraEventApi initializedWithState:completion:]_block_invoke', symObjAddr: 0x56B8, symBinAddr: 0xE0B0, symSize: 0x134 }
  - { offset: 0xA5D8E, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi reportError:completion:]', symObjAddr: 0x57EC, symBinAddr: 0xE1E4, symSize: 0x1E8 }
  - { offset: 0xA5E14, size: 0x8, addend: 0x0, symName: '___44-[FCPCameraEventApi reportError:completion:]_block_invoke', symObjAddr: 0x59D4, symBinAddr: 0xE3CC, symSize: 0x134 }
  - { offset: 0xA5ED2, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi binaryMessenger]', symObjAddr: 0x5B08, symBinAddr: 0xE500, symSize: 0x8 }
  - { offset: 0xA5F0A, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi setBinaryMessenger:]', symObjAddr: 0x5B10, symBinAddr: 0xE508, symSize: 0xC }
  - { offset: 0xA5F4C, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi messageChannelSuffix]', symObjAddr: 0x5B1C, symBinAddr: 0xE514, symSize: 0x8 }
  - { offset: 0xA5F84, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi setMessageChannelSuffix:]', symObjAddr: 0x5B24, symBinAddr: 0xE51C, symSize: 0xC }
  - { offset: 0xA5FC6, size: 0x8, addend: 0x0, symName: '-[FCPCameraEventApi .cxx_destruct]', symObjAddr: 0x5B30, symBinAddr: 0xE528, symSize: 0x30 }
  - { offset: 0xA7B7E, size: 0x8, addend: 0x0, symName: _FLTEnsureToRunOnMainQueue, symObjAddr: 0x0, symBinAddr: 0xE558, symSize: 0x58 }
  - { offset: 0xA7B8C, size: 0x8, addend: 0x0, symName: _FLTEnsureToRunOnMainQueue, symObjAddr: 0x0, symBinAddr: 0xE558, symSize: 0x58 }
  - { offset: 0xA7C4B, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC0A0AA0C0_pSgvpfi', symObjAddr: 0x0, symBinAddr: 0xE5B0, symSize: 0xC }
  - { offset: 0xA7C63, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_4, symObjAddr: 0xC, symBinAddr: 0xE5BC, symSize: 0x14 }
  - { offset: 0xA7C77, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x20, symBinAddr: 0xE5D0, symSize: 0x4 }
  - { offset: 0xA7C8B, size: 0x8, addend: 0x0, symName: '_$sSo6CMTimeawet', symObjAddr: 0x24, symBinAddr: 0xE5D4, symSize: 0x20 }
  - { offset: 0xA7C9F, size: 0x8, addend: 0x0, symName: '_$sSo6CMTimeawst', symObjAddr: 0x44, symBinAddr: 0xE5F4, symSize: 0x2C }
  - { offset: 0xA7CBE, size: 0x8, addend: 0x0, symName: '_$sSo22AVAudioSessionCategoryas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x570, symBinAddr: 0xEAA0, symSize: 0x2C }
  - { offset: 0xA7CD2, size: 0x8, addend: 0x0, symName: '_$sSo22AVAudioSessionCategoryas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x59C, symBinAddr: 0xEACC, symSize: 0x2C }
  - { offset: 0xA7CE6, size: 0x8, addend: 0x0, symName: '_$sSo22AVAudioSessionCategoryaSHSCSQWb', symObjAddr: 0x608, symBinAddr: 0xEB38, symSize: 0x2C }
  - { offset: 0xA7CFA, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x6B8, symBinAddr: 0xEBE8, symSize: 0x2C }
  - { offset: 0xA7D0E, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x6E4, symBinAddr: 0xEC14, symSize: 0x2C }
  - { offset: 0xA7D22, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeaSHSCSQWb', symObjAddr: 0x710, symBinAddr: 0xEC40, symSize: 0x2C }
  - { offset: 0xA7D36, size: 0x8, addend: 0x0, symName: '_$sSo19AVCaptureDeviceTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x83C, symBinAddr: 0xED6C, symSize: 0x2C }
  - { offset: 0xA7D4A, size: 0x8, addend: 0x0, symName: '_$sSo19AVCaptureDeviceTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x868, symBinAddr: 0xED98, symSize: 0x2C }
  - { offset: 0xA7D5E, size: 0x8, addend: 0x0, symName: '_$sSo19AVCaptureDeviceTypeaSHSCSQWb', symObjAddr: 0x918, symBinAddr: 0xEE48, symSize: 0x2C }
  - { offset: 0xA7DD6, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x2BC, symBinAddr: 0xE7EC, symSize: 0x14 }
  - { offset: 0xA7DF2, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x2D0, symBinAddr: 0xE800, symSize: 0x18 }
  - { offset: 0xA7E1D, size: 0x8, addend: 0x0, symName: '_$sSo18AVCaptureTorchModeVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x330, symBinAddr: 0xE860, symSize: 0x14 }
  - { offset: 0xA7E69, size: 0x8, addend: 0x0, symName: '_$sSo19AVCaptureDeviceTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x468, symBinAddr: 0xE998, symSize: 0x84 }
  - { offset: 0xA7E85, size: 0x8, addend: 0x0, symName: '_$sSo22AVAudioSessionCategoryas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x4EC, symBinAddr: 0xEA1C, symSize: 0x84 }
  - { offset: 0xA7EA1, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x634, symBinAddr: 0xEB64, symSize: 0x84 }
  - { offset: 0xA7ED0, size: 0x8, addend: 0x0, symName: '_$sSo19UIDeviceOrientationVSYSCSY8rawValuexSg03RawD0Qz_tcfCTW', symObjAddr: 0x220, symBinAddr: 0xE7D0, symSize: 0x10 }
  - { offset: 0xA7F64, size: 0x8, addend: 0x0, symName: '_$sSo16AVVideoCodecTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x348, symBinAddr: 0xE878, symSize: 0x28 }
  - { offset: 0xA7F8D, size: 0x8, addend: 0x0, symName: '_$sSo18AVCaptureTorchModeVSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x370, symBinAddr: 0xE8A0, symSize: 0xC }
  - { offset: 0xA836B, size: 0x8, addend: 0x0, symName: '_$s10Foundation12NotificationVIeghn_So14NSNotificationCIeyBhy_TR', symObjAddr: 0x440, symBinAddr: 0xF2E8, symSize: 0x94 }
  - { offset: 0xA86A5, size: 0x8, addend: 0x0, symName: '_$sIegh_IeyBh_TR', symObjAddr: 0x8A4, symBinAddr: 0xF74C, symSize: 0x2C }
  - { offset: 0xA86D3, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCfETo', symObjAddr: 0xA6C, symBinAddr: 0xF914, symSize: 0xC0 }
  - { offset: 0xA8AA2, size: 0x8, addend: 0x0, symName: '_$sSo7NSArrayCSgSo12FlutterErrorCSgIeyByy_SaySo28FCPPlatformCameraDescriptionCGSgAFIeggg_TR', symObjAddr: 0xF24, symBinAddr: 0xFDCC, symSize: 0x70 }
  - { offset: 0xA8ABA, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC08withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctF', symObjAddr: 0xF94, symBinAddr: 0xFE3C, symSize: 0x240 }
  - { offset: 0xA8B3A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC08withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyyYbcfU_', symObjAddr: 0x11D4, symBinAddr: 0x1007C, symSize: 0x148 }
  - { offset: 0xA8B91, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC08withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyyYbcfU_yAOcfU_', symObjAddr: 0x131C, symBinAddr: 0x101C4, symSize: 0x1FC }
  - { offset: 0xA8C0F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC08withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyyYbcfU_yAOcfU_yAOcfU_', symObjAddr: 0x1518, symBinAddr: 0x103C0, symSize: 0xC8 }
  - { offset: 0xA8C8E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC14OnSessionQueue8withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctF', symObjAddr: 0x15E0, symBinAddr: 0x10488, symSize: 0x240 }
  - { offset: 0xA8D0E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC14OnSessionQueue8withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyyYbcfU_', symObjAddr: 0x18E0, symBinAddr: 0x10788, symSize: 0x90 }
  - { offset: 0xA8D66, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC06createC08withName8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFTo', symObjAddr: 0x1820, symBinAddr: 0x106C8, symSize: 0xC0 }
  - { offset: 0xA8D82, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC018sessionQueueCreateC033_237EF82A591AD84AAC1050734DCB1252LL4name8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctF', symObjAddr: 0x1970, symBinAddr: 0x10818, symSize: 0x308 }
  - { offset: 0xA8ED7, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC018sessionQueueCreateC033_237EF82A591AD84AAC1050734DCB1252LL4name8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFSo16FLTCaptureDevice_pycfU_', symObjAddr: 0x1C78, symBinAddr: 0x10B20, symSize: 0x7C }
  - { offset: 0xA8F32, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC018sessionQueueCreateC033_237EF82A591AD84AAC1050734DCB1252LL4name8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyycfU0_', symObjAddr: 0x1CF4, symBinAddr: 0x10B9C, symSize: 0xB8 }
  - { offset: 0xA8FC0, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC010initializeC0_15withImageFormat10completionySi_So011FCPPlatformgH5GroupVySo12FlutterErrorCSgctF', symObjAddr: 0x1DAC, symBinAddr: 0x10C54, symSize: 0x228 }
  - { offset: 0xA9047, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC010initializeC0_15withImageFormat10completionySi_So011FCPPlatformgH5GroupVySo12FlutterErrorCSgctFyyYbcfU_', symObjAddr: 0x1FD4, symBinAddr: 0x10E7C, symSize: 0x88 }
  - { offset: 0xA90A4, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC022sessionQueueInitializeC033_237EF82A591AD84AAC1050734DCB1252LL_15withImageFormat10completionySi_So011FCPPlatformnO5GroupVySo12FlutterErrorCSgctF', symObjAddr: 0x205C, symBinAddr: 0x10F04, symSize: 0x2B8 }
  - { offset: 0xA920A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC022sessionQueueInitializeC033_237EF82A591AD84AAC1050734DCB1252LL_15withImageFormat10completionySi_So011FCPPlatformnO5GroupVySo12FlutterErrorCSgctFyycfU_', symObjAddr: 0x23A4, symBinAddr: 0x1124C, symSize: 0x11C }
  - { offset: 0xA9287, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC022sessionQueueInitializeC033_237EF82A591AD84AAC1050734DCB1252LL_15withImageFormat10completionySi_So011FCPPlatformnO5GroupVySo12FlutterErrorCSgctFyycfU_yycfU_', symObjAddr: 0x24C0, symBinAddr: 0x11368, symSize: 0x8C }
  - { offset: 0xA92C2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC010initializeC0_15withImageFormat10completionySi_So011FCPPlatformgH5GroupVySo12FlutterErrorCSgctFTo', symObjAddr: 0x2314, symBinAddr: 0x111BC, symSize: 0x90 }
  - { offset: 0xA93AC, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC07disposeC0_10completionySi_ySo12FlutterErrorCSgctF', symObjAddr: 0x2850, symBinAddr: 0x116F8, symSize: 0x244 }
  - { offset: 0xA9422, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC07disposeC0_10completionySi_ySo12FlutterErrorCSgctFyyYbcfU_', symObjAddr: 0x2A94, symBinAddr: 0x1193C, symSize: 0xAC }
  - { offset: 0xA9535, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgSo12FlutterErrorCSgIeyByy_SSSgAFIeggg_TR', symObjAddr: 0x2E28, symBinAddr: 0x11CD0, symSize: 0x4C }
  - { offset: 0xA954D, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19startVideoRecording13withStreaming10completionySb_ySo12FlutterErrorCSgctF', symObjAddr: 0x2F50, symBinAddr: 0x11DF8, symSize: 0x228 }
  - { offset: 0xA95C3, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19startVideoRecording13withStreaming10completionySb_ySo12FlutterErrorCSgctFyyYbcfU_', symObjAddr: 0x3178, symBinAddr: 0x12020, symSize: 0xD4 }
  - { offset: 0xA963D, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19startVideoRecording13withStreaming10completionySb_ySo12FlutterErrorCSgctFTo', symObjAddr: 0x324C, symBinAddr: 0x120F4, symSize: 0x7C }
  - { offset: 0xA97F3, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC031updateDescriptionWhileRecordingC4Name_10completionySS_ySo12FlutterErrorCSgctF', symObjAddr: 0x45A0, symBinAddr: 0x13448, symSize: 0x230 }
  - { offset: 0xA9869, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC031updateDescriptionWhileRecordingC4Name_10completionySS_ySo12FlutterErrorCSgctFyyYbcfU_', symObjAddr: 0x47D0, symBinAddr: 0x13678, symSize: 0xB0 }
  - { offset: 0xA98CB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC031updateDescriptionWhileRecordingC4Name_10completionySS_ySo12FlutterErrorCSgctFTo', symObjAddr: 0x4880, symBinAddr: 0x13728, symSize: 0xA0 }
  - { offset: 0xA9941, size: 0x8, addend: 0x0, symName: '_$sSSSo16FLTCaptureDevice_pIeggo_So8NSStringCSoAA_pIeyBya_TR', symObjAddr: 0x4DCC, symBinAddr: 0x13C74, symSize: 0x60 }
  - { offset: 0xA996A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC16availableCameras10completionyySaySo011FCPPlatformC11DescriptionCGSg_So12FlutterErrorCSgtc_tFyyYbcfU_TA', symObjAddr: 0x5544, symBinAddr: 0x143EC, symSize: 0xC }
  - { offset: 0xA997E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5550, symBinAddr: 0x143F8, symSize: 0x10 }
  - { offset: 0xA9992, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5560, symBinAddr: 0x14408, symSize: 0x8 }
  - { offset: 0xA99A6, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x5568, symBinAddr: 0x14410, symSize: 0x44 }
  - { offset: 0xA99BA, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC16startImageStream10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5630, symBinAddr: 0x144D8, symSize: 0xC }
  - { offset: 0xA99CE, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC15stopImageStream10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x563C, symBinAddr: 0x144E4, symSize: 0x24 }
  - { offset: 0xA99E2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC23receivedImageStreamData10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5660, symBinAddr: 0x14508, symSize: 0xC }
  - { offset: 0xA99F6, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC07disposeC0_10completionySi_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x566C, symBinAddr: 0x14514, symSize: 0xC }
  - { offset: 0xA9A0A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC11lockCapture_10completionySo28FCPPlatformDeviceOrientationV_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x5678, symBinAddr: 0x14520, symSize: 0xC }
  - { offset: 0xA9A1E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC24unlockCaptureOrientation10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5684, symBinAddr: 0x1452C, symSize: 0xC }
  - { offset: 0xA9A32, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC11takePicture10completionyySSSg_So12FlutterErrorCSgtc_tFyyYbcfU_TA', symObjAddr: 0x5690, symBinAddr: 0x14538, symSize: 0x24 }
  - { offset: 0xA9A46, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC24prepareForVideoRecording10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x56B4, symBinAddr: 0x1455C, symSize: 0x24 }
  - { offset: 0xA9A5A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19startVideoRecording13withStreaming10completionySb_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x56E0, symBinAddr: 0x14588, symSize: 0x10 }
  - { offset: 0xA9A6E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC18stopVideoRecording10completionyySSSg_So12FlutterErrorCSgtc_tFyyYbcfU_TA', symObjAddr: 0x56F0, symBinAddr: 0x14598, symSize: 0x24 }
  - { offset: 0xA9A82, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19pauseVideoRecording10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5714, symBinAddr: 0x145BC, symSize: 0xC }
  - { offset: 0xA9A96, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC20resumeVideoRecording10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5720, symBinAddr: 0x145C8, symSize: 0x28 }
  - { offset: 0xA9AAA, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC12setFlashMode_10completionySo011FCPPlatformfG0V_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x574C, symBinAddr: 0x145F4, symSize: 0x24 }
  - { offset: 0xA9ABE, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC15setExposureMode_10completionySo011FCPPlatformfG0V_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x5770, symBinAddr: 0x14618, symSize: 0xC }
  - { offset: 0xA9AD2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC16setExposurePoint_10completionySo011FCPPlatformG0CSg_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x5780, symBinAddr: 0x14628, symSize: 0x24 }
  - { offset: 0xA9AE6, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC24getMinimumExposureOffsetyyySo8NSNumberCSg_So12FlutterErrorCSgtcFyyYbcfU_TA', symObjAddr: 0x57AC, symBinAddr: 0x14654, symSize: 0x24 }
  - { offset: 0xA9AFA, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC24getMaximumExposureOffsetyyySo8NSNumberCSg_So12FlutterErrorCSgtcFyyYbcfU_TA', symObjAddr: 0x57D0, symBinAddr: 0x14678, symSize: 0x24 }
  - { offset: 0xA9B0E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC17setExposureOffset_10completionySd_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x57F4, symBinAddr: 0x1469C, symSize: 0x10 }
  - { offset: 0xA9B22, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC12setFocusMode_10completionySo011FCPPlatformfG0V_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x5804, symBinAddr: 0x146AC, symSize: 0xC }
  - { offset: 0xA9B36, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8setFocus_10completionySo16FCPPlatformPointCSg_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x5844, symBinAddr: 0x146EC, symSize: 0x24 }
  - { offset: 0xA9B4A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19getMinimumZoomLevelyyySo8NSNumberCSg_So12FlutterErrorCSgtcFyyYbcfU_TA', symObjAddr: 0x5868, symBinAddr: 0x14710, symSize: 0x24 }
  - { offset: 0xA9B5E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC19getMaximumZoomLevelyyySo8NSNumberCSg_So12FlutterErrorCSgtcFyyYbcfU_TA', symObjAddr: 0x588C, symBinAddr: 0x14734, symSize: 0x24 }
  - { offset: 0xA9B72, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC12setZoomLevel_10completionySd_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x58B0, symBinAddr: 0x14758, symSize: 0x10 }
  - { offset: 0xA9B86, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC12pausePreview10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x58C0, symBinAddr: 0x14768, symSize: 0x28 }
  - { offset: 0xA9B9A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC13resumePreview10completionyySo12FlutterErrorCSgc_tFyyYbcfU_TA', symObjAddr: 0x5920, symBinAddr: 0x147C8, symSize: 0x28 }
  - { offset: 0xA9BAE, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC18setImageFileFormat_10completionySo011FCPPlatformfgH0V_ySo12FlutterErrorCSgctFyyYbcfU_TA', symObjAddr: 0x59C8, symBinAddr: 0x14870, symSize: 0xC }
  - { offset: 0xA9BC2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCMa', symObjAddr: 0x59D4, symBinAddr: 0x1487C, symSize: 0x20 }
  - { offset: 0xA9BE1, size: 0x8, addend: 0x0, symName: '_$sSo12FlutterErrorCSgIeyBy_ACIegg_TRTA', symObjAddr: 0x5A18, symBinAddr: 0x148C0, symSize: 0x10 }
  - { offset: 0xA9C15, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCSgSo12FlutterErrorCSgIeyByy_AcFIeggg_TRTA', symObjAddr: 0x5A28, symBinAddr: 0x148D0, symSize: 0x14 }
  - { offset: 0xA9C3E, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgSo12FlutterErrorCSgIeyByy_SSSgAFIeggg_TRTA', symObjAddr: 0x5A3C, symBinAddr: 0x148E4, symSize: 0x8 }
  - { offset: 0xA9C52, size: 0x8, addend: 0x0, symName: '_$sSo7NSArrayCSgSo12FlutterErrorCSgIeyByy_SaySo28FCPPlatformCameraDescriptionCGSgAFIeggg_TRTA', symObjAddr: 0x5A44, symBinAddr: 0x148EC, symSize: 0x8 }
  - { offset: 0xA9C66, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC022sessionQueueInitializeC033_237EF82A591AD84AAC1050734DCB1252LL_15withImageFormat10completionySi_So011FCPPlatformnO5GroupVySo12FlutterErrorCSgctFyycfU_TA', symObjAddr: 0x5C68, symBinAddr: 0x14B10, symSize: 0x8 }
  - { offset: 0xA9C7A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC21sendDeviceOrientationyySo08UIDeviceG0VFyyScMYccfU_TA', symObjAddr: 0x5C70, symBinAddr: 0x14B18, symSize: 0x8 }
  - { offset: 0xA9C8E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC022sessionQueueInitializeC033_237EF82A591AD84AAC1050734DCB1252LL_15withImageFormat10completionySi_So011FCPPlatformnO5GroupVySo12FlutterErrorCSgctFyycfU_yycfU_TA', symObjAddr: 0x5C78, symBinAddr: 0x14B20, symSize: 0x8 }
  - { offset: 0xA9CA2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC018sessionQueueCreateC033_237EF82A591AD84AAC1050734DCB1252LL4name8settings10completionySS_So24FCPPlatformMediaSettingsCySo8NSNumberCSg_So12FlutterErrorCSgtctFyycfU0_TA', symObjAddr: 0x5DB8, symBinAddr: 0x14C60, symSize: 0xC }
  - { offset: 0xA9CB6, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x5DC4, symBinAddr: 0x14C6C, symSize: 0x24 }
  - { offset: 0xA9CCA, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x5DE8, symBinAddr: 0x14C90, symSize: 0x20 }
  - { offset: 0xA9CDE, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8registry9messenger9globalAPI16deviceDiscoverer17permissionManager0I7Factory014captureSessionM00n11DeviceInputM00nO5QueueACSo22FlutterTextureRegistry_p_So0S15BinaryMessenger_pSo23FCPCameraGlobalEventApiCSo09FLTCameraP11Discovering_pSo019FLTCameraPermissionL0CSo010FLTCaptureP0_pSScSo010FLTCaptureO0_pycSo010FLTCapturepqM0_pSo17OS_dispatch_queueCtcfcy10Foundation12NotificationVYbcfU_TA', symObjAddr: 0x5E94, symBinAddr: 0x14CFC, symSize: 0x8 }
  - { offset: 0xA9CF2, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x5E9C, symBinAddr: 0x14D04, symSize: 0x40 }
  - { offset: 0xA9D06, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC18orientationChangedyy10Foundation12NotificationVFyyYbcfU_TA', symObjAddr: 0x5F1C, symBinAddr: 0x14D84, symSize: 0x8 }
  - { offset: 0xA9DB8, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8register4withySo07FlutterD9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0xEEA8, symSize: 0x4 }
  - { offset: 0xA9DCC, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8register4withySo07FlutterD9Registrar_p_tFZSo16FLTCaptureDevice_pSScfU_', symObjAddr: 0x4, symBinAddr: 0xEEAC, symSize: 0xA4 }
  - { offset: 0xA9E44, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8register4withySo07FlutterD9Registrar_p_tFZSo17FLTCaptureSession_pycfU0_', symObjAddr: 0xA8, symBinAddr: 0xEF50, symSize: 0x60 }
  - { offset: 0xAA5D9, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8register4withySo07FlutterD9Registrar_p_tFZTo', symObjAddr: 0x108, symBinAddr: 0xEFB0, symSize: 0x30 }
  - { offset: 0xAA623, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8registry9messenger9globalAPI16deviceDiscoverer17permissionManager0I7Factory014captureSessionM00n11DeviceInputM00nO5QueueACSo22FlutterTextureRegistry_p_So0S15BinaryMessenger_pSo23FCPCameraGlobalEventApiCSo09FLTCameraP11Discovering_pSo019FLTCameraPermissionL0CSo010FLTCaptureP0_pSScSo010FLTCaptureO0_pycSo010FLTCapturepqM0_pSo17OS_dispatch_queueCtcfcy10Foundation12NotificationVYbcfU_', symObjAddr: 0x138, symBinAddr: 0xEFE0, symSize: 0x5C }
  - { offset: 0xAA6C5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC18orientationChangedyy10Foundation12NotificationVF', symObjAddr: 0x194, symBinAddr: 0xF03C, symSize: 0x2AC }
  - { offset: 0xAA749, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC18orientationChangedyy10Foundation12NotificationVFyyYbcfU_', symObjAddr: 0x5A8, symBinAddr: 0xF450, symSize: 0xE0 }
  - { offset: 0xAA7C4, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC16detachFromEngine3forySo07FlutterD9Registrar_p_tF', symObjAddr: 0x4D4, symBinAddr: 0xF37C, symSize: 0x4C }
  - { offset: 0xAA80F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC16detachFromEngine3forySo07FlutterD9Registrar_p_tFTo', symObjAddr: 0x520, symBinAddr: 0xF3C8, symSize: 0x88 }
  - { offset: 0xAA899, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC21sendDeviceOrientationyySo08UIDeviceG0VF', symObjAddr: 0x688, symBinAddr: 0xF530, symSize: 0x21C }
  - { offset: 0xAA8EF, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC21sendDeviceOrientationyySo08UIDeviceG0VFyyScMYccfU_', symObjAddr: 0x8D0, symBinAddr: 0xF778, symSize: 0xEC }
  - { offset: 0xAA926, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC21sendDeviceOrientationyySo08UIDeviceG0VFyyScMYccfU_ySo12FlutterErrorCSgcfU_', symObjAddr: 0x9BC, symBinAddr: 0xF864, symSize: 0x4 }
  - { offset: 0xAA950, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCACycfC', symObjAddr: 0x9C0, symBinAddr: 0xF868, symSize: 0x20 }
  - { offset: 0xAA964, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCACycfc', symObjAddr: 0x9E0, symBinAddr: 0xF888, symSize: 0x2C }
  - { offset: 0xAA9BD, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCACycfcTo', symObjAddr: 0xA0C, symBinAddr: 0xF8B4, symSize: 0x2C }
  - { offset: 0xAAA1C, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginCfD', symObjAddr: 0xA38, symBinAddr: 0xF8E0, symSize: 0x34 }
  - { offset: 0xAADEA, size: 0x8, addend: 0x0, symName: '_$sSo19FLTCamConfigurationC13mediaSettings0cD7Wrapper20captureDeviceFactory012audioCapturegH00f7SessionH00fK5Queue0fg5InputH017initialCameraNameABSo016FCPPlatformMediaD0C_So0arD9AVWrapperCSo010FLTCaptureG0_pSScSoAO_pycSo0tK0_pycSo17OS_dispatch_queueCSo0tgmH0_pSStcfcTO', symObjAddr: 0x4C28, symBinAddr: 0x13AD0, symSize: 0x1A4 }
  - { offset: 0xAADFE, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8registry9messenger9globalAPI16deviceDiscoverer17permissionManager0I7Factory014captureSessionM00n11DeviceInputM00nO5QueueACSo22FlutterTextureRegistry_p_So0S15BinaryMessenger_pSo23FCPCameraGlobalEventApiCSo09FLTCameraP11Discovering_pSo019FLTCameraPermissionL0CSo010FLTCaptureP0_pSScSo010FLTCaptureO0_pycSo010FLTCapturepqM0_pSo17OS_dispatch_queueCtcfcTf4nnnennnenn_nSo017FLTDefaultCapturepqM0C_So010FLTDefaultcpJ0CTg5Tf4gggggggggn_n', symObjAddr: 0x4E68, symBinAddr: 0x13D10, symSize: 0x358 }
  - { offset: 0xAAF45, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC8register4withySo07FlutterD9Registrar_p_tFZTf4nd_n', symObjAddr: 0x51C0, symBinAddr: 0x14068, symSize: 0x360 }
  - { offset: 0xAB117, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation12CameraPluginC23flutterErrorFromNSError33_237EF82A591AD84AAC1050734DCB1252LLySo07FlutterF0CSo0H0CFZTf4nd_n', symObjAddr: 0x5A4C, symBinAddr: 0x148F4, symSize: 0x1F8 }
  - { offset: 0xAB301, size: 0x8, addend: 0x0, symName: '_$sSo12FlutterErrorCSgIegg_ACIeyBy_TR', symObjAddr: 0x0, symBinAddr: 0x14FCC, symSize: 0x50 }
  - { offset: 0xABA15, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x988, symBinAddr: 0x15928, symSize: 0x20 }
  - { offset: 0xABD79, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0x6B84, symBinAddr: 0x1BB20, symSize: 0xD8 }
  - { offset: 0xABEC5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCfETo', symObjAddr: 0x763C, symBinAddr: 0x1C5D4, symSize: 0x48 }
  - { offset: 0xABEF5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCMa', symObjAddr: 0x7A10, symBinAddr: 0x1C9A8, symSize: 0x20 }
  - { offset: 0xABF09, size: 0x8, addend: 0x0, symName: '_$sSSSgs5Error_pSgIeggg_So8NSStringCSgSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x7AB4, symBinAddr: 0x1C9C8, symSize: 0x88 }
  - { offset: 0xABF2C, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo28FCPPlatformCameraDescriptionC_Ttg5', symObjAddr: 0x7B3C, symBinAddr: 0x1CA50, symSize: 0x64 }
  - { offset: 0xABFBC, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo28FCPPlatformCameraDescriptionC_Tg5', symObjAddr: 0x7BA0, symBinAddr: 0x1CAB4, symSize: 0x130 }
  - { offset: 0xAC169, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo19AVCaptureDeviceTypea_Tg5', symObjAddr: 0x7CD0, symBinAddr: 0x1CBE4, symSize: 0x124 }
  - { offset: 0xAC2ED, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSDySSypG_Tg5', symObjAddr: 0x7DF4, symBinAddr: 0x1CD08, symSize: 0x128 }
  - { offset: 0xAC419, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo28FCPPlatformCameraDescriptionC_Tt1g5', symObjAddr: 0x7F1C, symBinAddr: 0x1CE30, symSize: 0x84 }
  - { offset: 0xAC4AE, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo28FCPPlatformCameraDescriptionC_Tg5', symObjAddr: 0x7FA0, symBinAddr: 0x1CEB4, symSize: 0x118 }
  - { offset: 0xAC599, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x80B8, symBinAddr: 0x1CFCC, symSize: 0x64 }
  - { offset: 0xAC5C6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x811C, symBinAddr: 0x1D030, symSize: 0x68 }
  - { offset: 0xAC63F, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x8184, symBinAddr: 0x1D098, symSize: 0xE0 }
  - { offset: 0xAC6C2, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo16FLTCaptureDevice_p_Tg5', symObjAddr: 0x8264, symBinAddr: 0x1D178, symSize: 0x1B4 }
  - { offset: 0xAC757, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo8NSNumberC_Tg5', symObjAddr: 0x85E4, symBinAddr: 0x1D4F8, symSize: 0x1D4 }
  - { offset: 0xAC7D8, size: 0x8, addend: 0x0, symName: '_$sSh8containsySbxFSo22AVAudioSessionCategorya_Tg5', symObjAddr: 0x87B8, symBinAddr: 0x1D6CC, symSize: 0x1C8 }
  - { offset: 0xAC939, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSS_ypTg5', symObjAddr: 0x8980, symBinAddr: 0x1D894, symSize: 0xD0 }
  - { offset: 0xAC97D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x8A50, symBinAddr: 0x1D964, symSize: 0x1F4 }
  - { offset: 0xACA46, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0x8C44, symBinAddr: 0x1DB58, symSize: 0x328 }
  - { offset: 0xACBBD, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfCSo22AVAudioSessionCategorya_Tt0g5Tf4g_n', symObjAddr: 0x9610, symBinAddr: 0x1E32C, symSize: 0x240 }
  - { offset: 0xACE15, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x9C8C, symBinAddr: 0x1E9A8, symSize: 0xA4 }
  - { offset: 0xACE87, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x9D30, symBinAddr: 0x1EA4C, symSize: 0x78 }
  - { offset: 0xACEB0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x9DA8, symBinAddr: 0x1EAC4, symSize: 0x80 }
  - { offset: 0xACF10, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x9E28, symBinAddr: 0x1EB44, symSize: 0x64 }
  - { offset: 0xACF28, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x9E28, symBinAddr: 0x1EB44, symSize: 0x64 }
  - { offset: 0xACF8B, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC15copyPixelBuffers9UnmanagedVySo11CVBufferRefaGSgyFyyXEfU_TA', symObjAddr: 0xB414, symBinAddr: 0x20130, symSize: 0x8 }
  - { offset: 0xACFAA, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0xB41C, symBinAddr: 0x20138, symSize: 0x20 }
  - { offset: 0xACFD3, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xB43C, symBinAddr: 0x20158, symSize: 0x10 }
  - { offset: 0xACFE7, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xB44C, symBinAddr: 0x20168, symSize: 0x8 }
  - { offset: 0xACFFB, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0xB454, symBinAddr: 0x20170, symSize: 0x10 }
  - { offset: 0xAD00F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC18reportErrorMessage33_3F3F3493BA19BB550E3AD779576E7543LLyySSFyycfU_TA', symObjAddr: 0xB488, symBinAddr: 0x201A4, symSize: 0xC }
  - { offset: 0xAD023, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureOutput_03didF04fromySo09AVCaptureF0C_So17CMSampleBufferRefaSo0I10ConnectionCtFyyXEfU_TA', symObjAddr: 0xB4C0, symBinAddr: 0x201DC, symSize: 0x34 }
  - { offset: 0xAD061, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0xB518, symBinAddr: 0x20234, symSize: 0x8 }
  - { offset: 0xAD075, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0xB520, symBinAddr: 0x2023C, symSize: 0x40 }
  - { offset: 0xAD089, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0xB560, symBinAddr: 0x2027C, symSize: 0x40 }
  - { offset: 0xAD09D, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0xB5A0, symBinAddr: 0x202BC, symSize: 0x10 }
  - { offset: 0xAD0B1, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC27handleSampleBufferStreaming33_3F3F3493BA19BB550E3AD779576E7543LLyySo08CMSampleG3RefaFyyScMYccfU_TA', symObjAddr: 0xB5DC, symBinAddr: 0x202F8, symSize: 0xC }
  - { offset: 0xAD0C5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC18stopVideoRecording10completionyySSSg_So12FlutterErrorCSgtc_tFyycfU_TA', symObjAddr: 0xB6F4, symBinAddr: 0x20384, symSize: 0xC }
  - { offset: 0xAD0D9, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC19startVideoRecording10completion21messengerForStreamingyySo12FlutterErrorCSgc_So0L15BinaryMessenger_pSgtFyAIcfU_TA', symObjAddr: 0xB700, symBinAddr: 0x20390, symSize: 0xC }
  - { offset: 0xAD0ED, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC16startImageStream4with05imageG7Handler10completionySo22FlutterBinaryMessenger_p_So08FLTImagegJ0CySo0L5ErrorCSgctFyycfU_TA', symObjAddr: 0xB70C, symBinAddr: 0x2039C, symSize: 0xC }
  - { offset: 0xAD101, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC16startImageStream4with05imageG7Handler10completionySo22FlutterBinaryMessenger_p_So08FLTImagegJ0CySo0L5ErrorCSgctFyycfU_yyYbcfU_TA', symObjAddr: 0xB718, symBinAddr: 0x203A8, symSize: 0xC }
  - { offset: 0xAD115, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0xB768, symBinAddr: 0x203B4, symSize: 0x3C }
  - { offset: 0xAD129, size: 0x8, addend: 0x0, symName: '_$sSh8IteratorV8_VariantOySo22AVAudioSessionCategorya__GWOe', symObjAddr: 0xB7C0, symBinAddr: 0x2040C, symSize: 0x8 }
  - { offset: 0xAD13D, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureToFile10completionyySSSg_So12FlutterErrorCSgtc_tFyAF_s0J0_pSgtcfU_TA', symObjAddr: 0xB82C, symBinAddr: 0x20478, symSize: 0xC }
  - { offset: 0xAD151, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureToFile10completionyySSSg_So12FlutterErrorCSgtc_tFyAF_s0J0_pSgtcfU_yyYbcfU_TA', symObjAddr: 0xB864, symBinAddr: 0x204B0, symSize: 0x8 }
  - { offset: 0xAD165, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC25reportInitializationStateyyFyycfU_TA', symObjAddr: 0xB8D0, symBinAddr: 0x2051C, symSize: 0x8 }
  - { offset: 0xAD1CC, size: 0x8, addend: 0x0, symName: '_$sSh10isDisjoint4withSbShyxG_tFSo22AVAudioSessionCategorya_Tg5', symObjAddr: 0x9A8, symBinAddr: 0x15948, symSize: 0x204 }
  - { offset: 0xAD34C, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo16AVVideoCodecTypeaG_Tg5', symObjAddr: 0x2FA4, symBinAddr: 0x17F40, symSize: 0x154 }
  - { offset: 0xAD544, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo8NSNumberCG_Tg5', symObjAddr: 0x30F8, symBinAddr: 0x18094, symSize: 0x18C }
  - { offset: 0xAD8AA, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTt0g5Tf4g_n', symObjAddr: 0x8F6C, symBinAddr: 0x1DE80, symSize: 0x114 }
  - { offset: 0xADCCA, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC11videoFormats6UInt32VvgTo', symObjAddr: 0x7C, symBinAddr: 0x1501C, symSize: 0x3C }
  - { offset: 0xADD28, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC11videoFormats6UInt32VvsTo', symObjAddr: 0xB8, symBinAddr: 0x15058, symSize: 0x68 }
  - { offset: 0xADD86, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC11videoFormats6UInt32VvW', symObjAddr: 0x120, symBinAddr: 0x150C0, symSize: 0x13C }
  - { offset: 0xADE99, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC17deviceOrientationSo08UIDeviceF0VvgTo', symObjAddr: 0x25C, symBinAddr: 0x151FC, symSize: 0x3C }
  - { offset: 0xADEF1, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC17deviceOrientationSo08UIDeviceF0VvsTo', symObjAddr: 0x298, symBinAddr: 0x15238, symSize: 0x94 }
  - { offset: 0xAE0F6, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC35setUpCaptureSessionForAudioIfNeededyyF', symObjAddr: 0x32C, symBinAddr: 0x152CC, symSize: 0x624 }
  - { offset: 0xAE24B, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC35setUpCaptureSessionForAudioIfNeededyyFyycfU_', symObjAddr: 0x950, symBinAddr: 0x158F0, symSize: 0x38 }
  - { offset: 0xAE311, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC25reportInitializationStateyyF', symObjAddr: 0xBAC, symBinAddr: 0x15B4C, symSize: 0x204 }
  - { offset: 0xAE34C, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC25reportInitializationStateyyFyycfU_', symObjAddr: 0xDB0, symBinAddr: 0x15D50, symSize: 0xE4 }
  - { offset: 0xAE7D5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC19startVideoRecording10completion21messengerForStreamingyySo12FlutterErrorCSgc_So0L15BinaryMessenger_pSgtF', symObjAddr: 0xE98, symBinAddr: 0x15E34, symSize: 0x4D4 }
  - { offset: 0xAE9ED, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC19startVideoRecording10completion21messengerForStreamingyySo12FlutterErrorCSgc_So0L15BinaryMessenger_pSgtFyAIcfU_', symObjAddr: 0x136C, symBinAddr: 0x16308, symSize: 0x70 }
  - { offset: 0xAEB2F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC19setUpVideoRecording33_3F3F3493BA19BB550E3AD779576E7543LL10completionyySo12FlutterErrorCSgc_tF', symObjAddr: 0x13DC, symBinAddr: 0x16378, symSize: 0x2D4 }
  - { offset: 0xAEC41, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC11setupWriter33_3F3F3493BA19BB550E3AD779576E7543LL7forPathSbSS_tF', symObjAddr: 0x16B0, symBinAddr: 0x1664C, symSize: 0xDE8 }
  - { offset: 0xAF1BB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC18stopVideoRecording10completionyySSSg_So12FlutterErrorCSgtc_tF', symObjAddr: 0x2498, symBinAddr: 0x17434, symSize: 0x2F8 }
  - { offset: 0xAF2F9, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC18stopVideoRecording10completionyySSSg_So12FlutterErrorCSgtc_tFyycfU_', symObjAddr: 0x2790, symBinAddr: 0x1772C, symSize: 0x254 }
  - { offset: 0xAF3B7, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureToFile10completionyySSSg_So12FlutterErrorCSgtc_tF', symObjAddr: 0x29E4, symBinAddr: 0x17980, symSize: 0x5C0 }
  - { offset: 0xAF5BB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureToFile10completionyySSSg_So12FlutterErrorCSgtc_tFyAF_s0J0_pSgtcfU_', symObjAddr: 0x3284, symBinAddr: 0x18220, symSize: 0x304 }
  - { offset: 0xAF67F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureToFile10completionyySSSg_So12FlutterErrorCSgtc_tFyAF_s0J0_pSgtcfU_yyYbcfU_', symObjAddr: 0x3588, symBinAddr: 0x18524, symSize: 0xCC }
  - { offset: 0xAF759, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC17applyExposureMode33_3F3F3493BA19BB550E3AD779576E7543LLyyF', symObjAddr: 0x3654, symBinAddr: 0x185F0, symSize: 0x1AC }
  - { offset: 0xAF77F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC17setExposureOffsetyySdF', symObjAddr: 0x3800, symBinAddr: 0x1879C, symSize: 0x140 }
  - { offset: 0xAF80B, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC16setExposurePoint_14withCompletionySo011FCPPlatformG0CSg_ySo12FlutterErrorCSgctF', symObjAddr: 0x3940, symBinAddr: 0x188DC, symSize: 0x30C }
  - { offset: 0xAF8DF, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13setFocusPoint_10completionySo011FCPPlatformG0CSg_ySo12FlutterErrorCSgctF', symObjAddr: 0x3C4C, symBinAddr: 0x18BE8, symSize: 0x338 }
  - { offset: 0xAF9D1, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC12setZoomLevel_14withCompletiony12CoreGraphics7CGFloatV_ySo12FlutterErrorCSgctF', symObjAddr: 0x3F84, symBinAddr: 0x18F20, symSize: 0x3F8 }
  - { offset: 0xAFB42, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC12setFlashMode_14withCompletionySo011FCPPlatformfG0V_ySo12FlutterErrorCSgctF', symObjAddr: 0x437C, symBinAddr: 0x19318, symSize: 0x5F4 }
  - { offset: 0xAFCAB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC28setDescriptionWhileRecording_14withCompletionySS_ySo12FlutterErrorCSgctF', symObjAddr: 0x4970, symBinAddr: 0x1990C, symSize: 0xBD8 }
  - { offset: 0xAFE7E, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC16startImageStream4with05imageG7Handler10completionySo22FlutterBinaryMessenger_p_So08FLTImagegJ0CySo0L5ErrorCSgctF', symObjAddr: 0x5548, symBinAddr: 0x1A4E4, symSize: 0x27C }
  - { offset: 0xAFF72, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC15stopImageStreamyyF', symObjAddr: 0x5AEC, symBinAddr: 0x1AA88, symSize: 0x128 }
  - { offset: 0xB006D, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC27handleSampleBufferStreaming33_3F3F3493BA19BB550E3AD779576E7543LLyySo08CMSampleG3RefaF', symObjAddr: 0x5C14, symBinAddr: 0x1ABB0, symSize: 0xBD4 }
  - { offset: 0xB06AB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC27handleSampleBufferStreaming33_3F3F3493BA19BB550E3AD779576E7543LLyySo08CMSampleG3RefaFyyScMYccfU_', symObjAddr: 0x6C5C, symBinAddr: 0x1BBF8, symSize: 0x68 }
  - { offset: 0xB06F1, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC14newAudioSample33_3F3F3493BA19BB550E3AD779576E7543LLyySo17CMSampleBufferRefaF', symObjAddr: 0x67E8, symBinAddr: 0x1B784, symSize: 0x310 }
  - { offset: 0xB07B5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureOutput_03didF04fromySo09AVCaptureF0C_So17CMSampleBufferRefaSo0I10ConnectionCtFTo', symObjAddr: 0x6AF8, symBinAddr: 0x1BA94, symSize: 0x8C }
  - { offset: 0xB080F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC5closeyyF', symObjAddr: 0x6CC4, symBinAddr: 0x1BC60, symSize: 0x5B8 }
  - { offset: 0xB0D94, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC15copyPixelBuffers9UnmanagedVySo11CVBufferRefaGSgyF', symObjAddr: 0x727C, symBinAddr: 0x1C218, symSize: 0x140 }
  - { offset: 0xB0DE7, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC15copyPixelBuffers9UnmanagedVySo11CVBufferRefaGSgyFyyXEfU_', symObjAddr: 0x73BC, symBinAddr: 0x1C358, symSize: 0x50 }
  - { offset: 0xB0E26, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC15copyPixelBuffers9UnmanagedVySo11CVBufferRefaGSgyFTo', symObjAddr: 0x740C, symBinAddr: 0x1C3A8, symSize: 0x34 }
  - { offset: 0xB0E40, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC18reportErrorMessage33_3F3F3493BA19BB550E3AD779576E7543LLyySSFyycfU_', symObjAddr: 0x7440, symBinAddr: 0x1C3DC, symSize: 0x100 }
  - { offset: 0xB0E73, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCfD', symObjAddr: 0x7544, symBinAddr: 0x1C4DC, symSize: 0x78 }
  - { offset: 0xB0EA7, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCfDTo', symObjAddr: 0x75BC, symBinAddr: 0x1C554, symSize: 0x80 }
  - { offset: 0xB0ED9, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13configuration5errorACSo19FLTCamConfigurationC_SAySo7NSErrorCSgGSgtcfcTo', symObjAddr: 0x7684, symBinAddr: 0x1C61C, symSize: 0x4C }
  - { offset: 0xB0F03, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCACycfc', symObjAddr: 0x76D0, symBinAddr: 0x1C668, symSize: 0x320 }
  - { offset: 0xB0FB0, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraCACycfcTo', symObjAddr: 0x79F0, symBinAddr: 0x1C988, symSize: 0x20 }
  - { offset: 0xB0FF6, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC20getTemporaryFilePath33_3F3F3493BA19BB550E3AD779576E7543LL13withExtension9subfolder6prefixS2S_S2StKFTf4nnnd_n', symObjAddr: 0x9080, symBinAddr: 0x1DF94, symSize: 0x398 }
  - { offset: 0xB1114, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC27upgradeAudioSessionCategory33_3F3F3493BA19BB550E3AD779576E7543LL09requestedH07optionsySo07AVAudiogH0a_So0rgH7OptionsVtFZTf4nnd_n', symObjAddr: 0x9850, symBinAddr: 0x1E56C, symSize: 0x43C }
  - { offset: 0xB14F2, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC7cgPoint33_3F3F3493BA19BB550E3AD779576E7543LL3for15withOrientationSo7CGPointVSo011FCPPlatformF0C_So08UIDeviceP0VtFTf4nnd_n', symObjAddr: 0x9E8C, symBinAddr: 0x1EBA8, symSize: 0xE4 }
  - { offset: 0xB1561, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC14applyFocusMode33_3F3F3493BA19BB550E3AD779576E7543LL_8onDeviceySo011FCPPlatformfG0V_So010FLTCaptureP0_ptFTf4nnd_n', symObjAddr: 0x9F70, symBinAddr: 0x1EC8C, symSize: 0x140 }
  - { offset: 0xB15D4, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC16createConnection33_3F3F3493BA19BB550E3AD779576E7543LL13captureDevice11videoFormat0nO12InputFactorySo010FLTCaptureR0_p_So0T15VideoDataOutput_pSo09AVCaptureF0CtSo0tO0_p_s6UInt32VSo0torS0_ptKFZTf4nnnd_n', symObjAddr: 0xA0B0, symBinAddr: 0x1EDCC, symSize: 0x2F0 }
  - { offset: 0xB178A, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC32copySampleBufferWithAdjustedTime33_3F3F3493BA19BB550E3AD779576E7543LL_2bySo08CMSampleG3RefaSgAH_So6CMTimeatFTf4nnd_n', symObjAddr: 0xA3A0, symBinAddr: 0x1F0BC, symSize: 0x1C4 }
  - { offset: 0xB18EB, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13captureOutput_03didF04fromySo09AVCaptureF0C_So17CMSampleBufferRefaSo0I10ConnectionCtFTf4nndn_n', symObjAddr: 0xA564, symBinAddr: 0x1F280, symSize: 0xB68 }
  - { offset: 0xB1AE5, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation13DefaultCameraC13configuration5errorACSo19FLTCamConfigurationC_SAySo7NSErrorCSgGSgtcfcTf4gnn_n', symObjAddr: 0xB0CC, symBinAddr: 0x1FDE8, symSize: 0x324 }
  - { offset: 0xB1E7F, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation30captureSessionQueueSpecificKey_WZ', symObjAddr: 0x0, symBinAddr: 0x2061C, symSize: 0x40 }
  - { offset: 0xB1EA3, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation30captureSessionQueueSpecificKey8Dispatch0hfG0CySSGvp', symObjAddr: 0x17C0, symBinAddr: 0x44E58, symSize: 0x0 }
  - { offset: 0xB1EB1, size: 0x8, addend: 0x0, symName: '_$s19camera_avfoundation30captureSessionQueueSpecificKey_WZ', symObjAddr: 0x0, symBinAddr: 0x2061C, symSize: 0x40 }
...
