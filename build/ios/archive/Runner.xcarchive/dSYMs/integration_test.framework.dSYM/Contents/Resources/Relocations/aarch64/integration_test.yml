---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ctnlridqzlszcscjliwgbinamljf/Build/Intermediates.noindex/ArchiveIntermediates/Runner/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/integration_test.framework/integration_test'
relocations:
  - { offset: 0x5DE23, size: 0x8, addend: 0x0, symName: _integration_testVersionString, symObjAddr: 0x0, symBinAddr: 0x5480, symSize: 0x0 }
  - { offset: 0x5DE58, size: 0x8, addend: 0x0, symName: _integration_testVersionNumber, symObjAddr: 0x30, symBinAddr: 0x54B0, symSize: 0x0 }
  - { offset: 0x5DE95, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner init]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x68 }
  - { offset: 0x5DF0F, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner init]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x68 }
  - { offset: 0x5DF46, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner testIntegrationTestWithResults:]', symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x18C }
  - { offset: 0x5DFA9, size: 0x8, addend: 0x0, symName: '___59-[FLTIntegrationTestRunner testIntegrationTestWithResults:]_block_invoke', symObjAddr: 0x1F4, symBinAddr: 0x41F4, symSize: 0x148 }
  - { offset: 0x5E102, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b, symObjAddr: 0x33C, symBinAddr: 0x433C, symSize: 0x3C }
  - { offset: 0x5E12B, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x378, symBinAddr: 0x4378, symSize: 0x30 }
  - { offset: 0x5E14A, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner capturedScreenshotsByName]', symObjAddr: 0x3A8, symBinAddr: 0x43A8, symSize: 0x44 }
  - { offset: 0x5E181, size: 0x8, addend: 0x0, symName: '+[FLTIntegrationTestRunner testCaseNameFromDartTestName:]', symObjAddr: 0x3EC, symBinAddr: 0x43EC, symSize: 0xE4 }
  - { offset: 0x5E1F4, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner integrationTestPlugin]', symObjAddr: 0x4D0, symBinAddr: 0x44D0, symSize: 0xC }
  - { offset: 0x5E22B, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner setIntegrationTestPlugin:]', symObjAddr: 0x4DC, symBinAddr: 0x44DC, symSize: 0x8 }
  - { offset: 0x5E26A, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner .cxx_destruct]', symObjAddr: 0x4E4, symBinAddr: 0x44E4, symSize: 0xC }
  - { offset: 0x5E4A5, size: 0x8, addend: 0x0, symName: '-[IntegrationTestIosTest testIntegrationTest:]', symObjAddr: 0x0, symBinAddr: 0x44F0, symSize: 0x194 }
  - { offset: 0x5E4C5, size: 0x8, addend: 0x0, symName: '-[IntegrationTestIosTest testIntegrationTest:]', symObjAddr: 0x0, symBinAddr: 0x44F0, symSize: 0x194 }
  - { offset: 0x5E565, size: 0x8, addend: 0x0, symName: '___46-[IntegrationTestIosTest testIntegrationTest:]_block_invoke', symObjAddr: 0x194, symBinAddr: 0x4684, symSize: 0xA0 }
  - { offset: 0x5E657, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x234, symBinAddr: 0x4724, symSize: 0x28 }
  - { offset: 0x5E680, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x25C, symBinAddr: 0x474C, symSize: 0x28 }
  - { offset: 0x5E7F8, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance]', symObjAddr: 0x0, symBinAddr: 0x4774, symSize: 0x30 }
  - { offset: 0x5E80B, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance]', symObjAddr: 0x0, symBinAddr: 0x4774, symSize: 0x30 }
  - { offset: 0x5E835, size: 0x8, addend: 0x0, symName: _instance.onceToken, symObjAddr: 0x5430, symBinAddr: 0x9480, symSize: 0x0 }
  - { offset: 0x5E84B, size: 0x8, addend: 0x0, symName: _instance.sInstance, symObjAddr: 0x5438, symBinAddr: 0x9488, symSize: 0x0 }
  - { offset: 0x5E91C, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance].cold.1', symObjAddr: 0x5A8, symBinAddr: 0x4D1C, symSize: 0x14 }
  - { offset: 0x5E938, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance].cold.1', symObjAddr: 0x5A8, symBinAddr: 0x4D1C, symSize: 0x14 }
  - { offset: 0x5E94A, size: 0x8, addend: 0x0, symName: '___33+[IntegrationTestPlugin instance]_block_invoke', symObjAddr: 0x30, symBinAddr: 0x47A4, symSize: 0x30 }
  - { offset: 0x5E971, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin initForRegistration]', symObjAddr: 0x60, symBinAddr: 0x47D4, symSize: 0x4 }
  - { offset: 0x5E9A6, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin init]', symObjAddr: 0x64, symBinAddr: 0x47D8, symSize: 0x60 }
  - { offset: 0x5E9DD, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin registerWithRegistrar:]', symObjAddr: 0xC4, symBinAddr: 0x4838, symSize: 0xA8 }
  - { offset: 0x5EA30, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin handleMethodCall:result:]', symObjAddr: 0x16C, symBinAddr: 0x48E0, symSize: 0x240 }
  - { offset: 0x5EB1C, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin capturePngScreenshot]', symObjAddr: 0x3AC, symBinAddr: 0x4B20, symSize: 0x184 }
  - { offset: 0x5EB8F, size: 0x8, addend: 0x0, symName: '___45-[IntegrationTestPlugin capturePngScreenshot]_block_invoke', symObjAddr: 0x530, symBinAddr: 0x4CA4, symSize: 0x18 }
  - { offset: 0x5EBE6, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x548, symBinAddr: 0x4CBC, symSize: 0x8 }
  - { offset: 0x5EC0D, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x550, symBinAddr: 0x4CC4, symSize: 0x8 }
  - { offset: 0x5EC2C, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin testResults]', symObjAddr: 0x558, symBinAddr: 0x4CCC, symSize: 0x8 }
  - { offset: 0x5EC63, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin setTestResults:]', symObjAddr: 0x560, symBinAddr: 0x4CD4, symSize: 0xC }
  - { offset: 0x5ECA4, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin capturedScreenshotsByName]', symObjAddr: 0x56C, symBinAddr: 0x4CE0, symSize: 0xC }
  - { offset: 0x5ECDB, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin .cxx_destruct]', symObjAddr: 0x578, symBinAddr: 0x4CEC, symSize: 0x30 }
...
