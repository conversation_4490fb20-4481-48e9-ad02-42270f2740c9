{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "integration_test", "path": "/Users/<USER>/fvm/versions/3.29.0/packages/integration_test/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android_camerax", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.19+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.30/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "integration_test", "path": "/Users/<USER>/fvm/versions/3.29.0/packages/integration_test/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.18/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.13/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": [], "dev_dependency": false}, {"name": "video_player_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android_camerax", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android_camerax", "dependencies": []}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "image_gallery_saver", "dependencies": []}, {"name": "integration_test", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}], "date_created": "2025-08-28 16:32:28.055631", "version": "3.29.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}