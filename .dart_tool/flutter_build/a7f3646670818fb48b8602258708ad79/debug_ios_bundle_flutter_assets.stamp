{"inputs": ["/Users/<USER>/fvm/versions/3.29.0/bin/internal/engine.version", "/Users/<USER>/fvm/versions/3.29.0/bin/internal/engine.version", "/Users/<USER>/Documents/manish/camera/.dart_tool/flutter_build/a7f3646670818fb48b8602258708ad79/app.dill", "/Users/<USER>/Documents/manish/camera/.dart_tool/flutter_build/a7f3646670818fb48b8602258708ad79/App.framework/App", "/Users/<USER>/Documents/manish/camera/pubspec.yaml", "/Users/<USER>/fvm/versions/3.29.0/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/fvm/versions/3.29.0/bin/internal/engine.version", "/Users/<USER>/fvm/versions/3.29.0/bin/internal/engine.version", "/Users/<USER>/fvm/versions/3.29.0/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "/Users/<USER>/fvm/versions/3.29.0/bin/internal/engine.version", "/Users/<USER>/Documents/manish/camera/pubspec.yaml", "/Users/<USER>/Documents/manish/camera/ios/Runner/Info.plist", "/Users/<USER>/Documents/manish/camera/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/fvm/versions/3.29.0/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/fvm/versions/3.29.0/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/manish/camera/.dart_tool/flutter_build/a7f3646670818fb48b8602258708ad79/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-88.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-8.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.11.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.19+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.30/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.13+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.18/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/process-5.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sync_http-0.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webdriver-3.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/Users/<USER>/fvm/versions/3.29.0/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/fvm/versions/3.29.0/packages/flutter/LICENSE"], "outputs": ["/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/App", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/Info.plist", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/Documents/manish/camera/build/ios/Debug-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json"]}